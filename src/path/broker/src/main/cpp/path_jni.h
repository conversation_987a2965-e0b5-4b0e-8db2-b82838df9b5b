/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_aurora_path_jni_PathJNI */

#ifndef _Included_com_aurora_path_jni_PathJNI
#define _Included_com_aurora_path_jni_PathJNI
#ifdef __cplusplus
extern "C" {
#endif

/*
 * Class:     com_aurora_path_jni_PathJNI
 * Method:    initPathEngine
 * Signature: (Ljava/lang/String;Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_aurora_path_jni_PathJNI_initPathEngine
  (JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_aurora_path_jni_PathJNI
 * Method:    startPathEngine
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_aurora_path_jni_PathJNI_startPathEngine
  (JNIEnv *, jobject);

/*
 * Class:     com_aurora_path_jni_PathJNI
 * Method:    stopPathEngine
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_aurora_path_jni_PathJNI_stopPathEngine
  (JNIEnv *, jobject);

/*
 * Class:     com_aurora_path_jni_PathJNI
 * Method:    uninitPathEngine
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_aurora_path_jni_PathJNI_uninitPathEngine
  (JNIEnv *, jobject);

/*
 * Class:     com_aurora_path_jni_PathJNI
 * Method:    requestPath
 * Signature: ([B)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_aurora_path_jni_PathJNI_requestPath
  (JNIEnv *, jobject, jbyteArray);

/*
 * Class:     com_aurora_path_jni_PathJNI
 * Method:    cancelRequest
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_aurora_path_jni_PathJNI_cancelRequest
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_aurora_path_jni_PathJNI
 * Method:    isInitialized
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_aurora_path_jni_PathJNI_isInitialized
  (JNIEnv *, jobject);

/*
 * Class:     com_aurora_path_jni_PathJNI
 * Method:    getVersion
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_aurora_path_jni_PathJNI_getVersion
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
