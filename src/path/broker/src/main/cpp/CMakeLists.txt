cmake_minimum_required(VERSION 3.16)
project(path_jni)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(<PERSON>NI REQUIRED)
find_package(Protobuf REQUIRED)
find_package(PkgConfig REQUIRED)

# Find Java
if(NOT DEFINED ENV{JAVA_HOME})
    message(FATAL_ERROR "JAVA_HOME environment variable is not set")
endif()

set(JAVA_HOME $ENV{JAVA_HOME})
set(JAVA_INCLUDE_PATH ${JAVA_HOME}/include)

# Platform-specific Java include paths
if(WIN32)
    set(JAV<PERSON>_INCLUDE_PATH2 ${JAVA_HOME}/include/win32)
elseif(APPLE)
    set(JAVA_INCLUDE_PATH2 ${JAVA_HOME}/include/darwin)
else()
    set(J<PERSON><PERSON>_INCLUDE_PATH2 ${JAV<PERSON>_HOME}/include/linux)
endif()

# Aurora Path Engine paths
set(AURORA_PATH_ROOT ${CMAKE_CURRENT_SOURCE_DIR}/../../../..)
set(AURORA_PATH_INCLUDE ${AURORA_PATH_ROOT}/include)
set(AURORA_PATH_SRC ${AURORA_PATH_ROOT}/src)

# Include directories
include_directories(
    ${JNI_INCLUDE_DIRS}
    ${JAVA_INCLUDE_PATH}
    ${JAVA_INCLUDE_PATH2}
    ${AURORA_PATH_INCLUDE}
    ${AURORA_PATH_SRC}
    ${AURORA_PATH_SRC}/base
    ${AURORA_PATH_SRC}/config
    ${AURORA_PATH_SRC}/graph_reader
    ${AURORA_PATH_SRC}/route_algorithm
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${Protobuf_INCLUDE_DIRS}
)

# Find protobuf generated files
set(PROTO_GENERATED_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../proto-generated)
file(GLOB PROTO_SOURCES ${PROTO_GENERATED_DIR}/*.pb.cc)
file(GLOB PROTO_HEADERS ${PROTO_GENERATED_DIR}/*.pb.h)

include_directories(${PROTO_GENERATED_DIR})

# Aurora Path Engine source files
file(GLOB_RECURSE AURORA_PATH_SOURCES
    ${AURORA_PATH_SRC}/*.cpp
    ${AURORA_PATH_SRC}/*.cc
)

# Exclude test files and main.cpp
list(FILTER AURORA_PATH_SOURCES EXCLUDE REGEX ".*test.*")
list(FILTER AURORA_PATH_SOURCES EXCLUDE REGEX ".*main\\.cpp$")

# JNI source files
set(JNI_SOURCES
    path_jni.cpp
    ${PROTO_SOURCES}
)

# Create shared library
add_library(path_jni SHARED ${JNI_SOURCES} ${AURORA_PATH_SOURCES})

# Link libraries
target_link_libraries(path_jni
    ${JNI_LIBRARIES}
    ${Protobuf_LIBRARIES}
    pthread
    dl
)

# Find and link additional dependencies
pkg_check_modules(YAML_CPP REQUIRED yaml-cpp)
target_link_libraries(path_jni ${YAML_CPP_LIBRARIES})
target_include_directories(path_jni PRIVATE ${YAML_CPP_INCLUDE_DIRS})

# Find Boost
find_package(Boost REQUIRED COMPONENTS system filesystem thread)
target_link_libraries(path_jni ${Boost_LIBRARIES})
target_include_directories(path_jni PRIVATE ${Boost_INCLUDE_DIRS})

# Find spdlog
find_package(spdlog REQUIRED)
target_link_libraries(path_jni spdlog::spdlog)

# Compiler flags
target_compile_options(path_jni PRIVATE
    -fPIC
    -Wall
    -Wextra
    -O2
    -DNDEBUG
)

# Set output directory
set_target_properties(path_jni PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../../../target/native
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/../../../target/native
)

# Install target
install(TARGETS path_jni
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)
