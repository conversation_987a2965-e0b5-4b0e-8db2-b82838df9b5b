package com.aurora.path.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 路径规划请求DTO，参考高德地图API设计
 */
public class RouteRequest {
    
    /**
     * 起点坐标，格式：经度,纬度
     */
    @NotNull(message = "起点坐标不能为空")
    @JsonProperty("origin")
    private String origin;
    
    /**
     * 终点坐标，格式：经度,纬度
     */
    @NotNull(message = "终点坐标不能为空")
    @JsonProperty("destination")
    private String destination;
    
    /**
     * 途经点坐标列表，格式：经度,纬度|经度,纬度
     */
    @JsonProperty("waypoints")
    private String waypoints;
    
    /**
     * 路径规划策略
     * 0: 最短时间优先（默认）
     * 1: 最短距离优先
     * 2: 高速优先
     * 3: 避开收费路段
     */
    @JsonProperty("strategy")
    private Integer strategy = 0;
    
    /**
     * 算路模式
     * 0: 离线（默认）
     * 1: 在线
     * 2: 自动选择
     */
    @JsonProperty("mode")
    private Integer mode = 0;
    
    /**
     * 出发时间类型
     * 0: 无时间（默认）
     * 1: 当前时间
     * 2: 出发时间
     * 3: 到达时间
     */
    @JsonProperty("departure_time_type")
    private Integer departureTimeType = 0;
    
    /**
     * 出发时间，格式：YYYY-MM-DDThh:mm
     */
    @JsonProperty("departure_time")
    private String departureTime;
    
    /**
     * 返回格式
     * json: JSON格式（默认）
     * proto: Protobuf格式
     */
    @JsonProperty("output")
    private String output = "json";
    
    /**
     * 是否返回路径几何信息
     */
    @JsonProperty("geometry")
    private Boolean geometry = true;
    
    /**
     * 是否返回路段信息
     */
    @JsonProperty("steps")
    private Boolean steps = true;
    
    /**
     * 扩展信息
     * all: 返回全部扩展信息
     */
    @JsonProperty("extensions")
    private String extensions = "all";
    
    // Getters and Setters
    public String getOrigin() {
        return origin;
    }
    
    public void setOrigin(String origin) {
        this.origin = origin;
    }
    
    public String getDestination() {
        return destination;
    }
    
    public void setDestination(String destination) {
        this.destination = destination;
    }
    
    public String getWaypoints() {
        return waypoints;
    }
    
    public void setWaypoints(String waypoints) {
        this.waypoints = waypoints;
    }
    
    public Integer getStrategy() {
        return strategy;
    }
    
    public void setStrategy(Integer strategy) {
        this.strategy = strategy;
    }
    
    public Integer getMode() {
        return mode;
    }
    
    public void setMode(Integer mode) {
        this.mode = mode;
    }
    
    public Integer getDepartureTimeType() {
        return departureTimeType;
    }
    
    public void setDepartureTimeType(Integer departureTimeType) {
        this.departureTimeType = departureTimeType;
    }
    
    public String getDepartureTime() {
        return departureTime;
    }
    
    public void setDepartureTime(String departureTime) {
        this.departureTime = departureTime;
    }
    
    public String getOutput() {
        return output;
    }
    
    public void setOutput(String output) {
        this.output = output;
    }
    
    public Boolean getGeometry() {
        return geometry;
    }
    
    public void setGeometry(Boolean geometry) {
        this.geometry = geometry;
    }
    
    public Boolean getSteps() {
        return steps;
    }
    
    public void setSteps(Boolean steps) {
        this.steps = steps;
    }
    
    public String getExtensions() {
        return extensions;
    }
    
    public void setExtensions(String extensions) {
        this.extensions = extensions;
    }
    
    @Override
    public String toString() {
        return "RouteRequest{" +
                "origin='" + origin + '\'' +
                ", destination='" + destination + '\'' +
                ", waypoints='" + waypoints + '\'' +
                ", strategy=" + strategy +
                ", mode=" + mode +
                ", departureTimeType=" + departureTimeType +
                ", departureTime='" + departureTime + '\'' +
                ", output='" + output + '\'' +
                ", geometry=" + geometry +
                ", steps=" + steps +
                ", extensions='" + extensions + '\'' +
                '}';
    }
}
