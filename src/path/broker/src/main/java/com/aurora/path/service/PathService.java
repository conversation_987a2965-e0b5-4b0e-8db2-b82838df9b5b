package com.aurora.path.service;

import com.aurora.path.dto.RouteRequest;
import com.aurora.path.dto.RouteResponse;
import com.aurora.path.jni.PathJNI;
import com.aurora.path.proto.*;
import com.google.protobuf.InvalidProtocolBufferException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 路径规划服务
 */
@Service
public class PathService {
    
    private static final Logger logger = LoggerFactory.getLogger(PathService.class);
    
    @Autowired
    private PathJNI pathJNI;
    
    /**
     * 执行路径规划
     * @param request 路径规划请求
     * @return 路径规划结果
     */
    public RouteResponse calculateRoute(RouteRequest request) {
        try {
            // 转换请求为protobuf格式
            PathRequest protoRequest = convertToProtoRequest(request);
            
            // 序列化请求
            byte[] requestData = protoRequest.toByteArray();
            
            // 调用JNI接口
            byte[] responseData = pathJNI.requestPath(requestData);
            
            if (responseData == null) {
                return createErrorResponse("路径规划失败", "10001");
            }
            
            // 反序列化响应
            PathResponse protoResponse = PathResponse.parseFrom(responseData);
            
            // 转换为DTO格式
            return convertToRouteResponse(protoResponse, request);
            
        } catch (InvalidProtocolBufferException e) {
            logger.error("Failed to parse protobuf response", e);
            return createErrorResponse("数据解析失败", "10002");
        } catch (Exception e) {
            logger.error("Route calculation failed", e);
            return createErrorResponse("路径规划异常", "10003");
        }
    }
    
    /**
     * 获取protobuf格式的路径规划结果
     * @param request 路径规划请求
     * @return protobuf格式的响应数据
     */
    public byte[] calculateRouteProto(RouteRequest request) {
        try {
            // 转换请求为protobuf格式
            PathRequest protoRequest = convertToProtoRequest(request);
            
            // 序列化请求
            byte[] requestData = protoRequest.toByteArray();
            
            // 调用JNI接口
            return pathJNI.requestPath(requestData);
            
        } catch (Exception e) {
            logger.error("Route calculation failed", e);
            
            // 创建错误响应
            PathResponse.Builder errorResponse = PathResponse.newBuilder()
                    .setRequestId(UUID.randomUUID().toString())
                    .setSuccess(false)
                    .setErrorMessage("路径规划异常: " + e.getMessage());
            
            return errorResponse.build().toByteArray();
        }
    }
    
    /**
     * 转换RouteRequest为PathRequest
     */
    private PathRequest convertToProtoRequest(RouteRequest request) {
        PathRequest.Builder builder = PathRequest.newBuilder()
                .setRequestId(UUID.randomUUID().toString());
        
        PathQuery.Builder queryBuilder = PathQuery.newBuilder();
        
        // 设置策略
        queryBuilder.setStrategy(PathStrategy.forNumber(request.getStrategy()));
        
        // 设置模式
        queryBuilder.setMode(PathMode.forNumber(request.getMode()));
        
        // 设置时间选项
        DateTimeOption.Builder dateOptionBuilder = DateTimeOption.newBuilder()
                .setType(DateTimeType.forNumber(request.getDepartureTimeType()));
        
        if (request.getDepartureTime() != null) {
            dateOptionBuilder.setValue(request.getDepartureTime());
        }
        queryBuilder.setDateOption(dateOptionBuilder);
        
        // 设置路径点
        List<PathLandmark> pathPoints = new ArrayList<>();
        
        // 起点
        PathLandmark.Builder startBuilder = PathLandmark.newBuilder()
                .setValid(true)
                .setWaypointType(WayPointType.START_POINT)
                .setLandmarkType(LandmarkType.CLICK);
        
        String[] originCoords = request.getOrigin().split(",");
        if (originCoords.length == 2) {
            PointLL origin = PointLL.newBuilder()
                    .setLng(Double.parseDouble(originCoords[0]))
                    .setLat(Double.parseDouble(originCoords[1]))
                    .build();
            startBuilder.setPt(origin);
        }
        pathPoints.add(startBuilder.build());
        
        // 途经点
        if (request.getWaypoints() != null && !request.getWaypoints().isEmpty()) {
            String[] waypointPairs = request.getWaypoints().split("\\|");
            for (String waypointPair : waypointPairs) {
                String[] coords = waypointPair.split(",");
                if (coords.length == 2) {
                    PathLandmark.Builder waypointBuilder = PathLandmark.newBuilder()
                            .setValid(true)
                            .setWaypointType(WayPointType.VIA_POINT)
                            .setLandmarkType(LandmarkType.CLICK);
                    
                    PointLL waypoint = PointLL.newBuilder()
                            .setLng(Double.parseDouble(coords[0]))
                            .setLat(Double.parseDouble(coords[1]))
                            .build();
                    waypointBuilder.setPt(waypoint);
                    pathPoints.add(waypointBuilder.build());
                }
            }
        }
        
        // 终点
        PathLandmark.Builder endBuilder = PathLandmark.newBuilder()
                .setValid(true)
                .setWaypointType(WayPointType.END_POINT)
                .setLandmarkType(LandmarkType.CLICK);
        
        String[] destCoords = request.getDestination().split(",");
        if (destCoords.length == 2) {
            PointLL destination = PointLL.newBuilder()
                    .setLng(Double.parseDouble(destCoords[0]))
                    .setLat(Double.parseDouble(destCoords[1]))
                    .build();
            endBuilder.setPt(destination);
        }
        pathPoints.add(endBuilder.build());
        
        queryBuilder.addAllPathPoints(pathPoints);
        
        builder.setQuery(queryBuilder);
        return builder.build();
    }
    
    /**
     * 转换PathResponse为RouteResponse
     */
    private RouteResponse convertToRouteResponse(PathResponse protoResponse, RouteRequest request) {
        RouteResponse response = new RouteResponse();
        
        if (!protoResponse.getSuccess()) {
            response.setStatus("0");
            response.setInfo(protoResponse.getErrorMessage());
            response.setInfocode("10001");
            response.setCount(0);
            return response;
        }
        
        response.setStatus("1");
        response.setInfo("OK");
        response.setInfocode("10000");
        
        PathResult protoResult = protoResponse.getResult();
        response.setCount(protoResult.getPathsCount());
        
        // 构建Route对象
        RouteResponse.Route route = new RouteResponse.Route();
        route.setOrigin(request.getOrigin());
        route.setDestination(request.getDestination());
        
        List<RouteResponse.Path> paths = new ArrayList<>();
        for (PathInfo protoPath : protoResult.getPathsList()) {
            RouteResponse.Path path = convertToPath(protoPath, request);
            paths.add(path);
        }
        route.setPaths(paths);
        
        response.setRoute(route);
        return response;
    }
    
    /**
     * 转换PathInfo为Path
     */
    private RouteResponse.Path convertToPath(PathInfo protoPath, RouteRequest request) {
        RouteResponse.Path path = new RouteResponse.Path();
        
        path.setDistance((int) protoPath.getLength());
        path.setDuration((int) protoPath.getTravelTime());
        path.setTrafficLights((int) protoPath.getTrafficLightNum());
        
        // 设置策略描述
        String strategyDesc = getStrategyDescription(request.getStrategy());
        path.setStrategy(strategyDesc);
        
        // 构建坐标点串
        if (request.getGeometry() != null && request.getGeometry()) {
            StringBuilder polyline = new StringBuilder();
            for (int i = 0; i < protoPath.getPointsCount(); i++) {
                PointLL point = protoPath.getPoints(i);
                if (i > 0) {
                    polyline.append(";");
                }
                polyline.append(point.getLng()).append(",").append(point.getLat());
            }
            path.setPolyline(polyline.toString());
        }
        
        // 构建路段信息
        if (request.getSteps() != null && request.getSteps()) {
            List<RouteResponse.Step> steps = new ArrayList<>();
            for (Section section : protoPath.getSectionsList()) {
                RouteResponse.Step step = new RouteResponse.Step();
                step.setDistance((int) section.getLength());
                step.setDuration((int) section.getTime());
                step.setInstruction("直行"); // 简化处理
                step.setOrientation("北"); // 简化处理
                steps.add(step);
            }
            path.setSteps(steps);
        }
        
        return path;
    }
    
    /**
     * 获取策略描述
     */
    private String getStrategyDescription(Integer strategy) {
        switch (strategy) {
            case 0: return "最短时间优先";
            case 1: return "最短距离优先";
            case 2: return "高速优先";
            case 3: return "避开收费路段";
            default: return "未知策略";
        }
    }
    
    /**
     * 创建错误响应
     */
    private RouteResponse createErrorResponse(String message, String code) {
        RouteResponse response = new RouteResponse();
        response.setStatus("0");
        response.setInfo(message);
        response.setInfocode(code);
        response.setCount(0);
        return response;
    }
}
