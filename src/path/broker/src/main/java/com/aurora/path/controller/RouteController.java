package com.aurora.path.controller;

import com.aurora.path.dto.RouteRequest;
import com.aurora.path.dto.RouteResponse;
import com.aurora.path.service.PathService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 路径规划REST API控制器
 * 参考高德地图API设计
 */
@RestController
@RequestMapping("/v3/direction")
@Validated
public class RouteController {
    
    private static final Logger logger = LoggerFactory.getLogger(RouteController.class);
    
    @Autowired
    private PathService pathService;
    
    /**
     * 驾车路径规划
     * GET /v3/direction/driving
     * 
     * @param origin 起点坐标，格式：经度,纬度
     * @param destination 终点坐标，格式：经度,纬度
     * @param waypoints 途经点坐标，格式：经度,纬度|经度,纬度
     * @param strategy 路径规划策略，0:最短时间优先，1:最短距离优先，2:高速优先，3:避开收费路段
     * @param mode 算路模式，0:离线，1:在线，2:自动选择
     * @param departureTimeType 出发时间类型，0:无时间，1:当前时间，2:出发时间，3:到达时间
     * @param departureTime 出发时间，格式：YYYY-MM-DDThh:mm
     * @param output 返回格式，json:JSON格式，proto:Protobuf格式
     * @param geometry 是否返回路径几何信息
     * @param steps 是否返回路段信息
     * @param extensions 扩展信息，all:返回全部扩展信息
     * @return 路径规划结果
     */
    @GetMapping("/driving")
    public ResponseEntity<?> getDrivingRoute(
            @RequestParam String origin,
            @RequestParam String destination,
            @RequestParam(required = false) String waypoints,
            @RequestParam(defaultValue = "0") Integer strategy,
            @RequestParam(defaultValue = "0") Integer mode,
            @RequestParam(name = "departure_time_type", defaultValue = "0") Integer departureTimeType,
            @RequestParam(name = "departure_time", required = false) String departureTime,
            @RequestParam(defaultValue = "json") String output,
            @RequestParam(defaultValue = "true") Boolean geometry,
            @RequestParam(defaultValue = "true") Boolean steps,
            @RequestParam(defaultValue = "all") String extensions) {
        
        logger.info("Received driving route request: origin={}, destination={}, waypoints={}, strategy={}, mode={}, output={}", 
                   origin, destination, waypoints, strategy, mode, output);
        
        try {
            // 构建请求对象
            RouteRequest request = new RouteRequest();
            request.setOrigin(origin);
            request.setDestination(destination);
            request.setWaypoints(waypoints);
            request.setStrategy(strategy);
            request.setMode(mode);
            request.setDepartureTimeType(departureTimeType);
            request.setDepartureTime(departureTime);
            request.setOutput(output);
            request.setGeometry(geometry);
            request.setSteps(steps);
            request.setExtensions(extensions);
            
            // 根据输出格式返回不同类型的响应
            if ("proto".equalsIgnoreCase(output)) {
                byte[] protoData = pathService.calculateRouteProto(request);
                
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                headers.add("Content-Disposition", "attachment; filename=route.pb");
                
                return new ResponseEntity<>(protoData, headers, HttpStatus.OK);
            } else {
                RouteResponse response = pathService.calculateRoute(request);
                return ResponseEntity.ok(response);
            }
            
        } catch (Exception e) {
            logger.error("Error processing driving route request", e);
            
            if ("proto".equalsIgnoreCase(output)) {
                // 返回protobuf格式的错误响应
                // 这里简化处理，实际应该构建protobuf错误响应
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Internal server error".getBytes());
            } else {
                RouteResponse errorResponse = new RouteResponse();
                errorResponse.setStatus("0");
                errorResponse.setInfo("服务器内部错误: " + e.getMessage());
                errorResponse.setInfocode("20000");
                errorResponse.setCount(0);
                
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(errorResponse);
            }
        }
    }
    
    /**
     * 驾车路径规划 (POST方式)
     * POST /v3/direction/driving
     * 
     * @param request 路径规划请求
     * @return 路径规划结果
     */
    @PostMapping("/driving")
    public ResponseEntity<?> postDrivingRoute(@Valid @RequestBody RouteRequest request) {
        
        logger.info("Received POST driving route request: {}", request);
        
        try {
            // 根据输出格式返回不同类型的响应
            if ("proto".equalsIgnoreCase(request.getOutput())) {
                byte[] protoData = pathService.calculateRouteProto(request);
                
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                headers.add("Content-Disposition", "attachment; filename=route.pb");
                
                return new ResponseEntity<>(protoData, headers, HttpStatus.OK);
            } else {
                RouteResponse response = pathService.calculateRoute(request);
                return ResponseEntity.ok(response);
            }
            
        } catch (Exception e) {
            logger.error("Error processing POST driving route request", e);
            
            if ("proto".equalsIgnoreCase(request.getOutput())) {
                // 返回protobuf格式的错误响应
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Internal server error".getBytes());
            } else {
                RouteResponse errorResponse = new RouteResponse();
                errorResponse.setStatus("0");
                errorResponse.setInfo("服务器内部错误: " + e.getMessage());
                errorResponse.setInfocode("20000");
                errorResponse.setCount(0);
                
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(errorResponse);
            }
        }
    }
    
    /**
     * 健康检查接口
     * GET /v3/direction/health
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("OK");
    }
}
