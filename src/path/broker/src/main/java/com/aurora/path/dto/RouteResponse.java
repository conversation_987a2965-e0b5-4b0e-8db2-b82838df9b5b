package com.aurora.path.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 路径规划响应DTO，参考高德地图API设计
 */
public class RouteResponse {
    
    /**
     * 状态码
     * 1: 成功
     * 0: 失败
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 状态信息
     */
    @JsonProperty("info")
    private String info;
    
    /**
     * 状态码
     */
    @JsonProperty("infocode")
    private String infocode;
    
    /**
     * 路径规划结果数量
     */
    @JsonProperty("count")
    private Integer count;
    
    /**
     * 路径规划方案
     */
    @JsonProperty("route")
    private Route route;
    
    public static class Route {
        /**
         * 起点坐标
         */
        @JsonProperty("origin")
        private String origin;
        
        /**
         * 终点坐标
         */
        @JsonProperty("destination")
        private String destination;
        
        /**
         * 路径列表
         */
        @JsonProperty("paths")
        private List<Path> paths;
        
        // Getters and Setters
        public String getOrigin() {
            return origin;
        }
        
        public void setOrigin(String origin) {
            this.origin = origin;
        }
        
        public String getDestination() {
            return destination;
        }
        
        public void setDestination(String destination) {
            this.destination = destination;
        }
        
        public List<Path> getPaths() {
            return paths;
        }
        
        public void setPaths(List<Path> paths) {
            this.paths = paths;
        }
    }
    
    public static class Path {
        /**
         * 路径距离，单位：米
         */
        @JsonProperty("distance")
        private Integer distance;
        
        /**
         * 路径耗时，单位：秒
         */
        @JsonProperty("duration")
        private Integer duration;
        
        /**
         * 路径策略
         */
        @JsonProperty("strategy")
        private String strategy;
        
        /**
         * 收费路段长度，单位：米
         */
        @JsonProperty("tolls")
        private Integer tolls;
        
        /**
         * 收费金额，单位：元
         */
        @JsonProperty("toll_distance")
        private Integer tollDistance;
        
        /**
         * 红绿灯个数
         */
        @JsonProperty("traffic_lights")
        private Integer trafficLights;
        
        /**
         * 路径坐标点串
         */
        @JsonProperty("polyline")
        private String polyline;
        
        /**
         * 路段列表
         */
        @JsonProperty("steps")
        private List<Step> steps;
        
        // Getters and Setters
        public Integer getDistance() {
            return distance;
        }
        
        public void setDistance(Integer distance) {
            this.distance = distance;
        }
        
        public Integer getDuration() {
            return duration;
        }
        
        public void setDuration(Integer duration) {
            this.duration = duration;
        }
        
        public String getStrategy() {
            return strategy;
        }
        
        public void setStrategy(String strategy) {
            this.strategy = strategy;
        }
        
        public Integer getTolls() {
            return tolls;
        }
        
        public void setTolls(Integer tolls) {
            this.tolls = tolls;
        }
        
        public Integer getTollDistance() {
            return tollDistance;
        }
        
        public void setTollDistance(Integer tollDistance) {
            this.tollDistance = tollDistance;
        }
        
        public Integer getTrafficLights() {
            return trafficLights;
        }
        
        public void setTrafficLights(Integer trafficLights) {
            this.trafficLights = trafficLights;
        }
        
        public String getPolyline() {
            return polyline;
        }
        
        public void setPolyline(String polyline) {
            this.polyline = polyline;
        }
        
        public List<Step> getSteps() {
            return steps;
        }
        
        public void setSteps(List<Step> steps) {
            this.steps = steps;
        }
    }
    
    public static class Step {
        /**
         * 路段指示
         */
        @JsonProperty("instruction")
        private String instruction;
        
        /**
         * 路段方向
         */
        @JsonProperty("orientation")
        private String orientation;
        
        /**
         * 路段距离，单位：米
         */
        @JsonProperty("distance")
        private Integer distance;
        
        /**
         * 路段耗时，单位：秒
         */
        @JsonProperty("duration")
        private Integer duration;
        
        /**
         * 路段坐标点串
         */
        @JsonProperty("polyline")
        private String polyline;
        
        /**
         * 路段道路名称
         */
        @JsonProperty("road")
        private String road;
        
        // Getters and Setters
        public String getInstruction() {
            return instruction;
        }
        
        public void setInstruction(String instruction) {
            this.instruction = instruction;
        }
        
        public String getOrientation() {
            return orientation;
        }
        
        public void setOrientation(String orientation) {
            this.orientation = orientation;
        }
        
        public Integer getDistance() {
            return distance;
        }
        
        public void setDistance(Integer distance) {
            this.distance = distance;
        }
        
        public Integer getDuration() {
            return duration;
        }
        
        public void setDuration(Integer duration) {
            this.duration = duration;
        }
        
        public String getPolyline() {
            return polyline;
        }
        
        public void setPolyline(String polyline) {
            this.polyline = polyline;
        }
        
        public String getRoad() {
            return road;
        }
        
        public void setRoad(String road) {
            this.road = road;
        }
    }
    
    // Main class getters and setters
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getInfo() {
        return info;
    }
    
    public void setInfo(String info) {
        this.info = info;
    }
    
    public String getInfocode() {
        return infocode;
    }
    
    public void setInfocode(String infocode) {
        this.infocode = infocode;
    }
    
    public Integer getCount() {
        return count;
    }
    
    public void setCount(Integer count) {
        this.count = count;
    }
    
    public Route getRoute() {
        return route;
    }
    
    public void setRoute(Route route) {
        this.route = route;
    }
}
