package com.aurora.path.jni;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * JNI接口类，用于调用C++算路引擎
 */
@Component
public class PathJNI {
    
    private static final Logger logger = LoggerFactory.getLogger(PathJNI.class);
    
    static {
        try {
            // 加载本地库
            System.loadLibrary("path_jni");
            logger.info("Successfully loaded path_jni library");
        } catch (UnsatisfiedLinkError e) {
            logger.error("Failed to load path_jni library", e);
            throw new RuntimeException("Failed to load path_jni library", e);
        }
    }
    
    /**
     * 初始化路径引擎
     * @param configPath 配置文件路径
     * @param dataDir 数据目录路径
     * @return 是否成功初始化
     */
    public native boolean initPathEngine(String configPath, String dataDir);
    
    /**
     * 启动路径引擎
     * @return 是否成功启动
     */
    public native boolean startPathEngine();
    
    /**
     * 停止路径引擎
     * @return 是否成功停止
     */
    public native boolean stopPathEngine();
    
    /**
     * 反初始化路径引擎
     * @return 是否成功反初始化
     */
    public native boolean uninitPathEngine();
    
    /**
     * 请求路径规划
     * @param requestData 序列化的PathRequest protobuf数据
     * @return 序列化的PathResponse protobuf数据
     */
    public native byte[] requestPath(byte[] requestData);
    
    /**
     * 取消路径请求
     * @param requestId 请求ID
     * @return 是否成功取消
     */
    public native boolean cancelRequest(String requestId);
    
    /**
     * 检查引擎是否已初始化
     * @return 是否已初始化
     */
    public native boolean isInitialized();
    
    /**
     * 获取引擎版本信息
     * @return 版本信息
     */
    public native String getVersion();
}
