package com.aurora.path.controller;

import com.aurora.path.dto.RouteResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<RouteResponse> handleValidationException(MethodArgumentNotValidException e) {
        logger.warn("Validation error: {}", e.getMessage());
        
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        RouteResponse response = createErrorResponse("参数校验失败: " + errorMessage, "40001");
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<RouteResponse> handleBindException(BindException e) {
        logger.warn("Bind error: {}", e.getMessage());
        
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        RouteResponse response = createErrorResponse("参数绑定失败: " + errorMessage, "40002");
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<RouteResponse> handleConstraintViolationException(ConstraintViolationException e) {
        logger.warn("Constraint violation: {}", e.getMessage());
        
        String errorMessage = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        
        RouteResponse response = createErrorResponse("参数约束违反: " + errorMessage, "40003");
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<RouteResponse> handleMissingParameterException(MissingServletRequestParameterException e) {
        logger.warn("Missing parameter: {}", e.getMessage());
        
        RouteResponse response = createErrorResponse("缺少必需参数: " + e.getParameterName(), "40004");
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<RouteResponse> handleIllegalArgumentException(IllegalArgumentException e) {
        logger.warn("Illegal argument: {}", e.getMessage());
        
        RouteResponse response = createErrorResponse("参数错误: " + e.getMessage(), "40005");
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<RouteResponse> handleRuntimeException(RuntimeException e) {
        logger.error("Runtime error", e);
        
        RouteResponse response = createErrorResponse("服务运行异常: " + e.getMessage(), "50001");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<RouteResponse> handleGenericException(Exception e) {
        logger.error("Unexpected error", e);
        
        RouteResponse response = createErrorResponse("服务器内部错误", "50000");
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 创建错误响应
     */
    private RouteResponse createErrorResponse(String message, String code) {
        RouteResponse response = new RouteResponse();
        response.setStatus("0");
        response.setInfo(message);
        response.setInfocode(code);
        response.setCount(0);
        return response;
    }
}
