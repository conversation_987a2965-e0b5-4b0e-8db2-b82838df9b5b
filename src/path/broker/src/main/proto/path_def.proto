syntax = "proto3";

package aurora.path;

option java_package = "com.aurora.path.proto";
option java_outer_classname = "PathDefProto";
option java_multiple_files = true;

// 基础数据类型
message PointLL {
    double lng = 1;  // 经度
    double lat = 2;  // 纬度
}

message AABB2 {
    PointLL min_pt = 1;  // 最小点
    PointLL max_pt = 2;  // 最大点
}

// 枚举类型
enum PathStrategy {
    TIME_FIRST = 0;      // 最短时间优先
    DISTANCE_FIRST = 1;  // 最短距离优先
    HIGHWAY_FIRST = 2;   // 高速优先
    AVOID_TOLL = 3;      // 避开收费路段
}

enum WayPointType {
    START_POINT = 0;     // 起始点
    VIA_POINT = 1;       // 途经点
    END_POINT = 2;       // 终点
}

enum LandmarkType {
    EGO = 0;             // 自车位置
    CLICK = 1;           // 地图点击选点
    POI = 2;             // POI选点
}

enum DateTimeType {
    NO_TIME = 0;         // 无时间
    CURRENT = 1;         // 当前时间
    DEPART_AT = 2;       // 出发时间
    ARRIVE_BY = 3;       // 到达时间
}

enum PathTrigger {
    INITIAL_ROUTING = 0; // 初次算路
    RE_ROUTING = 1;      // 偏航重规划
    ROUTE_REFRESH = 2;   // 路线刷新
}

enum PathMode {
    OFFLINE = 0;         // 离线
    ONLINE = 1;          // 在线
    AUTO = 2;            // 自动选择
}

// 复杂数据结构
message DateTimeOption {
    DateTimeType type = 1;    // 日期时间类型
    string value = 2;         // 日期时间值（ISO 8601格式：YYYY-MM-DDThh:mm）
}

message EdgeInfo {
    uint64 tile_id = 1;                    // route tile id
    uint32 id = 2;                         // 边的tile内id
    bool forward = 3;                      // 边信息是正向还是反向
    
    // 基础link属性
    uint32 function_class = 4;             // 功能等级
    uint32 link_type = 5;                  // 链接类型
    uint32 direction = 6;                  // 方向
    bool need_toll = 7;                    // 是否收费
    
    uint32 start_node_id = 8;              // 起始节点ID
    uint32 positive_speed_limit = 9;       // 正向速度限制
    bool is_overhead = 10;                 // 是否高架
    bool is_inner_link = 11;               // 是否内部链接
    bool is_separate = 12;                 // 是否分隔
    
    uint32 end_node_id = 13;               // 终止节点ID
    uint32 negative_speed_limit = 14;      // 负向速度限制
    bool is_area_link = 15;                // 是否区域链接
    bool is_city_link = 16;                // 是否城市链接
    bool is_ramp = 17;                     // 是否匝道
    
    uint32 link_form = 18;                 // link form
    uint32 speed_grade = 19;               // 速度等级
    uint32 length = 20;                    // 长度
    
    uint32 forward_lane_count = 21;        // 正向车道数
    uint32 backward_lane_count = 22;       // 反向车道数
    uint32 lane_count = 23;                // 总车道数
    
    uint32 road_class = 24;                // 道路等级
    bool is_left = 25;                     // 是否左侧
    bool has_turn_rule = 26;               // 是否有转弯规则
    bool is_time_limit = 27;               // 是否有时间限制
    bool is_all_day_limit = 28;            // 是否全天限制
    
    bool is_building = 29;                 // 是否建筑物
    bool is_paved = 30;                    // 是否铺装
    bool is_gate = 31;                     // 是否门/闸
    bool no_crossing = 32;                 // 是否禁止穿越
    bool is_private = 33;                  // 是否私有
    
    repeated PointLL geos = 34;            // 几何形状
}

message Candidate {
    double heading = 1;      // 朝向角，从正北顺时针计算（弧度rad）
    EdgeInfo link = 2;       // 道路信息
    double offset = 3;       // 沿通行方向道路偏移量, 单位m
    double proj_dis = 4;     // 到道路投影点的距离
    uint64 proj_index = 5;   // 投影segment的起始index
    PointLL proj_pt = 6;     // 道路投影点
    double confidence = 7;   // 匹配置信度
}

message PathLandmark {
    bool valid = 1;                        // 是否填充有效坐标
    WayPointType waypoint_type = 2;        // 起点/途经点/终点
    LandmarkType landmark_type = 3;        // 地标类型
    PointLL pt = 4;                        // 需要匹配的经纬度坐标
    string name = 5;                       // 地标名称
    repeated Candidate candidates = 6;     // 候选匹配结果
}

message RestrictionOption {
    repeated uint64 edges = 1;
    repeated uint64 tile_ids = 2;
    repeated PointLL polygon = 3;
}

message PathQuery {
    PathStrategy strategy = 1;                     // 路径规划策略
    DateTimeOption date_option = 2;                // 日期时间选项
    PathTrigger trigger = 3;                       // 算路触发类型
    PathMode mode = 4;                             // 算路模式：在线、离线
    RestrictionOption restrict_option = 5;         // 限行选项
    repeated PathLandmark path_points = 6;         // 起点/途经点/终点
}

message ResultMetadata {
    string build_version = 1;     // 构建版本
    string data_version = 2;      // 数据版本
    uint64 query_time_ms = 3;     // 算路时间（毫秒）
}

message Section {
    uint32 index = 1;        // 对应links的起始索引
    uint32 num = 2;          // 两个算路点之间的link数量
    double length = 3;       // 长度，单位m
    double time = 4;         // 时间，单位s
    double start_offset = 5; // 沿着通行方向从link开始节点的偏移量，单位m
    double end_offset = 6;   // 沿着通行方向的结束偏移量，单位m
}

message PathInfo {
    uint64 path_id = 1;                // 路径唯一标识符
    double length = 2;                 // 路径总长度，单位m
    double travel_time = 3;            // 时间花费，单位s
    uint32 traffic_light_num = 4;      // 途经红绿灯数量
    repeated Section sections = 5;     // 路径section列表
    repeated EdgeInfo links = 6;       // 路径link列表
    repeated PointLL points = 7;       // WGS84坐标系下的路径点列表
    AABB2 bbox = 8;                    // MBR
}

message PathResult {
    string uuid = 1;              // 结果唯一标识符，与算路请求对应
    string status = 2;            // 状态信息
    string tag = 3;               // 路线标签：如 高速最多，限速多等
    int32 code = 4;               // 结果状态码
    ResultMetadata metadata = 5;  // 元数据信息
    repeated PathInfo paths = 6;  // 规划路径结果列表
}

// JNI 接口消息
message PathRequest {
    string request_id = 1;        // 请求ID
    PathQuery query = 2;          // 路径查询
}

message PathResponse {
    string request_id = 1;        // 请求ID
    PathResult result = 2;        // 路径结果
    bool success = 3;             // 是否成功
    string error_message = 4;     // 错误信息
}
