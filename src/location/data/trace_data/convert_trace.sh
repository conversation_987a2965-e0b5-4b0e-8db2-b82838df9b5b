#!/bin/bash

dir=/home/<USER>/docs/navi/map_engine/test/api_test/
test_out_dir=trace_data/
# rm -rf debug_output/*/
# CASE=test_normal_0_test_20180312143929
# CASE=test_small_angle_road_
# Recursively find all *.dat files in the dir and subdirs, then run test app with each file
find "$dir" -type f -name "*.dat" | while read -r file; do
    echo "Running test with file: $file"
    test_base_name=`basename "$file" .dat`
    test_category=$(echo $test_base_name | sed 's/_._test_.*$//g')
    test_name=$(echo $test_base_name | sed 's/^.*_._test_//g')
    mkdir $test_out_dir/$test_category
    echo target==  $test_out_dir/$test_category/$test_name.csv
    /home/<USER>/src/map_engine/distribution/bin/location_test_replay -c  "$file"  $test_out_dir/$test_category/$test_name.csv
    # /home/<USER>/src/map_engine/distribution/bin/location_test_replay "$file" $test_out_dir/$test_base_name 1>  $test_out_dir/$test_base_name/test.log 2> $test_out_dir/$test_base_name/error.log
    # sha1sum $file >>hash.log
    # ((i++))
    # echo $i
done
# new_bn_file="NewBreakCount.`date +%Y%m%d%H%M%S`.log"

# grep "Break count" debug_output/*/stati*   > $new_bn_file
# diff -u $prev_bn_file $new_bn_file > break_count_diff.log

# echo $i
