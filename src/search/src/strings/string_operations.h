#pragma once

#include <sstream>
#include <string>
#include <vector>

#include "uni_string.h"
namespace aurora::search::strings {
std::vector<std::string> SplitString(const std::string& str, char delimiter);
template <typename Iterator>
std::string Join(Iterator begin, Iterator end, const std::string& sep) {
  return Join(begin, end, sep, [](const auto& item) -> std::string {
    if constexpr (std::is_same_v<std::decay_t<decltype(item)>, std::string>) {
      return item;
    } else {
      return std::to_string(item);
    }
  });
}

template <typename Iterator, typename Extract>
std::string Join(Iterator begin, Iterator end, const std::string& sep, Extract extract) {
  std::ostringstream oss;
  for (Iterator it = begin; it != end; ++it) {
    if (it != begin) {
      oss << sep;
    }
    oss << extract(*it);
  }
  return oss.str();
}

int GetByteNum(unsigned char k);

int GetNextChar(const char* in, int off, char* out);

int GetNewWord(const char* content, int off, char* word);

std::string DealString(const std::string& str);

// UniString utils
UniString MakeUniString(std::string_view string_utf8);
std::string ToUtf8(UniString const& s);

/// Performs full case folding for string to make it search-compatible according
/// to rules in ftp://ftp.unicode.org/Public/UNIDATA/CaseFolding.txt
void MakeLowerCaseInplace(UniString& s);

/// Performs NFKD - Compatibility decomposition for Unicode according
/// to rules in ftp://ftp.unicode.org/Public/UNIDATA/UnicodeData.txt
void NormalizeInplace(UniString& s);

UniString Normalize(UniString s);
std::string Normalize(std::string const& s);

std::string DebugString(UniChar c);
std::string DebugString(const UniString& str);

UniString NormalizeAndSimplifyString(std::string_view s);

bool IsDelimiter(UniChar c);

bool ContainsChinese(const UniString& str);
void SplitUniString(UniString const& uniS, std::vector<UniString>* tokens);
}  // namespace aurora::search::strings
