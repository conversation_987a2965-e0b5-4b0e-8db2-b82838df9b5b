#include "stop_words_checker.h"

#include <set>

#include "string_operations.h"

namespace aurora::search::strings {
namespace {

class StopWordsChecker {
 public:
  // Returns true when |s| is a stop-word and may be removed from a query.
  StopWordsChecker() {
    // Only 2-letters and the most common.
    char const* arr[] = {"a",  "s",  "the",                          // English
                         "am", "im", "an",                           // German
                         "d",  "da", "de",  "di", "du", "la", "le",  // French, Spanish, Italian
                         "и",  "я",                                  // Cyrillic
                         "号", "路", "店",  "栋", "米", "层", "与", "门", "区", "街", "座",
                         "村", "新", "城",  "坪", "巷", "园", "旁", "室", "楼", "和"};

    for (char const* s : arr) {
      stop_words_.insert(MakeUniString(s));
    }
  }
  bool Has(UniString const& s) const { return stop_words_.count(s) > 0; }

 private:
  std::set<UniString> stop_words_;
};

}  // namespace

const StopWordsChecker& GetStopWordsChecker() {
  static StopWordsChecker const stop_words_checker;
  return stop_words_checker;
}

bool IsStopWord(UniString const& s) { return GetStopWordsChecker().Has(s); }
bool IsStopWord(const std::string& s) { return GetStopWordsChecker().Has(MakeUniString(s)); }

}  // namespace aurora::search::strings
