#include "string_operations.h"

#include <utfcpp/utf8/unchecked.h>

#include <cstring>
#include <iomanip>
#include <sstream>

#include "utils/stl_helper.h"

namespace aurora::search::strings {
namespace {
bool IsChineseCharacter(uint32_t codePoint) {
  return (codePoint >= 0x4E00 && codePoint <= 0x9FFF) ||    // CJK Unified Ideographs
         (codePoint >= 0x3400 && codePoint <= 0x4DBF) ||    // CJK Unified Ideographs Extension A
         (codePoint >= 0x20000 && codePoint <= 0x2A6DF) ||  // CJK Unified Ideographs Extension B
         (codePoint >= 0x2A700 && codePoint <= 0x2B73F) ||  // CJK Unified Ideographs Extension C
         (codePoint >= 0x2B740 && codePoint <= 0x2B81F) ||  // CJK Unified Ideographs Extension D
         (codePoint >= 0x2B820 && codePoint <= 0x2CEAF) ||  // CJK Unified Ideographs Extension E
         (codePoint >= 0x2CEB0 && codePoint <= 0x2EBEF) ||  // CJK Unified Ideographs Extension F
         (codePoint >= 0xF900 && codePoint <= 0xFAFF);      // CJK Compatibility Ideographs
}

}  // namespace
std::vector<std::string> SplitString(const std::string& str, char delimiter) {
  std::vector<std::string> fields;
  std::stringstream ss(str);
  std::string item;
  while (std::getline(ss, item, delimiter)) {
    fields.push_back(item);
  }
  return fields;
}

int GetByteNum(unsigned char k) {
  if (k == 0) {
    return 0;
  }
  int bit = 0;
  while (k > 0x7f) {
    k = k << 1;
    ++bit;
  }
  if (bit == 0) {
    return 1;
  }
  return bit;
}

int GetNextChar(const char* in, int off, char* out) {
  if (off < 0) {
    return 0;
  }
  int byte_num = GetByteNum(in[off]);
  strncpy(out, &in[off], byte_num);
  out[byte_num] = '\0';
  return byte_num;
}

unsigned int ToUnicode(char* word) {
  unsigned int a;
  unsigned char t = word[0];
  a = t & 0x0f;
  t = word[1];
  a = (a << 6) + (t & 0x3f);
  t = word[2];
  a = (a << 6) + (t & 0x3f);
  return a;
}

int GetNewWord(const char* content, int off, char* word) {
  if (word == nullptr) {
    return -1;
  }
  int add = GetNextChar(content, off, word);  // 字长
  int p = add + off;                          // 当前字位置
  while (add != 0) {
    word[add] = '\0';
    if (add == 3) {
      // u4e00-u9fa5 〇=u3007
      unsigned int a = ToUnicode(word);
      if ((a >= 0x4e00 && a <= 0x9fa5) || a == 0x3007) {
        return p;
      }
    }
    if (word[0] >= 'A' && word[0] <= 'Z') {
      word[0] = word[0] + 32;  //'a' - 'A';
      return p;
    }
    if (word[0] >= 'a' && word[0] <= 'z') {
      return p;
    }
    if (word[0] >= '0' && word[0] <= '9') {
      return p;
    }
    add = GetNextChar(content, p, word);
    p = add + p;
  }
  return -1;
}

std::string DealString(const std::string& str) {
  char* word = new char[str.size() + 1];
  const char* s = str.c_str();
  int p = GetNewWord(s, 0, word);
  std::string result("");
  while (p != -1) {
    result += word;
    p = GetNewWord(s, p, word);
  }
  delete[] word;
  return result;
}

UniString MakeUniString(const std::string_view string_utf8) {
  UniString result;
  utf8::unchecked::utf8to32(string_utf8.begin(), string_utf8.end(), std::back_inserter(result));
  return result;
}

std::string ToUtf8(UniString const& s) {
  std::string result;
  utf8::unchecked::utf32to8(s.begin(), s.end(), back_inserter(result));
  return result;
}
UniString Normalize(UniString s) {
  NormalizeInplace(s);
  return s;
}

std::string Normalize(std::string const& s) {
  auto uniString = MakeUniString(s);
  NormalizeInplace(uniString);
  return ToUtf8(uniString);
}

std::string DebugString(const UniChar c) {
  std::stringstream ss;
  ss << "U+" << std::hex << std::uppercase << std::setfill('0') << std::setw(4) << c;
  return ss.str();
}

std::string DebugString(const UniString& str) {
  std::stringstream ss;
  for (char32_t c : str) {
    ss << DebugString(c) << " ";
  }
  std::string formatted_str = ss.str();
  if (!formatted_str.empty()) {
    formatted_str.pop_back();
  }
  return formatted_str;
}

UniString NormalizeAndSimplifyString(std::string_view s) {
  UniString uniString = MakeUniString(s);
  for (size_t i = 0; i < uniString.size(); ++i) {
    UniChar& c = uniString[i];
    switch (c) {
      // Replace "d with stroke" to simple d letter. Used in Vietnamese.
      // (unicode-compliant implementation leaves it unchanged)
      case 0x0110:
      case 0x0111:
        c = 'd';
        break;
      // Replace small turkish dotless 'ı' with dotted 'i'.  Our own
      // invented hack to avoid well-known Turkish I-letter bug.
      case 0x0131:
        c = 'i';
        break;
      // Replace capital turkish dotted 'İ' with dotted lowercased 'i'.
      // Here we need to handle this case manually too, because default
      // unicode-compliant implementation of MakeLowerCase converts 'İ'
      // to 'i' + 0x0307.
      case 0x0130:
        c = 'i';
        break;
      // Some Danish-specific hacks.
      case 0x00d8:  // Ø
      case 0x00f8:  // ø
        c = 'o';
        break;
      case 0x0152:  // Œ
      case 0x0153:  // œ
        c = 'o';
        uniString.insert(uniString.begin() + (i++) + 1, 'e');
        break;
      case 0x00c6:  // Æ
      case 0x00e6:  // æ
        c = 'a';
        uniString.insert(uniString.begin() + (i++) + 1, 'e');
        break;
      case 0x2018:  // ‘
      case 0x2019:  // ’
        c = '\'';
        break;
      case 0x2116:  // №
        c = '#';
        break;
    }
  }

  MakeLowerCaseInplace(uniString);
  NormalizeInplace(uniString);

  // Remove accents that can appear after NFKD normalization.
  uniString.erase_if([](UniChar const& c) {
    // ̀  COMBINING GRAVE ACCENT
    // ́  COMBINING ACUTE ACCENT
    return (c == 0x0300 || c == 0x0301);
  });

  // Replace sequence of spaces with single one.
  Unique(uniString, [](UniChar l, UniChar r) { return (l == r && l == ' '); });

  return uniString;
}

bool ContainsChinese(const UniString& str) {
  for (auto& c : str) {
    if (IsChineseCharacter(c)) {
      return true;
    }
  }
  return false;
}

void SplitUniString(UniString const& uniS, std::vector<UniString>* tokens) {
  size_t const count = uniS.size();
  size_t i = 0;
  while (true) {
    while (i < count && IsDelimiter(uniS[i])) ++i;
    if (i >= count) break;

    size_t j = i + 1;
    while (j < count && !IsDelimiter(uniS[j])) ++j;

    auto const beg = uniS.begin();
    UniString str(beg + i, beg + j);

    // Transform "xyz's" -> "xyzs".
    if (j + 1 < count && uniS[j] == '\'' && uniS[j + 1] == 's' &&
        (j + 2 == count || strings::IsDelimiter(uniS[j + 2]))) {
      str.push_back(uniS[j + 1]);
      j += 2;
    }
    tokens->emplace_back(std::move(str));

    i = j;
  }
}

}  // namespace aurora::search::strings