#pragma once

#include <memory>
#include <vector>

#include "tokenizer.h"
namespace cppjieba {
class Jieba;
}  // namespace cppjieba

namespace aurora::search {
namespace strings {
class UniString;
}  // namespace strings
class ChineseTokenizer final : public Tokenizer {
 public:
  ChineseTokenizer() = default;
  ~ChineseTokenizer() override = default;
  bool Init(const std::shared_ptr<SearchConfig> &config) override;

  void Tokenize(const std::string &query, std::vector<Token> *tokens) const override;

 private:
  std::vector<strings::UniString> TokenizeChinese(
      const std::vector<strings::UniString> &raw_tokens) const;
  bool can_process_chinese_ = false;
  std::shared_ptr<cppjieba::Jieba> jieba_ = nullptr;
};

}  // namespace aurora::search
