#include "general_tokenizer.h"

#include <common/search_config.h>
#include <logger.h>

#include <cppjieba/Jieba.hpp>

#include "common/parsed_query.h"
#include "strings/stop_words_checker.h"
#include "strings/string_operations.h"

namespace aurora::search {
bool ChineseTokenizer::Init(const std::shared_ptr<SearchConfig> &config) {
  can_process_chinese_ = false;
  if (config->enable_chinese_processing) {
    auto dict_path = config->jieba_dir;
    try {
      jieba_ = std::make_shared<cppjieba::Jieba>(
          dict_path + "/jieba.dict.utf8", dict_path + "/hmm_model.utf8",
          dict_path + "/user.dict.utf8", dict_path + "/idf.utf8", dict_path + "/stop_words.utf8");
      can_process_chinese_ = true;
    } catch (const std::exception &e) {
      LOG_ERROR("Unable to load jieba: {}", e.what());
      jieba_ = nullptr;
      return false;
    }
  }
  return true;
}

void ChineseTokenizer::Tokenize(const std::string &query, std::vector<Token> *tokens) const {
  auto normalized_query = strings::NormalizeAndSimplifyString(query);
  std::vector<strings::UniString> raw_tokens;
  strings::SplitUniString(normalized_query, &raw_tokens);

  bool process_chinese = can_process_chinese_ && strings::ContainsChinese(normalized_query);
  if (process_chinese) {
    raw_tokens = TokenizeChinese(raw_tokens);
  }

  // remove stop words
  for (auto &raw_token : raw_tokens) {
    if (!strings::IsStopWord(raw_token)) {
      tokens->emplace_back(std::move(raw_token));
    }
  }

  LOG_INFO("Origin: [{}], segmented: [{}]", query,
           strings::Join(tokens->begin(), tokens->end(), "|",
                         [](const Token &token) { return token.utf8; }));
}

std::vector<strings::UniString> ChineseTokenizer::TokenizeChinese(
    const std::vector<strings::UniString> &raw_tokens) const {
  std::vector<strings::UniString> tokens;
  std::vector<std::string> cut_result;
  for (auto &raw_token : raw_tokens) {
    cut_result.clear();
    jieba_->Cut(strings::ToUtf8(raw_token), cut_result);
    for (auto &word : cut_result) {
      tokens.emplace_back(strings::MakeUniString(word));
    }
  }
  return tokens;
}

}  // namespace aurora::search