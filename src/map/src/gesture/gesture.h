#pragma once
#include "point2.h"
#include <functional>
#include <vector>
#include <mutex>
#include <thread>
#include <condition_variable>
#include <set>

namespace aurora {

enum GestureEventType
{
    GUESTURE_EVENT_PINCH_ZOOM,
    GUESTURE_EVENT_ROTATE,
    GUESTURE_EVENT_PITCH,

    GUESTURE_EVENT_COUNT

};

struct GestureResult
{
    GestureEventType event_type;

    union {
        float zoom_scale;
        float rotate_angle;
        float pitch_angle;
    };

};

using GestureResultCallback = std::function<void(const GestureResult&)>;

typedef std::shared_ptr<GestureResultCallback > GestureResultCallbackPtr;


class GestureDetector
{
    public:
        GestureDetector() ;

        virtual ~GestureDetector();

        virtual void OnTwoFingerDown(const Point2d& pos1, const Point2d& pos2);

        virtual void OnTwoFingerMove(const Point2d& current_pos1, const Point2d& current_pos2);

        virtual void OnTwoFingerUp();

        virtual void RegisterGestureCallback(GestureResultCallbackPtr gesture_result_callback);

        virtual void UnRegisterGestureCallback(GestureResultCallbackPtr gesture_result_callback);

        virtual void StopHandler();

    protected:
        void Reset2FingerVars();

        float Distance(const Point2d& pos1, const Point2d& pos2);

        float AngleRad(const Point2d& pos1, const Point2d& pos2);

        void HandleGestureEvent();

    private:
        bool tow_finger_down_ = false;
        float pinch_zoom_threshole_ = 0.0f; // 双指pinch的阈值，超过此值回调pinch zoom事件
        float rotate_threshole_ = 0.0f; // 双指旋转的阈值，超过此值回调rotate事件
        float pitch_threshole_ = 0.0f;  // 双指pitch操作，同上，同下超过此阈值才认为是pitch操作

        float tow_finger_distance_ = 0.0f ;// 双指down时初始距离
        float tow_finger_angle_ = 0.0f; // 双指down时初始向量夹角
        float tow_finger_x_distance_ = 0.0f; // 双指横向的距离
        float tow_finger_y_distance_ = 0.0f; // 双指纵向的距离

        Point2d finger_pos1_; 
        Point2d finger_pos2_;

        std::recursive_mutex callback_mutex_;
        std::set<GestureResultCallbackPtr > gesture_callback_set_;

        std::thread gesture_handler_thread_;
        std::recursive_mutex result_mutex_;
        std::condition_variable_any result_cv_;
        std::vector<GestureResult> cb_results_;
        bool handle_stop_ = false;

};


};


