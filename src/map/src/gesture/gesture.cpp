
#include "gesture.h"
#include "constants.h"


namespace aurora {
    // 默认pinch缩放变化阈值5%
    const float DEFAULT_PINCH_ZOOM_THRESHOLE = 0.05f;
    const float DEFAULT_ROTATE_THRESHOLE = 5.0 * M_PI / 180; // 5度对应的弧度
    const float DEFAULT_PITCH_THRESHOLE = 10; // 同上，同下10像素
    GestureDetector::GestureDetector()
    : tow_finger_down_(false)
    , pinch_zoom_threshole_(DEFAULT_PINCH_ZOOM_THRESHOLE)
    , rotate_threshole_(DEFAULT_ROTATE_THRESHOLE)
    , pitch_threshole_(DEFAULT_PITCH_THRESHOLE)
    {
        gesture_handler_thread_ = std::thread(&GestureDetector::HandleGestureEvent , this);
    } 

    GestureDetector::~GestureDetector()
    {
        StopHandler();
        std::lock_guard<std::recursive_mutex> lock(callback_mutex_);
        gesture_callback_set_.clear();
       
    }

    void GestureDetector::OnTwoFingerDown(const Point2d& pos1, const Point2d& pos2)
    {
        tow_finger_down_ = true;
        finger_pos1_ = pos1;
        finger_pos2_ = pos2;
        tow_finger_distance_ = Distance(pos1, pos2);
        tow_finger_angle_ = AngleRad(pos1, pos2);
        tow_finger_x_distance_ = (pos2.x() - pos1.x());
        tow_finger_y_distance_ = (pos2.y()- pos1.y());
        printf("OnTwoFingerDown :p1%f %f p2 %f %f  dis:%f, angle:%f, x dis:%f, y dis:%f\n",
            pos1.x(), pos1.y(), pos2.x(), pos2.y(), tow_finger_distance_, tow_finger_angle_, tow_finger_x_distance_, tow_finger_y_distance_
        );
    }

    void GestureDetector::OnTwoFingerMove(const Point2d& current_pos1, const Point2d& current_pos2)
    {
        if (tow_finger_down_) {
             printf("OnTwoFingerMove :p1%f %f p2 %f %f  dis:%f, angle:%f, x dis:%f, y dis:%f\n",
                current_pos1.x(), current_pos1.y(), current_pos2.x(), current_pos2.y(), tow_finger_distance_, tow_finger_angle_, tow_finger_x_distance_, tow_finger_y_distance_
            );
            // 判断是否双指pinch zoom
            if (kEpsilon < tow_finger_distance_ ) {
                auto cur_dis = Distance(current_pos1, current_pos2);
                auto scale_change = (cur_dis - tow_finger_distance_) / tow_finger_distance_;
                 printf("OnTwoFingerMove :p1%f %f p2 %f %f  cur_dis:%f, scale_change:%f\n",
                current_pos1.x(), current_pos1.y(), current_pos2.x(), current_pos2.y(), cur_dis,  scale_change
            );
                if (fabs(scale_change) > pinch_zoom_threshole_) {
                    // scale_change > 0 , zoom in  <0 zoom out
                    std::unique_lock<std::recursive_mutex> lock(result_mutex_);
                    
                    // if (scale_change > 0) {
                    //     // 通知比例尺缩小
                    // }
                    // else {
                    //     // 通知比例尺放大
                    // }
                    GestureResult zoom_result;
                    zoom_result.event_type = GUESTURE_EVENT_PINCH_ZOOM;
                    zoom_result.zoom_scale = scale_change;
                    cb_results_.push_back(zoom_result);
                    result_cv_.notify_one();
                    tow_finger_distance_= cur_dis;
                    return;
                }

            }
            if (kEpsilon < tow_finger_angle_) {
                auto cur_angle = AngleRad(current_pos1, current_pos2);
                auto angle_change = (cur_angle - tow_finger_angle_) / tow_finger_angle_;
                printf("OnTwoFingerMove :p1%f %f p2 %f %f  cur_angle:%f, angle_change:%f\n",
                    current_pos1.x(), current_pos1.y(), current_pos2.x(), current_pos2.y(), cur_angle,  angle_change
                );
                if (fabs(angle_change) > rotate_threshole_) {
                    // 通知旋转delta cur_angle - tow_finger_angle_、
                    std::unique_lock<std::recursive_mutex> lock(result_mutex_);
                    GestureResult rotate_result;
                    rotate_result.event_type = GUESTURE_EVENT_ROTATE;
                    rotate_result.rotate_angle = angle_change;
                    cb_results_.push_back(rotate_result);
                    result_cv_.notify_one();
                    tow_finger_angle_ = cur_angle;
                    return;
                }
            }
            
            if (tow_finger_x_distance_ > kEpsilon) {
                auto cur_x_dis = current_pos2.x() - current_pos1.x();
                auto x_change_scale = (cur_x_dis - tow_finger_x_distance_) / tow_finger_x_distance_;
                if (fabs(x_change_scale) < pitch_threshole_) {  // x变化极小
                    auto cur_y_dis = current_pos2.y() - current_pos1.y();
                    if ( tow_finger_y_distance_< kEpsilon) {
                        auto yoffset = current_pos2.y() - finger_pos2_.y();
                        if (yoffset >= pitch_threshole_) {
                             std::unique_lock<std::recursive_mutex> lock(result_mutex_);
                            GestureResult pitch_result;
                            pitch_result.event_type = GUESTURE_EVENT_PITCH;
                            auto pitch_angle = ((int)yoffset % 100) * M_PI /180;
                            pitch_result.pitch_angle = pitch_angle;
                            cb_results_.push_back(pitch_result);
                            result_cv_.notify_one();
                            tow_finger_x_distance_ = cur_x_dis;
                            tow_finger_y_distance_ = cur_y_dis;
                            return;
                        }
                    }
                    
                    
                }
            }
        }

    }

    void GestureDetector::OnTwoFingerUp()
    {
       Reset2FingerVars();
    }

    void  GestureDetector::Reset2FingerVars() 
    {
        tow_finger_down_ = false;
        tow_finger_distance_ = 0.0f ;// 双指down时初始距离
        tow_finger_angle_ = 0.0f; // 双指down时初始向量夹角
        tow_finger_x_distance_ = 0.0f; // 双指横向的距离
        tow_finger_y_distance_ = 0.0f; // 双指纵向的距离

        finger_pos1_ = {0.0f, 0.0f}; 
        finger_pos2_ = {0.0f, 0.0f};
    }

    float GestureDetector::Distance(const Point2d& pos1, const Point2d& pos2)
    {
        float distance = 0.0f;
        distance = pos1.Distance(pos2);

        return distance;
    }

    float GestureDetector::AngleRad(const Point2d& pos1, const Point2d& pos2)
    {
        float angle = 0.0f;
        // 计算p2->p1 向量与（0, y）正方向的角度，即与y轴正方形的夹角
        angle = atan2(pos2.x() - pos1.x(), pos2.y() - pos1.y());

        return angle;
    }

    void GestureDetector::RegisterGestureCallback(GestureResultCallbackPtr gesture_result_callback)
    {
        if (gesture_result_callback != nullptr) {
            // 保证不重复添加
            // printf("+++++++RegisterGestureCallback add cb %p\n", gesture_result_callback.get());
            std::lock_guard<std::recursive_mutex> lock(callback_mutex_);
            if (gesture_callback_set_.count(gesture_result_callback) == 0) {
                    gesture_callback_set_.insert(gesture_result_callback);
                }
        }
    }

    void GestureDetector::UnRegisterGestureCallback(GestureResultCallbackPtr gesture_result_callback)
    {
        if (gesture_result_callback != nullptr) {
            std::lock_guard<std::recursive_mutex> lock(callback_mutex_);
            if (gesture_callback_set_.count(gesture_result_callback) > 0) {
                    gesture_callback_set_.erase(gesture_result_callback);
                }
        }
    }

    void GestureDetector::HandleGestureEvent()
    {
        while(!handle_stop_) {
            std::unique_lock<std::recursive_mutex> lock(result_mutex_);
            result_cv_.wait(lock, [this] { return !cb_results_.empty() || handle_stop_; });
            
            if (handle_stop_) {
                break;
            }
            std::set<GestureResultCallbackPtr> gesture_callback_list;
            {
                std::lock_guard<std::recursive_mutex> lock(callback_mutex_);
                gesture_callback_list = gesture_callback_set_;
            }
        
            
            for (auto result: cb_results_) {
                for(auto& cb: gesture_callback_list) {
                    printf("-----------cb:%p\n", cb.get());
                    (*cb)(result);
                }
            }
            cb_results_.clear();
        }
        
        
    }
    

    void GestureDetector::StopHandler()
    {
        std::unique_lock<std::recursive_mutex> lock(result_mutex_);
        if (!handle_stop_) {
            handle_stop_ = true;
            result_cv_.notify_one();
            if (gesture_handler_thread_.joinable()) {
                gesture_handler_thread_.join();
            }
        }
        
    }
    

}