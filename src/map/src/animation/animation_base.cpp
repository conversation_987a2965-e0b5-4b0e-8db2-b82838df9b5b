#include "animation_base.h"

#include <cmath>

namespace aurora {

AnimationBase::AnimationBase(AnimationType t, Duration d, bool auto_start)
    : type_(t), duration_(d) {
  if (auto_start) {
    start_time_ = Clock::now();
    end_time_ = start_time_ + d;
  }
}

void AnimationBase::StartAnimation(bool repeat_flag, uint32_t repeat_count,
                                   const TimePoint& start_time) {
  start_time_ = start_time;
  need_repeat = repeat_flag;
  if (repeat_flag) {
    if (repeat_count > 0) {
      end_time_ = start_time_ + (duration_ * repeat_count);
    }
  } else {
    end_time_ = start_time_ + duration_;
  }
}

void AnimationBase::SkipToEnd() { skip_flag_ = true; }

void AnimationBase::Restart(const TimePoint& start_time) {
  if (end_time_) {
      end_time_ = start_time + (end_time_.value() - start_time_);
  }
  start_time_ = start_time;
  
}

double AnimationBase::GetProcess(const TimePoint& current_time) {
  if (IsFinished(current_time) || duration_.count() == 0) {
    next_frame_draw_ = false;
    return 1.0;
  }
  Duration d = current_time - start_time_;
  double raw_progress = std::chrono::duration_cast<Milliseconds>(d).count() * 1.0 /
                        std::chrono::duration_cast<Milliseconds>(duration_).count();
  if (need_repeat) {
    next_frame_draw_ = true;
    return raw_progress - static_cast<int>(raw_progress);
  } else {
    if (raw_progress >= 1.0) {
      next_frame_draw_ = false;
    } else {
      next_frame_draw_ = true;
    }
    return std::clamp(raw_progress, 0.0, 1.0);
  }
}

bool AnimationBase::IsFinished(const TimePoint& current_time) {
  if (skip_flag_) {
    return true;
  }
  if (!end_time_) {
    return !need_repeat;
  }
  return current_time >= end_time_;
}

bool AnimationBase::NeedNextFrameDraw() { return next_frame_draw_; }

AnimationBase& AnimationBase::operator=(const AnimationBase& rhs) {
  type_ = rhs.type_;
  duration_ = rhs.duration_;
  start_time_ = Clock::now();
  end_time_ = start_time_ + duration_;
  skip_flag_ = rhs.skip_flag_;
  need_repeat = rhs.need_repeat;
  next_frame_draw_ = rhs.next_frame_draw_;
  return *this;
}

}  // namespace aurora


