#ifndef MAP_RENDER_ANIMATION_ANIMATION_FUNC_H
#define MAP_RENDER_ANIMATION_ANIMATION_FUNC_H
#include <cstdint>
#include <functional>
#include <math.h>
#include <map>
#include <unitbezier.h>
#include "constants.h"


namespace aurora {
namespace func {

const UnitBezier DEFAULT_Bezier_EASE = { 0, 0, 0.58, 1 };  //  default贝塞尔参数
const UnitBezier EaseOutQuad_Bezier = {0.25, 0.46, 0.45, 0.94 };  //  轻量惯性贝塞尔参数（EaseOutQuad）
const UnitBezier EaseOutCubic_Bezier = { 0.215, 0.61, 0.355, 1 };  //  标准惯性贝塞尔参数（EaseOutCubic，类似高德默认）
const UnitBezier EaseOutQuart_Bezier = { 0.165, 0.84, 0.44, 1 }; //  丝滑惯性贝塞尔参数（EaseOutQuart，类似Google Map）
const UnitBezier EaseOutExpo_Bezier = { 0.19, 1, 0.22, 1 };  //  强惯性贝塞尔参数（EaseOutExpo）

using AnimationFunc = std::function<double(double, double, double)>;

const AnimationFunc linear_func = [](double start, double end, double progress) -> double {
  return start + progress * (end - start);
};

const AnimationFunc EaseOutQuadFunc = [](double start, double end, double progress) -> double {
  auto bezier_progress =  EaseOutQuad_Bezier.solve(progress, 1e-3);
  return start + bezier_progress * (end - start);
};

const AnimationFunc EaseOutCubicFunc = [](double start, double end, double progress) -> double {
  auto bezier_progress = EaseOutCubic_Bezier.solve(progress, 1e-3);
  return start + bezier_progress * (end - start);
};

const AnimationFunc EaseOutQuartFunc = [](double start, double end, double progress) -> double {
  auto bezier_progress = EaseOutQuart_Bezier.solve(progress, 1e-3);
  return start + bezier_progress * (end - start);
};

const AnimationFunc EaseOutExpoFunc = [](double start, double end, double progress) -> double {
  auto bezier_progress = EaseOutExpo_Bezier.solve(progress, 1e-3);
  return start + bezier_progress * (end - start);
};

const AnimationFunc EaseDefaultBezierFunc = [](double start, double end, double progress) -> double {
  auto bezier_progress = DEFAULT_Bezier_EASE.solve(progress, 1e-3);
  return start + bezier_progress * (end - start);
};


const AnimationFunc damped_oscillation_func = [](double offset, double end,
                                                 double progress) -> double {
  double y = std::sin(4 * 2 * kPiD * progress) * std::pow(2, -progress * 3.25);
  return y * offset;
};

}  // namespace func

}  // namespace aurora
#endif  // MAP_RENDER_ANIMATION_ANIMATION_FUNC_H
