#ifndef MAP_RENDER_ANIMATION_ANIMATION_BASE_H
#define MAP_RENDER_ANIMATION_ANIMATION_BASE_H
#include <chrono>
#include <cstdint>
#include <memory>
#include <chrono>
#include <string>
#include <functional>
#include <experimental/optional>


namespace aurora {

template <typename T>
using optional = std::experimental::optional<T>;

using Clock = std::chrono::steady_clock;

using Seconds = std::chrono::seconds;
using Milliseconds = std::chrono::milliseconds;

using TimePoint = Clock::time_point;
using Duration  = Clock::duration;

enum AnimationType {
    kAnimationTypeNone = 0x0,
    kAnimationTypeFlyTo = 0x1,
    kAnimationTypeZoomTo = 0x2,
    kAnimationTypeMoveTo = 0x4,
    kAnimationTypeRollTo = 0x8,
    kAnimationTypeRotateTo = 0x10,

    kAnimationTypeAlpha = 0x80000000,
    kAnimationTypeSize,
    kAnimationTypeDegree,
};

class AnimationBase {
public:
  AnimationBase() = default;
  AnimationBase(AnimationType t, Duration d, bool auto_start = true);
  void StartAnimation(bool repeat_flag = false, uint32_t repeat_count = 0,
                      const TimePoint& start_time = Clock::now());
  void SkipToEnd();
  void Restart(const TimePoint& start_time = Clock::now());
  virtual double GetProcess(const TimePoint& current_time = Clock::now());
  bool IsFinished(const TimePoint& current_time = Clock::now());
  bool NeedNextFrameDraw();

  AnimationType GetAnimationType() const {
    return type_;
  }
  Duration GetDuration() const {
    return duration_;
  }

  AnimationBase& operator = (const AnimationBase& rhs) ;

private:
  AnimationType type_;
  TimePoint start_time_;
  optional<TimePoint> end_time_;
  Duration duration_{0};
  bool skip_flag_{false};
  bool need_repeat{false};
  bool next_frame_draw_{false};
};

using AnimationPtr = std::shared_ptr<AnimationBase>;

}  // namespace aurora
#endif  // MAP_RENDER_ANIMATION_ANIMATION_BASE_H
