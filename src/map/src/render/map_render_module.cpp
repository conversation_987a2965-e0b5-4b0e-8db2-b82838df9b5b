/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

 #include "map_render_module.h"
 #include "map_render_service.h"
 #include "map_data_provider.h"

 namespace aurora {
 
    IMapRenderService::IMapRenderService() {}
    
    IMapRenderService::~IMapRenderService() {}
     
    int32_t IMapRenderService::Init(const AuroraMapConfig& map_config, void* themeconfig)
    {
        render_service_ = std::make_unique<MapRenderService>(map_config);
        auto dataprovider_shared = std::make_shared<MapDataProvider>(map_config.data_path_);
        if (nullptr == dataprovider_shared || map_config.data_path_.empty()) {
            printf("Init FAILED!!!! new dataprovider is null or map_config.data_path_ is empty,  CHECK IT\n");
            return -1;
        }

        return render_service_->Init(dataprovider_shared, themeconfig);
    }

    int32_t IMapRenderService::SetScreenSize(uint32_t width, uint32_t height)
    {
        if (render_service_ != nullptr)
        {
            render_service_->SetScreenSize(width, height);
        }
        return 0;
    }
 
    void IMapRenderService::Destory()
    {
        if (render_service_ != nullptr)
        {
            render_service_->Destory();
        }
    }
 
    int32_t IMapRenderService::SetMapCenter(double lon, double lat, uint32_t animation_duration_ms)
    {
        if (render_service_ != nullptr)
        {
            return render_service_->SetMapCenter(lon, lat, animation_duration_ms);
        }
        return 0;
    }
 
    Point2d IMapRenderService::GetMapCenter()
    {
        Point2d center;
        if (render_service_ != nullptr)
        {
            center = render_service_->GetMapCenter();

        }
        return center;
    }
     
    int32_t IMapRenderService::SetMapScale(double scale, uint32_t animation_duration_ms)
    {
        if (render_service_ != nullptr)
        {
            return render_service_->SetMapScale(scale, animation_duration_ms);
        }
        return 0;
    }
 
    void IMapRenderService::RenderMap()
    {
        if (render_service_ != nullptr)
        {
            render_service_->RenderMap();
        }
    }
 
    int32_t IMapRenderService::SetMapRotation(float rot, uint32_t animation_duration_ms)
    {
        if (render_service_ != nullptr)
        {
            render_service_->SetMapRotation(rot, animation_duration_ms);
        }
        return 0;
    }
 
    Point2d IMapRenderService::ScreenToMap(const Point2d& pt)
    {
        Point2d pt_ret; 
        if (render_service_!= nullptr)
        {
            pt_ret = render_service_->ScreenToMap({pt.x(), pt.y()});
        }
        return pt_ret;
    }
 
    Point2d IMapRenderService::MapToScreen(const Point2d& geo)
    {
        Point2d pt; 
        if (render_service_!= nullptr)
        {
            auto ret = render_service_->MapToScreen(geo);
            pt = {ret.x, ret.y};
        }
        return pt;
    }

    void IMapRenderService::MoveMap(double delta_x, double delta_y, bool move_end, uint32_t animation_duration_ms)
    {
        if (render_service_!= nullptr)
        {
            render_service_->MoveMap(delta_x, delta_y, move_end, animation_duration_ms);
        }
    }

    float IMapRenderService::GetMapScale()
    {
        if (render_service_!= nullptr)
        {
            return render_service_->GetMapScale();
        }
        return 0;
    }

    void IMapRenderService::SetMapPitch(float pitch, uint32_t animation_duration_ms)
    {
        if (render_service_!= nullptr)
        {
            render_service_->SetMapPitch(pitch, animation_duration_ms);
        }
    }

    float IMapRenderService::GetMapPitch()
    {
        if (render_service_!= nullptr)
        {
            return render_service_->GetMapPitch();
        }
        return 0;
    }

    void IMapRenderService::FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms)
    {
        if (render_service_!= nullptr)
        {
            render_service_->FlyTo(lon, lat, dest_scale, animation_duration_ms);
        }
    }

    void IMapRenderService::SetPath(uint32_t type, std::vector<Point2d>& path)
    {
        if (render_service_!= nullptr)
        {
            render_service_->SetPath(type, path);
        }
    }

    void IMapRenderService::ClearPath(uint32_t type)
    {
        if (render_service_!= nullptr)
        {
            render_service_->ClearPath(type);
        }
    }

    int32_t IMapRenderService::SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name)
    {
        if (render_service_!= nullptr)
        {
            return render_service_->SetMarkInfo(mark_type, mark_lnglat_pos, mark_anchor, mark_name);
        }
        return -1;
    }

    void IMapRenderService::UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name)
    {
        if (render_service_!= nullptr)
        {
            render_service_->UpdateMarkInfo(mark_type, mark_id, mark_anchor, mark_name);
        }
    }

    void IMapRenderService::ClearMark(uint16_t mark_type, int32_t mark_id)
    {
        if (render_service_!= nullptr)
        {
            render_service_->ClearMark(mark_type, mark_id);
        }
    }

    void IMapRenderService::UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree)
    {
        if (render_service_!= nullptr)
        {
            render_service_->UpdateMarkInfo(mark_type, mark_id, mark_lnglat_pos, degree);
        }
    }

    AABB2<Point2d> IMapRenderService::GetMapScreenBoudingBox() 
    {
        AABB2<Point2d> ret;
        if (render_service_!= nullptr)
        {
            ret = render_service_->GetMapScreenBoudingBox();
        }
        return ret;
    }

    void IMapRenderService::OnClick(const Point2d& screen_pos, ClickCallback callback)
    {
        if (render_service_!= nullptr)
        {
            render_service_->OnClick(screen_pos, callback);
        }
    }

    void IMapRenderService::OnLongClick(const Point2d& screen_pos, ClickCallback callback)
    {
        if (render_service_!= nullptr)
        {
            render_service_->OnLongClick(screen_pos, callback);
        }
    }

    void IMapRenderService::OnTwoFingerDown(const Point2d& pos1, const Point2d& pos2)
    {
        if (render_service_!= nullptr)
        {
            render_service_->OnTwoFingerDown(pos1, pos2);
        }
    }

    void IMapRenderService::OnTwoFingerUp()
    {
        if (render_service_!= nullptr)
        {
            render_service_->OnTwoFingerUp();
        }
    }

    void IMapRenderService::OnTwoFingerMove(const Point2d& current_pos1, const Point2d& current_pos2)
    {
        if (render_service_!= nullptr)
        {
            render_service_->OnTwoFingerMove(current_pos1, current_pos2);
        }
    }


    int32_t IMapRenderService::DrawMarkOnLayer(OverlayLayer layer, const MarkDrawData& mark_data, 
        const CollisionLayer collision_layer)
    {
        if (render_service_!= nullptr)
        {
            return render_service_->DrawMarkOnLayer(layer, mark_data, collision_layer);
        }
        return -1;
    }

    bool IMapRenderService::DrawPolygonOnLayer(OverlayLayer layer, const PolygonDrawData& polygon_data,
      const CollisionLayer collision_layer)
    {
        if (render_service_!= nullptr)
        {
            return render_service_->DrawPolygonOnLayer(layer, polygon_data, collision_layer);
        }
        return false;
    }

    bool IMapRenderService::DrawLineOnLayer(OverlayLayer layer, const LineDrawData& line_data,
      const CollisionLayer collision_layer)
    {
        if (render_service_!= nullptr)
        {
            return render_service_->DrawLineOnLayer(layer, line_data, collision_layer);
        }
        return false;
    }

    void IMapRenderService::ClearLayer(OverlayLayer layer)
    {
        if (render_service_!= nullptr)
        {
            render_service_->ClearLayer(layer);
        }
    }

    void IMapRenderService::ReloadDisplayData(const std::string display_data_path)
    {
        if (render_service_!= nullptr)
        {
            render_service_->ReloadDisplayData(display_data_path);
        }
    }

 } //namespace 
 /* EOF */
