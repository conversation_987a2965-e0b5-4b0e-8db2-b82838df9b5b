/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_manager.h
 * @brief Declaration file of class MapRenderManager.
 * @attention used for C/C++ only.
 */

#ifndef MAP_SRC_RENDER_MAPRENDERMANAGER_H_
#define MAP_SRC_RENDER_MAPRENDERMANAGER_H_

#include <memory>
#include <list>
#include "i_map_render_manager.h"
#include "util_gl_type.h"
#include <vector>
#include <unordered_set>
#include <unordered_map>
#include <string>
#include <mutex>
#include "render_over_layer.h"
#include "util_tile_define.h"
#include "render_effect_layer.h"
#include "gesture.h"

namespace aurora {

class MapRenderState;
class MapRenderThemeIF;
class RenderLayerIF;
class IMapRenderDataManager;
class IMapRenderScene;
struct CacheTile;

struct TileImage;
using TileImagePtr = std::shared_ptr<TileImage>;

using CacheTilePtr = std::shared_ptr<CacheTile>;

/**
* class breif description
*
* MapRenderManager
*
*/
class MapRenderManager: 
    public IMapRenderManager {

public:
    MapRenderManager(const std::string& default_font_path, const std::string& default_font_name);              
    virtual ~MapRenderManager();
    
    /**
     * Initialize resource
     */
    virtual bool Init(MapDataProviderPtr provider, MapRenderThemeIF* theme);

    /**
     * Destory resource
     */
    virtual void Destory();

    virtual void RenderMap();

    virtual void SetMapScale(float scale, const uint32_t animation_duration = 0);

    virtual void SetMapCenter(double lon, double lat, const uint32_t animation_duration = 0);

    virtual void SetMapRotation(float rot, const uint32_t animation_duration = 0);

    virtual float GetMapScale();

    virtual Lonlat GetMapCenter();

    virtual float GetMapRotation();

    virtual Point2d ScreenToMap(const Vector2<int32_t>& pt);

    virtual Vector2<int32_t> MapToScreen(const Point2d& geo);

    virtual void SetMapThemeByLayerId();

    virtual void RenderEmbedGeoObj();

    virtual void SetScreenSize(int32_t width, int32_t height);

    // 注意：地图内部左下角为0,0点，所以如果窗口给的y是按左上角做为0，0点，delta_y应该取反传入
    virtual void MoveMap(double delta_x, double delta_y, const bool move_end = false, const uint32_t animation_duration = 0);

    virtual void SetMapPitch(float pitch, const uint32_t animation_duration = 0);

    virtual float GetMapPitch();

    //  flyto 到指定经纬度和比例尺，默认最少5s动画
    virtual void FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms);

    virtual void SetPath(uint32_t type, std::vector<Point2d>& path);

    virtual void ClearPath(uint32_t type);

    virtual int32_t SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name);

    virtual void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name);

    virtual void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree = 0.f );

    virtual void ClearMark(uint16_t mark_type, int32_t mark_id);

    virtual AABB2<Point2d> GetMapScreenBoudingBox();

    // 点击事件相关，目前点击click/longclick由外部调用，后续考虑内部接受点击事件后自己判断回调，外部注册clickcallback
    virtual void OnClick(const Point2d& screen_pos, ClickCallback callback);

    virtual void OnLongClick(const Point2d& screen_pos, ClickCallback callback);


    // 双指移动事件，目前由外部调用，后续考虑内部接受点击事件后自己判断回调，外部注册clickcallback
    virtual void OnTwoFingerDown(const Point2d& pos1, const Point2d& pos2);

    virtual void OnTwoFingerMove(const Point2d& current_pos1, const Point2d& current_pos2);

    virtual void OnTwoFingerUp();

    /// @brief 在指定层绘制标记（覆盖该层原有同类型标记）
    /// @param layer 目标层次（需在 OverlayLayer 范围内）
    /// @param mark_data 标记绘制数据
    /// @return 标记ID（>=0成功，<0失败）
    virtual int32_t DrawMarkOnLayer(OverlayLayer layer, const MarkDrawData& mark_data, 
        const CollisionLayer collision_layer = CollisionLayer::kMarkCollisionLayer);

    /// @brief 在指定层绘制多边形（覆盖该层原有同类型多边形）
    /// @param layer 目标层次
    /// @param polygon_data 多边形绘制数据
    virtual bool DrawPolygonOnLayer(OverlayLayer layer, const PolygonDrawData& polygon_data,
      const CollisionLayer collision_layer = CollisionLayer::kOverlayCollisionLayer);

    /// @brief 在指定层绘制线（覆盖该层原有同类型线）
    /// @param layer 目标层次
    /// @param line_data 线绘制数据
    virtual bool DrawLineOnLayer(OverlayLayer layer, const LineDrawData& line_data,
      const CollisionLayer collision_layer = CollisionLayer::kOverlayCollisionLayer);

    /// @brief 清除指定层的所有绘制内容（标记、多边形、线）
    /// @param layer 目标层次
    virtual void ClearLayer(OverlayLayer layer);

    /// @brief 加载新数据
    virtual void ReloadDisplayData(const std::string display_data_path);

    virtual void GestureDetectorCallback(const GestureResult& result);

public:
    std::shared_ptr<IMapRenderScene> GetRenderScene() { return scene_; }

private:
    void UpdateCurrentRenderTiles(const AABB2<Point2d>& predict_boundingbox = {{0.0,0.0}, {0.0,0.0}});

    void GenerateBuildingData(std::shared_ptr<CacheTile> cache_tile);

    CacheTilePtr ProcessAndCacheTile(TileImagePtr tileImage, uint32_t cur_frame_z);

private:
    std::shared_ptr<IMapRenderScene>  scene_;
    MapRenderThemeIF*   render_theme_;
    IMapRenderDataManager* data_manager_;
    std::unordered_set<TileID, TileIDHashFunc> current_render_tile_ids_;  // 渲染列表（快速查找）
    std::unordered_map<TileID, std::shared_ptr<CacheTile>, TileIDHashFunc> current_render_tile_datas_;  // 渲染数据（ID关联）
    std::recursive_mutex cache_tile_mutex_;
    std::vector<TileImagePtr> background_cached_tileImage_; // 当前帧不画，但是需要缓存起来，下帧如果空闲则处理
    std::string default_fontPath_;
    std::string default_fontName_;
  
    RenderEffectLayer effect_layer_;

    std::shared_ptr<GestureDetector> gesture_detector_ = nullptr;
};
    
} //namespace 

#endif //MAPRENDER_RENDERMANAGER_H_
/* EOF */
