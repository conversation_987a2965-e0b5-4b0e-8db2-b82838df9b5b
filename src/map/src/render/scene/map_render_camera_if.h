/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_camera_if.h
 * @brief Declaration file of class MapRenderCameraIF
 * @attention used for C/C++ only.
 */

#ifndef MAPRENDER_MAPRENDERCAMERA_IF_H_
#define MAPRENDER_MAPRENDERCAMERA_IF_H_

#include "render_geometry_type.h"
#include "util_matrix4.h"
#include "util_tile_define.h"
#include "util_basic_type.h"
#include <memory>
#include <list>
#include <map>
#include "aabb2.h"
#include "camera_animation.h"

namespace aurora {

    
 namespace parser 
 {
    struct DisplayTileID;

 };
/**
* class breif description
*
* MapRenderCameraIF
*
*/
class MapRenderCameraIF {

public:           
    virtual ~MapRenderCameraIF() = default;

    /// @brief 设置视口大小
    /// @param left 左上角坐标
    /// @param top  左上角坐标
    /// @param w    视口宽
    /// @param h    视口高
    virtual void SetViewport(int left, int top, int w, int h) = 0;

    /// @brief 获取投影矩阵
    /// @param projViewMatrix  投影矩阵
    /// @returns 成功返回true，失败返回false
    virtual bool GetProjViewMatrix(Matrix4<double>& projViewMatrix, const TimePoint& now = Clock::now()) = 0;

    /// @brief 获取正投影矩阵
    /// @param orthViewMatrix  投影矩阵
    /// @returns 成功返回true，失败返回false
    virtual bool GetOrthViewMatrix(Matrix4<double>& orthViewMatrix, const TimePoint& now = Clock::now()) = 0;


    /// @brief 设置地图比例尺
    /// @param scale 要设置的地图比例尺
    virtual void SetMapScale(float scale, const CameraAnimation& options = {}) = 0;

    /// @brief 设置地图中心点的经纬度
    /// @param lon 地图中心点的经度
    /// @param lat 地图中心点的纬度
    virtual void SetMapCenter(double lon, double lat, const CameraAnimation& options = {}) = 0;

    /// @brief 设置地图的旋转角度
    /// @param rot 地图的旋转角度，单位为度
    virtual void SetMapRotation(float rot, const CameraAnimation& options = {}) = 0;

    /// @brief 获取地图的旋转角度
    /// @return 地图的旋转角度，单位为度
    virtual float GetMapRotation(const TimePoint& now = Clock::now()) = 0;

    /// @brief 设置偏航角
    /// @param yaw_angle 偏航角，单位为度
    virtual void SetYawAngle(float yaw_angle, const CameraAnimation& options = {}) = 0;

    /// @brief 设置俯仰角， 当设置角度有时，忽略设置的俯仰角模式
    /// @param pitch_angle 俯仰角，单位为度
    virtual void SetPitchAngle(float pitch_angle, const CameraAnimation& options = {}) = 0;  

    /// @brief 设置俯仰角模式
    /// @param pitch_mode 俯仰角模式，类型为 PitchAngleMode
    virtual void SetPitchMode(const PitchAngleMode& pitch_mode, const CameraAnimation& options = {}) = 0;

    /// @brief 获取当前地图比例尺
    /// @return 当前地图比例尺
    virtual float GetMapScale(const TimePoint& now = Clock::now()) = 0;

    /// @brief 获取地图中心点的经纬度
    /// @return 地图中心点的经纬度，类型为 Lonlat
    virtual Lonlat GetMapCenter(const TimePoint& now = Clock::now()) = 0;

    /// @brief 获取相机设置完相关的参数后，更新相机参数对对应的matrix，不用每次重新计算
    virtual void Update() = 0;

    virtual void SetScreenSize(uint32_t width, uint32_t height, bool left_bottom_0_0 = true) = 0;

    virtual void GetViewSize(uint32_t& width, uint32_t& height) = 0;

    virtual void OnMove(float dx, float dy, const bool move_end = false, const CameraAnimation& options = {}) = 0;

    virtual bool Screen2World(double x, double y, double& world_x, double& world_y) = 0;
    
    virtual bool World2Screen(double world_x, double world_y, double& x, double& y) = 0;

    virtual bool MercatorWorld2Screen(double world_x, double world_y, double& x, double& y) = 0;

    virtual void SetDpi(uint16_t dpi) = 0;

    virtual bool IsAnimationFinished(const TimePoint& now = Clock::now()) = 0;

    virtual float GetMapPitchAngle(const TimePoint& now = Clock::now()) = 0;

    virtual void FlyTo(double lon, double lat, unsigned int dest_scale, CameraAnimation& options) = 0;

    virtual void SetMinScale(uint32_t min_scale) = 0;

    virtual void SetMaxScale(uint32_t max_scale) = 0;

    virtual uint32_t GetMinScale() = 0;

    virtual uint32_t GetMaxScale() = 0;

    virtual AABB2<Point2d> GetMapRange() = 0;

    virtual void SetSkyBoxHeight(uint32_t height) = 0;

    virtual uint32_t GetSkyBoxHeight() = 0;

    // 有天空盒的时候，有效view高度需要screen - skybox
    virtual uint32_t GetEffectiveHeight() = 0;

    virtual void SetShowSkyBoxPitchAngle(float pitch_angle) = 0;

    virtual float GetShowSkyBoxPitchAngle() = 0;

    virtual CameraAnimation GetCurCameraAnimation() = 0;

 };

 using CameraPtr = std::shared_ptr<MapRenderCameraIF>;
} //namespace 

#endif //MAPRENDER_MAPRENDERCAMERA_IF_H_
/* EOF */
