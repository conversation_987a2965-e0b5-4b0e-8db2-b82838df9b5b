
#pragma once

#include <cmath>  // 新增：用于std::round和std::clamp
#include "unitbezier.h"
#include "point2.h"
#include "scale_converter.h"
#include <memory>  // Add for std::shared_ptr
#include "animation_base.h"
#include "animation_func.h"

namespace aurora {



// 新增：FlyTo动画状态记录
struct FlyToState {
    PointXY<double> target_center;       // 最终目标中心点
    unsigned int target_scale;          // 最终目标比例尺
    double max_zoom;                    // 放大阶段的最大比例尺（取m_maxZoom_）
};
    

/** Various options for describing a transition between viewpoints with
    animation. All fields are optional; the default values depend on how this
    struct is used. */
class CameraAnimation : public AnimationBase {


    public:

    /** Average velocity of a flyTo() transition, measured in screenfuls per
        second, assuming a linear timing curve.

        A <i>screenful</i> is the visible span in pixels. It does not correspond
        to a fixed physical distance but rather varies by zoom level. */
    optional<double> velocity;

    /** Zero-based zoom level at the peak of the flyTo() transition’s flight
        path. */
    optional<double> minZoom = MIN_VALID_TILE_LOGIC_LEVEL;

    /** The easing timing curve of the transition. */
    // Change from optional<UnitBezier> to optional<shared_ptr<UnitBezier>>
    func::AnimationFunc easing = func::EaseDefaultBezierFunc;

    /** A function that is called on each frame of the transition, just before a
        screen update, except on the last frame. The first parameter indicates
        the elapsed time as a percentage of the duration. */
    std::function<void(double)> transitionFrameFn;

    /** A function that is called once on the last frame of the transition, just
        before the corresponding screen update. */
    std::function<void()> transitionFinishFn;

    /** Creates an animation with no options specified. */
    CameraAnimation() = default;

    /** Creates an animation with the specified duration. */
    CameraAnimation(Duration d, AnimationType type_value)
        : AnimationBase(type_value, d) {

        }

    CameraAnimation(Duration d, double start_value, double end_value, AnimationType type_value )
        : AnimationBase(type_value, d) {
            if (type_value == kAnimationTypeZoomTo) {
                animate_scale = {start_value, end_value};
            }
            else if (type_value == kAnimationTypeRollTo) {
                animate_roll = {start_value, end_value};
            }
            else if (type_value == kAnimationTypeRotateTo) {
                animate_rotate = {start_value, end_value};
            }

        }

    CameraAnimation(Duration d, PointXY<double> start, PointXY<double> end, AnimationType type_value)
        : AnimationBase(type_value, d) 
    {
        if ((type_value & kAnimationTypeMoveTo) == kAnimationTypeMoveTo || (type_value & kAnimationTypeFlyTo) == kAnimationTypeFlyTo) {
            animate_pos = {start, end};
        }
    }

    CameraAnimation& operator=(const CameraAnimation& rhs) {

        if (rhs.velocity.operator bool()){
            velocity = rhs.velocity;
        }
        if (rhs.minZoom.operator bool()) {
            minZoom = rhs.minZoom;
        }
        
        
        easing = rhs.easing;
        
        transitionFinishFn = rhs.transitionFinishFn;
        transitionFrameFn = rhs.transitionFrameFn;
        // start_time_ = Clock::now();
        // // 新增：检查 rhs.duration 是否有效，避免 value() 未定义行为
        // if (rhs.duration) {
        //     end_time = start_time + rhs.duration.value();
        // } else {
        //     end_time = start_time;  // 无有效时长时，动画立即结束
        // }
        animate_pos = rhs.animate_pos; // <start_pos, end_pos>
        animate_scale = rhs.animate_scale; // <start_scale, end_scale>
        animate_rotate = rhs.animate_rotate; // <start_rorate, end_rotate>
        animate_roll = rhs.animate_roll; // <start_roll, end_roll>
        AnimationBase::operator=(rhs);
        return *this;
    };


    void SetAnimatePos(PointXY<double> start, PointXY<double> end) {
        animate_pos = {start, end};
    }

    bool GetAnimatePos(PointXY<double>& pos, const TimePoint& now = Clock::now()) {
        auto type = GetAnimationType();
        if ((type & kAnimationTypeMoveTo) != kAnimationTypeMoveTo && (type & kAnimationTypeFlyTo) != kAnimationTypeFlyTo) {
            return false;
        }
         // 获取当前5%档位的进度值
        double stepped_progress = GetProcess(now);
        
        // double progress;
        // // 检查是否需要使用缓存（仅当非结束状态且档位未变化时）
        // if (stepped_progress < 1.0 && std::abs(stepped_progress - pos_cache.last_stepped_progress) < 1e-6) {
        //     // 档位未变化，使用缓存的调整后progress
        //     progress = pos_cache.cached_adjusted_progress;
        // } else {
        //     // 重新计算调整后的progress（应用缓动曲线）
        //     if (easing.operator bool()) {
        //         // Update to dereference shared_ptr
        //         progress = easing.value()->solve(stepped_progress, 1e-3);
        //         progress = std::round(progress * 20) / 20.0;  // 20 = 1/0.05
        //     } else {
        //         progress = stepped_progress;
        //     }
        //     // 更新缓存（仅当非结束状态时）
        //     if (stepped_progress < 1.0) {
        //         pos_cache.last_stepped_progress = stepped_progress;
        //         pos_cache.cached_adjusted_progress = progress;
        //     } else {
        //         // 动画结束，重置缓存以便下次使用
        //         pos_cache.last_stepped_progress = -1.0;
        //     }
        // }
        
        // 根据调整后的进度计算当前位置
        auto x = easing(animate_pos.first.x(), animate_pos.second.x(), stepped_progress);
        auto y = easing(animate_pos.first.y(), animate_pos.second.y(), stepped_progress);
        pos.set_x(x);
        pos.set_y(y);
        // printf("GetAnimatePos type:%d animate_pos:%f, %f progress:%f\n", type, pos.x(), pos.y(), progress);
        return true;
    }

    void SetAnimateScale(double start, double end) {
        animate_scale = {start, end};
    }

    bool GetAnimateScale(double& scale, const TimePoint& now = Clock::now()) {
        auto type = GetAnimationType();
        if ((type & kAnimationTypeZoomTo) != kAnimationTypeZoomTo && (type & kAnimationTypeFlyTo) != kAnimationTypeFlyTo) {
            return false;
        }
         // 获取当前5%档位的进度值
        double stepped_progress = GetProcess(now);
        
        // double progress;
        // // 检查是否需要使用缓存
        // if (stepped_progress < 1.0 && std::abs(stepped_progress - scale_cache.last_stepped_progress) < 1e-6) {
        //     progress = scale_cache.cached_adjusted_progress;
        // } else {
        //     if (easing.operator bool()) {
        //         progress = easing.value()->solve(stepped_progress, 1e-3);
        //         progress = std::round(progress * 20) / 20.0;  // 20 = 1/0.05
        //     } else {
        //         progress = stepped_progress;
        //     }
        //     if (stepped_progress < 1.0) {
        //         scale_cache.last_stepped_progress = stepped_progress;
        //         scale_cache.cached_adjusted_progress = progress;
        //     } else {
        //         scale_cache.last_stepped_progress = -1.0;
        //     }
        // }
        
        scale = easing(animate_scale.first, animate_scale.second, stepped_progress);
        #ifdef DEBUG
        printf("GetAnimateScale type:%d animate_scale:%f  stepped_progress:%f\n", type, scale, stepped_progress);
        #endif
        return true;
    }

    void SetAnimateRotate(double start, double end) {
        animate_rotate = {start, end};
    }

    bool GetAnimateRotate(double& rotate, const TimePoint& now = Clock::now()) {
        auto type = GetAnimationType();
        if ((type & kAnimationTypeRotateTo) != kAnimationTypeRotateTo) {
            return false;
        }
        // 获取当前5%档位的进度值
        double stepped_progress = GetProcess(now);
        
        // double progress;
        // // 检查是否需要使用缓存（仅当非结束状态且档位未变化时）
        // if (stepped_progress < 1.0 && std::abs(stepped_progress - rotate_cache.last_stepped_progress) < 1e-6) {
        //     // 档位未变化，使用缓存的调整后progress
        //     progress = rotate_cache.cached_adjusted_progress;
        // } else {
        //     // 重新计算调整后的progress（应用缓动曲线）
        //     if (easing.operator bool()) {
        //         progress = easing.value()->solve(stepped_progress, 1e-3);
        //         progress = std::round(progress * 20) / 20.0;  // 20 = 1/0.05
        //     } else {
        //         progress = stepped_progress;
        //     }
        //     // 更新缓存（仅当非结束状态时）
        //     if (stepped_progress < 1.0) {
        //         rotate_cache.last_stepped_progress = stepped_progress;
        //         rotate_cache.cached_adjusted_progress = progress;
        //     } else {
        //         // 动画结束，重置缓存以便下次使用
        //         rotate_cache.last_stepped_progress = -1.0;
        //     }
        // }
        
        // 根据调整后的进度计算当前旋转值
        rotate = easing(animate_rotate.first , animate_rotate.second , stepped_progress);
        return true;
    }

    void SetAnimateRoll(double start, double end) {
        animate_roll = {start, end};
    }

    bool GetAnimateRoll(double& roll, const TimePoint& now = Clock::now()) {
        auto type = GetAnimationType();
        if ((type & kAnimationTypeRollTo) != kAnimationTypeRollTo) {
            return false;
        }
         // 获取当前5%档位的进度值
        double stepped_progress = GetProcess(now);
        
        // double progress;
        // // 检查是否需要使用缓存（仅当非结束状态且档位未变化时）
        // if (stepped_progress < 1.0 && std::abs(stepped_progress - roll_cache.last_stepped_progress) < 1e-6) {
        //     // 档位未变化，使用缓存的调整后progress
        //     progress = roll_cache.cached_adjusted_progress;
        //     progress = std::round(progress * 20) / 20.0;  // 20 = 1/0.05
        // } else {
        //     // 重新计算调整后的progress（应用缓动曲线）
        //     if (easing.operator bool()) {
        //         progress = easing.value()->solve(stepped_progress, 1e-3);
        //     } else {
        //         progress = stepped_progress;
        //     }
        //     // 更新缓存（仅当非结束状态时）
        //     if (stepped_progress < 1.0) {
        //         roll_cache.last_stepped_progress = stepped_progress;
        //         roll_cache.cached_adjusted_progress = progress;
        //     } else {
        //         // 动画结束，重置缓存以便下次使用
        //         roll_cache.last_stepped_progress = -1.0;
        //     }
        // }
        
        // 根据调整后的进度计算当前滚动值
        roll = easing(animate_roll.first, animate_roll.second, stepped_progress);
        #ifdef DEBUG
        printf("GetAnimateRoll type:%d animate_roll:%f  stepped_progress:%f\n", type, roll, stepped_progress);
        #endif
        return true;
    }

    private:

        std::pair<PointXY<double>, PointXY<double>> animate_pos = {{0.0,0.0}, {0.0,0.0}}; // <start_pos, end_pos>
        std::pair<double, double> animate_scale = {0.0, 0.0}; // <start_scale, end_scale>
        std::pair<double, double> animate_rotate = {0.0, 0.0}; // <start_rorate, end_rotate>
        std::pair<double, double> animate_roll = {0.0, 0.0}; // <start_roll, end_roll>
        // 新增：进度缓存结构体（保存上一次的档位值和调整后的progress）
        struct ProgressCache {
            double last_stepped_progress = -1.0; // 上一次的5%档位值（初始-1表示未初始化）
            double cached_adjusted_progress = 0.0; // 缓存的已调整progress（应用缓动后的值）
        };
        
        ProgressCache pos_cache;    // 位置动画缓存
        ProgressCache scale_cache;  // 缩放动画缓存
        ProgressCache rotate_cache; // 旋转动画缓存
        ProgressCache roll_cache;   // 滚动动画缓存
       
};
} // namespace aurora
