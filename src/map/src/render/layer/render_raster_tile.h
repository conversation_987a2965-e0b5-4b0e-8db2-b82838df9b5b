/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file raster_tile.h
 * @brief Declaration file of class RasterTile.
 * @attention used for C/C++ only.
 */

 #ifndef MAPRENDER_DATA_RASTER_TILE_H_
 #define MAPRENDER_DATA_RASTER_TILE_H_

 #include "gl_texture.h"
 #include "util_tile_define.h"
 #include "gpu_program.h"
 #include "map_render_camera_if.h"
 #include "gl_vbo.h"
 #include "gl_vao.h"
 #include "gl_ebo.h"
 #include "aabb2.h"
 #include "animation_base.h"

 namespace aurora {
 
 /**
 * class breif description
 *
 * RasterTile
 *
 */

 class RasterTile {
 public:
   RasterTile();              
    ~RasterTile();   

   bool Init(const void* pixeldata, TextureFormat format, uint32_t w, uint32_t h);

   void UpdateTileBuffer(const void* pixeldata, TextureFormat format, uint32_t w, uint32_t h);

   void Destory();

   void Render(CameraPtr camera, const TimePoint& now = Clock::now());
   
   bool IsValid();

   void SetTileID(TileID id,  const AABB2<Point2d>& tile_mbr) ;

   const TileID& GetTileID() const { return id_; }

   const AABB2<Point2d>& GetTileMBR() const { return tile_mbr_; }
  
 private:
    GLEBO ebo_{};
    GLVBO vbo_{};
    GLVAO vao_{};

    float vertices_[16]{0.0f};
    TileID id_{};
    AABB2<Point2d> tile_mbr_{};
    GLTexturePtr texture_{nullptr};
    GPUProgramPtr program_{nullptr};

    bool bInitVertData_{false};
 };
     
 } //namespace 
 
 #endif //MAPRENDER_DATA_RASTER_TILE_H_
 /* EOF */