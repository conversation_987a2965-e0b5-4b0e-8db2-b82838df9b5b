/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file i_map_render_manager.h
 * @brief Declaration file of class IMapRenderManager.
 * @attention used for C/C++ only.
 */

#ifndef MAP_SRC_RENDER_MAPRENDERMANAGER_INTERFACE_H_
#define MAP_SRC_RENDER_MAPRENDERMANAGER_INTERFACE_H_


#include "render_geometry_type.h"
#include "util_lonlat.h"
#include "util_vector2.h"
#include <vector>
#include "point2.h"
#include "map_render_defines.h"


namespace aurora {
    
class MapDataProvider;
using MapDataProviderPtr = std::shared_ptr<MapDataProvider>;

class MapRenderThemeIF;

/**
* class breif description
*
* IMapRenderManager
*
*/
class IMapRenderManager
{
public:             
    virtual ~IMapRenderManager() = default;
    
    /**
     * Initialize resource
     */
    virtual bool Init(MapDataProviderPtr provider, MapRenderThemeIF* theme) = 0;
    

    /**
     * Destory resource
     */
    virtual void Destory() = 0;

    virtual void RenderMap() = 0;

    virtual void SetMapScale(float scale, const uint32_t animation_duration_ms = 0) = 0;

    virtual void SetMapCenter(double lon, double lat, const uint32_t animation_duration_ms = 0) = 0;

    virtual void SetMapRotation(float rot, const uint32_t animation_duration_ms = 0) = 0;

    virtual float GetMapScale() = 0;

    virtual Lonlat GetMapCenter() = 0;

    virtual float GetMapRotation() = 0;

    virtual Point2d ScreenToMap(const Vector2<int32_t>& pt) = 0;

    virtual Vector2<int32_t> MapToScreen(const Point2d& geo) = 0;


    virtual void SetMapThemeByLayerId() = 0;

    virtual void RenderEmbedGeoObj() = 0;

    virtual void SetScreenSize(int32_t width, int32_t height) = 0;

    // 注意：地图内部左下角为0,0点，所以如果窗口给的y是按左上角做为0，0点，delta_y应该取反传入
    virtual void MoveMap(double delta_x, double delta_y, const bool move_end = false, const uint32_t animation_duration = 0) = 0;  

    virtual void SetMapPitch(float pitch, const uint32_t animation_duration = 0) = 0;

    virtual float GetMapPitch() = 0;

    //  flyto 到指定经纬度和比例尺，默认最少5s动画
    virtual void FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms) = 0;

    virtual void SetPath(uint32_t type, std::vector<Point2d>& path) = 0;

    virtual void ClearPath(uint32_t type) = 0;

    virtual int32_t SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name) = 0;

    virtual void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name) = 0;

    virtual void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree = 0.f ) = 0;

    virtual void ClearMark(uint16_t mark_type, int32_t mark_id) = 0;

    virtual AABB2<Point2d> GetMapScreenBoudingBox() = 0;

    // 点击事件相关，目前点击click/longclick由外部调用，后续考虑内部接受点击事件后自己判断回调，外部注册clickcallback
    virtual void OnClick(const Point2d& screen_pos, ClickCallback callback) = 0;

    virtual void OnLongClick(const Point2d& screen_pos, ClickCallback callback) = 0;


    // 双指移动事件，目前由外部调用，后续考虑内部接受点击事件后自己判断回调，外部注册clickcallback
    virtual void OnTwoFingerDown(const Point2d& pos1, const Point2d& pos2) = 0;

    virtual void OnTwoFingerMove(const Point2d& current_pos1, const Point2d& current_pos2) = 0;

    virtual void OnTwoFingerUp() = 0;

    /// @brief 在指定层绘制标记（覆盖该层原有同类型标记）
    /// @param layer 目标层次（需在 OverlayLayer 范围内）
    /// @param mark_data 标记绘制数据
    /// @return 标记ID（>=0成功，<0失败）
    virtual int32_t DrawMarkOnLayer(OverlayLayer layer, const MarkDrawData& mark_data, 
        const CollisionLayer collision_layer = CollisionLayer::kMarkCollisionLayer) = 0;

    /// @brief 在指定层绘制多边形（覆盖该层原有同类型多边形）
    /// @param layer 目标层次
    /// @param polygon_data 多边形绘制数据
    virtual bool DrawPolygonOnLayer(OverlayLayer layer, const PolygonDrawData& polygon_data,
      const CollisionLayer collision_layer = CollisionLayer::kOverlayCollisionLayer) = 0;

    /// @brief 在指定层绘制线（覆盖该层原有同类型线）
    /// @param layer 目标层次
    /// @param line_data 线绘制数据
    virtual bool DrawLineOnLayer(OverlayLayer layer, const LineDrawData& line_data,
      const CollisionLayer collision_layer = CollisionLayer::kOverlayCollisionLayer) = 0;

    /// @brief 清除指定层的所有绘制内容（标记、多边形、线）
    /// @param layer 目标层次
    virtual void ClearLayer(OverlayLayer layer) = 0;

    /// @brief 加载新数据
    virtual void ReloadDisplayData(const std::string display_data_path) = 0;

};
    
} //namespace 

#endif //MAP_SRC_RENDER_MAPRENDERMANAGER_INTERFACE_H_
/* EOF */