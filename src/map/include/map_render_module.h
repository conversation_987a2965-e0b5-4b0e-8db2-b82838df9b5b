/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_service.h
 * @brief Declaration file of class IMapRenderService.
 * @attention used for C/C++ only.
 */

 #ifndef INCLUDE_MAP_RENDER_SERVICE_H_
 #define INCLUDE_MAP_RENDER_SERVICE_H_
 
 #include <cstdint>
 #include <memory>
 #include <string>
 #include <vector>
 #include "point2.h"
 #include "map_render_defines.h"

 namespace aurora {

 class MapRenderService;

 /**
 * class breif description
 *
 * IMapRenderService
 * 
 */
 class IMapRenderService {

 public:        
   IMapRenderService();       
   ~IMapRenderService();
     
    /// @brief Initialize resources
    /// @param dataprovider Data provider
    /// @param themeconfig Theme configuration
    int32_t Init(const AuroraMapConfig& map_config, void* themeconfig);

    /// @brief Set screen size
    /// @param width Screen width
    /// @param height Screen height
    /// @return Returns true on 0, false on other
    int32_t SetScreenSize(uint32_t width, uint32_t height);
 
    /// @brief Destroy resources before rendering exits
    void Destory();
 
    /// @brief Set map center
    /// @param lon Longitude
    /// @param lat Latitude
    int32_t SetMapCenter(double lon, double lat, uint32_t animation_duration_ms = 0);
 
    /// @brief Get map center
    /// @return Map center coordinates
    Point2d GetMapCenter();
     
    /// @brief Set map scale
    /// @param scale Map scale
    /// @param animation_duration_ms Animation duration in milliseconds
    int32_t SetMapScale(double scale, uint32_t animation_duration_ms = 0);
 
    /// @brief Render map
    void RenderMap();
 
    /// @brief Set map rotation
    /// @param rot Rotation angle in degrees
    /// @param animation_duration_ms Animation duration in milliseconds
    int32_t SetMapRotation(float rot, uint32_t animation_duration_ms = 0);
 
    /// @brief Convert screen coordinates to world coordinates
    /// @param pt Screen coordinates
    /// @return World coordinates
    Point2d ScreenToMap(const Point2d& pt);
 
    /// @brief Convert world coordinates to screen coordinates
    /// @param geo World coordinates
    /// @return Screen coordinates
    Point2d MapToScreen(const Point2d& geo);

    /// @brief Move map
    /// @param delta_x X-axis offset
    /// @param delta_y Y-axis offset
    /// @param move_end Whether the move is the end
    /// @param animation_duration_ms Animation duration in milliseconds
    void MoveMap(double delta_x, double delta_y, bool move_end = false, uint32_t animation_duration_ms = 0);   

     float GetMapScale(); 


     void SetMapPitch(float pitch, uint32_t animation_duration_ms = 0);

     float GetMapPitch();

     //  默认flyto最少5s动画
     void FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms = 5000);

     void SetPath(uint32_t type, std::vector<Point2d>& path);

    void ClearPath(uint32_t type);

    // 返回 >= 0 MarkId, < 0 失败
    int32_t  SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name);

    // 更新对应markid图片，经纬度位置保持不变
    void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name);

    // 更新对应markid的位置和角度，图片和anchor保持不变
    void UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree = 0.f );

    // mark_id < 0 清除所有mark_type
    void ClearMark(uint16_t mark_type, int32_t mark_id = -1);

    virtual AABB2<Point2d> GetMapScreenBoudingBox();

    // 点击事件相关，目前点击click/longclick由外部调用，后续考虑内部接受点击事件后自己判断回调，外部注册clickcallback
    void OnClick(const Point2d& screen_pos, ClickCallback callback);

    void OnLongClick(const Point2d& screen_pos, ClickCallback callback);


    // 双指移动事件，目前由外部调用，后续考虑内部接受点击事件后自己判断回调，外部注册clickcallback
    void OnTwoFingerDown(const Point2d& pos1, const Point2d& pos2);

    void OnTwoFingerMove(const Point2d& current_pos1, const Point2d& current_pos2);

    void OnTwoFingerUp();

    /// @brief 在指定层绘制标记（覆盖该层原有同类型标记）
    /// @param layer 目标层次（需在 OverlayLayer 范围内）
    /// @param mark_data 标记绘制数据
    /// @return 标记ID（>=0成功，<0失败）
    int32_t DrawMarkOnLayer(OverlayLayer layer, const MarkDrawData& mark_data, 
        const CollisionLayer collision_layer = CollisionLayer::kMarkCollisionLayer);

    /// @brief 在指定层绘制多边形（覆盖该层原有同类型多边形）
    /// @param layer 目标层次
    /// @param polygon_data 多边形绘制数据
    bool DrawPolygonOnLayer(OverlayLayer layer, const PolygonDrawData& polygon_data,
      const CollisionLayer collision_layer = CollisionLayer::kOverlayCollisionLayer);

    /// @brief 在指定层绘制线（覆盖该层原有同类型线）
    /// @param layer 目标层次
    /// @param line_data 线绘制数据
    bool DrawLineOnLayer(OverlayLayer layer, const LineDrawData& line_data,
      const CollisionLayer collision_layer = CollisionLayer::kOverlayCollisionLayer);

    /// @brief 清除指定层的所有绘制内容（标记、多边形、线）
    /// @param layer 目标层次
    void ClearLayer(OverlayLayer layer);

    /// @brief 加载新数据
    void ReloadDisplayData(const std::string display_data_path);

 private:
   std::unique_ptr<MapRenderService> render_service_;
 };
     
 } //namespace 
 
 #endif // INCLUDE_MAP_RENDER_SERVICE_H_
 /* EOF */