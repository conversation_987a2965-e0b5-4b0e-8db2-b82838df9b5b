 #pragma once
 
#include <string>
#include <vector>
#include "point2.h"
#include <functional>
#include "util_vector4.h"
#include "aabb2.h"
#include <memory>

namespace aurora {

    
    struct AuroraMapConfig {
      std::string data_path_;
      std::string config_path_; // 目前主题配置在外面new出来传递，这个配置暂时用不到
      std::string default_fontPath_;
      std::string default_fontName_;
      std::string default_mark_path_;

      AuroraMapConfig() {}
      AuroraMapConfig(const std::string& data_path, const std::string& config_path, 
        const std::string& default_fontPath, const std::string& default_fontName,
        const std::string& default_mark_path)
      : data_path_(data_path),
        config_path_(config_path),
        default_fontPath_(default_fontPath),
        default_fontName_(default_fontName),
        default_mark_path_(default_mark_path)
      {}

      AuroraMapConfig(const AuroraMapConfig& config)
      : data_path_(config.data_path_),
        config_path_(config.config_path_),
        default_fontPath_(config.default_fontPath_),
        default_fontName_(config.default_fontName_),
        default_mark_path_(config.default_mark_path_)
      {}
    };

// 点击事件返回的元素类型枚举
enum class ClickElementType {
    kNone,        // 未点击到任何元素
    kPOI,
    kMark,        // 点击到标记
    kPath         // 点击到路径（需结合 SetPath 接口）
};

// 点击事件返回的数据结构
struct ClickEventResult {
    Point2d geo_pos;               // 点击位置的地理坐标（经纬度）
    ClickElementType element_type; // 被点击的元素类型
    union {
        uint64_t uuid;
        struct {
            int32_t element_id;            // 被点击元素的ID（如 mark_id，-1表示无）
            uint32_t element_subtype;      // 元素子类型（如 mark_type）
        };
    };
};

enum class OverlayLayer {
    kPathOverlayer,
    kCustomerOverlayer_MIN = 10, // 客户自定义 OverlayLayer 最小层，10-19按需添加
    kCustomerOverlayer_MAX = 20 // 客户自定义 OverlayLayer 最大层
};


// 标记样式（颜色、大小等）
struct MarkStyle {
    std::string texture_path;  // 标记纹理路径（或内置纹理ID）
    float size;                // 标记尺寸（像素）
    Point2d anchor;            // 锚点（0~1，相对于标记中心）
};

// 多边形样式（填充色、边框色等）
struct PolygonStyle {
    Vector4<float> fill_color;        // 填充颜色（RGBA，0~1）
    Vector4<float> border_color;      // 边框颜色（RGBA，0~1）
    float border_width;        // 边框宽度（像素）
};

// 线样式（颜色、线宽等）
struct LineStyle {
    Vector4<float> color;             // 线颜色（RGBA，0~1）
    float width;               // 线宽（像素）
    bool is_dashed;            // 是否虚线
};

// 标记绘制数据（地理坐标+样式）
struct MarkDrawData {
    Point2d geo_pos;           // 标记的地理坐标（经纬度）
    MarkStyle style;           // 标记样式
};

// 多边形绘制数据（闭合路径+样式）
struct PolygonDrawData {
    std::vector<Point2d> geo_points;  // 多边形顶点（地理坐标，闭合路径）
    PolygonStyle style;       // 多边形样式
};

// 线绘制数据（开放路径+样式）
struct LineDrawData {
    std::vector<Point2d> geo_points;  // 线顶点（地理坐标，开放路径）
    LineStyle style;          // 线样式
};

// 固定碰撞层（按渲染顺序从下到上排列）
enum class CollisionLayer {
    kBackgroundCollisionLayer,       // 背景层（地图底图）
    kBuildingCollisionLayer,         // 建筑层
    kMarkCollisionLayer,             // 标记层（点标记）
    kOverlayCollisionLayer,          // 覆盖层（多边形、线）
    kUICollisionLayer,               // UI层（最上层，如按钮）
    kMaxCollisionLayer               // 层数量标记（非实际层）
};

// 点击事件回调函数类型（用户自定义处理逻辑）
using ClickCallback = std::function<void(const ClickEventResult&)>;


} //namesapce
