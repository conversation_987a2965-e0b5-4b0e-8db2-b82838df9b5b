#ifndef MAP_SRC_GUIDANCE_INCLUDE_GUIDE_DEF_H
#define MAP_SRC_GUIDANCE_INCLUDE_GUIDE_DEF_H

#include <cstdint>
#include <string>
#include <vector>
#include <memory>

#include "pointll.h"

namespace aurora {
namespace guide {

/**
 * @enum DistanceUnits
 * @brief Enum for distance units.
 */
enum DistanceUnits {
    kDistanceKilometers = 0,  ///< Kilometers
    KDistanceMiles          ///< Miles
};

/**
 * @struct GuidanceOptions
 * @brief Struct for guidance options.
 */
struct GuidanceOptions {
    DistanceUnits distance_unit;  ///< Distance unit
    std::string   language_tag;   ///< Language tag
};

/**
 * @enum NavigationMode
 * @brief Enum for navigation modes.
 */
enum NavigationMode {
    kNavigationModeNone = 0,    ///< No navigation mode
    kNavigationModeGPS,         ///< Real navigation
    kNavigationModeSimulation,  ///< Simulated navigation
    kNavigationModeCruise       ///< Cruise mode
};

/**
 * @enum class ManeuverType
 * @brief Enum class for maneuver types.
 */
enum class ManeuverType {
    kTypeNone = 0,             ///< Invalid
    kTypeContinue,             ///< Go straight
    kTypeSlightRight,          ///< Slight right turn
    kTypeRight,                ///< Right turn
    kTypeSharpRight,           ///< Sharp right turn
    kTypeLeftUTurn,            ///< Left U-turn
    kTypeRightUTurn,           ///< Right U-turn
    kTypeSharpLeft,            ///< Sharp left turn
    kTypeLeft,                 ///< Left turn
    kTypeSlightLeft,           ///< Slight left turn

    kTypeRSideRoundStraight,    ///< Roundabout straight (right-hand traffic)
    kTypeRSideRoundRight,       ///< Roundabout right turn (right-hand traffic)
    kTypeRSideRoundLeft,        ///< Roundabout left turn (right-hand traffic)
    kTypeRSideRoundUTurn,       ///< Roundabout U-turn (right-hand traffic)
    kTypeLSideRoundStraight,    ///< Roundabout straight (left-hand traffic)
    kTypeLSideRoundRight,       ///< Roundabout right turn (left-hand traffic)
    kTypeLSideRoundLeft,        ///< Roundabout left turn (left-hand traffic)
    kTypeLSideRoundUTurn,       ///< Roundabout U-turn (left-hand traffic)

    kTypeLSideRoundIn,         ///< Enter roundabout, take exit number (left-hand traffic)
    kTypeLSideRoundOut,        ///< Roundabout exit (left-hand traffic)
    kTypeRSideRoundIn,         ///< Enter roundabout, take exit number (right-hand traffic)
    kTypeRSideRoundOut,        ///< Roundabout exit (right-hand traffic)

    kTypeBecomes,              ///< Road name change
    kTypeStart,                ///< Start position
    kTypeStartLeft,            ///< Start position on the left side of the route
    kTypeStartRight,           ///< Start position on the right side of the route
    kTypeViaPoint,             ///< Via point
    kTypeViaPointLeft,         ///< Via point on the left side of the route
    kTypeViaPointRight,        ///< Via point on the right side of the route
    kTypeDestination,          ///< Destination
    kTypeDestinationLeft,      ///< Destination on the left side of the route
    kTypeDestinationRight,     ///< Destination on the right side of the route

    kTypeStayStraight,         ///< Stay straight at a fork
    kTypeStayRight,            ///< Stay right at a fork
    kTypeStayLeft,             ///< Stay left at a fork

    kTypeStraightToRamp,       ///< Straight to ramp
    kTypeRightToRamp,          ///< Right turn to ramp
    kTypeLeftToRamp,           ///< Left turn to ramp

    kTypeExitLeft,             ///< Exit left from highway
    kTypeExitRight,            ///< Exit right from highway

    kTypeMerge,                ///< Merge into road
    kTypeMergeToLeft,          ///< Merge into road to the left
    kTypeMergeToRight,         ///< Merge into road to the right

    kTypeNum                   ///< Number of maneuver types
};

/**
 * @enum class CardinalDirection
 * @brief Enum class for cardinal directions.
 */
enum class CardinalDirection {
    kNorth = 0,                ///< North
    kNorthEast,                ///< Northeast
    kEast,                     ///< East
    kSouthEast,                ///< Southeast
    kSouth,                    ///< South
    kSouthWest,                ///< Southwest
    kWest,                     ///< West
    kNorthWest                 ///< Northwest
};

/**
 * @struct TimeAndDist
 * @brief Struct for time and distance.
 */
struct TimeAndDist {
    int32_t sec;    ///< Time in seconds
    float   dis;    ///< Distance in meters
};

/**
 * @enum class GuidanceSignPostType
 * @brief Enum class for guidance sign post types.
 */
enum class GuidanceSignPostType {
    kTypeNone = 0,  ///< No sign
    kTypeExit,      ///< Exit sign
    kTypeDirection  ///< Direction sign
};

/**
 * @struct GuidanceSignPostInfo
 * @brief Struct for guidance sign post information.
 */
struct GuidanceSignPostInfo {
    std::string                 id;                ///< ID
    GuidanceSignPostType        type;             ///< Sign post type
    float                       remain_dist;      ///< Remaining distance in meters
    PointLL                     lnglat;           ///< Latitude and longitude
    std::vector<std::string>    name_infos;       ///< Array of exit number names
};
using GuidanceSignPostInfoPtr = std::shared_ptr<GuidanceSignPostInfo>;

/**
 * @struct ManeuverPoint
 * @brief Struct for maneuver points.
 */
struct ManeuverPoint {
    ManeuverType type;  ///< Maneuver type
    float        distance;  ///< Distance
};

/**
 * @struct NavigationInfo
 * @brief Struct for navigation information.
 */
struct NavigationInfo {
    uint64_t            path_id;            ///< Route ID
    NavigationMode      mode;               ///< Navigation mode
    TimeAndDist         path_remain;        ///< Remaining route distance and time
    std::vector<TimeAndDist> via_remain;    ///< Remaining via point distance and time
    std::string        curr_road_name;      ///< Current road name
    std::vector<ManeuverPoint> next_maneuver_points;  ///< Next maneuver points
};
using NavigationInfoPtr = std::shared_ptr<NavigationInfo>;

/**
 * @enum class LaneAction
 * @brief Enum class for lane actions.
 */
enum class LaneAction : uint8_t {
    kLaneActionNULL             = 0,     ///< No corresponding lane
    kLaneActionAhead            = 1,     ///< Go straight
    kLaneActionLeft             = 2,     ///< Left turn
    kLaneActionAheadLeft        = 3,     ///< Go straight and left turn
    kLaneActionRight            = 4,     ///< Right turn
    kLaneActionAheadRight       = 5,     ///< Go straight and right turn
    kLaneActionLUTurn           = 6,     ///< Left U-turn
    kLaneActionLeftRight        = 7,     ///< Left turn and right turn
    kLaneActionAheadLeftRight   = 8,     ///< Go straight, left turn, and right turn
    kLaneActionRUTurn           = 9,     ///< Right U-turn
    kLaneActionAheadLUTurn      = 10,    ///< Go straight and left U-turn
    kLaneActionAheadRUTurn      = 11,    ///< Go straight and right U-turn
    kLaneActionLeftLUTurn       = 12,    ///< Left turn and left U-turn
    kLaneActionRightRUTurn      = 13,    ///< Right turn and right U-turn
    kLaneActionLeftInAhead      = 14,    ///< Invalid, reserved
    kLaneActionLeftLUturn       = 15,    ///< Invalid, reserved
    kLaneActionReserved         = 16,    ///< Reserved
    kLaneActionAheadLeftLUTurn  = 17,    ///< Go straight, left turn, and left U-turn
    kLaneActionRightLUTurn      = 18,    ///< Right turn and left U-turn
    kLaneActionLeftRightLUTurn  = 19,    ///< Left turn, right turn, and left U-turn
    kLaneActionAheadRightLUTurn = 20,    ///< Go straight, right turn, and left U-turn
    kLaneActionLeftRUTurn       = 21,    ///< Left turn and right U-turn
    kLaneActionBus              = 22,    ///< Bus lane
    kLaneActionEmpty            = 23,    ///< Empty lane
    kLaneActionVariable         = 24,    ///< Variable lane
    kLaneActionDedicated        = 25,    ///< Dedicated lane
    kLaneActionTidal            = 26,    ///< Tidal lane
    kLaneActionHov              = 27     ///< HOV lane
};

/**
 * @struct NavigationLaneInfo
 * @brief Struct for navigation lane information.
 */
struct NavigationLaneInfo {
    std::string             id;                ///< ID
    std::vector<LaneAction> back_lanes;        ///< Background lane information
    std::vector<LaneAction> front_lanes;       ///< Foreground lane information
};
using NavigationLaneInfoPtr = std::shared_ptr<NavigationLaneInfo>;

/**
 * @enum JunctionViewType
 * @brief Enum for junction view types.
 */
enum JunctionViewType {
    kNone = 0,
    kRealView,        ///< Real view
    kDiagramView,     ///< Diagram view
    kDirectionBoard   ///< Direction board
};

/**
 * @struct NavigationJunctionViewInfo
 * @brief Struct for navigation junction view information.
 */
struct NavigationJunctionViewInfo {
    std::string         id;                ///< ID
    int32_t             type;             ///< Real view/diagram view/direction board
    float               remain_dist;      ///< Remaining distance in meters
    PointLL             lnglat;           ///< Latitude and longitude (currently not supported)
    std::vector<int8_t> back_view_data;   ///< Background view data
    std::vector<int8_t> front_view_data;  ///< Foreground view data
};
using NavigationJunctionViewInfoPtr = std::shared_ptr<NavigationJunctionViewInfo>;

/**
 * @enum class GuidanceFacilityType
 * @brief Enum class for guidance facility types.
 */
enum class GuidanceFacilityType : uint16_t {
    kNone = 0,
    kRockFallLeft,              ///< Left rockfall
    kRockFallRight,             ///< Right rockfall
    kAccidentProne,             ///< Accident-prone area
    kSlipperyRoad,              ///< Slippery road
    kVillage,                   ///< Village
    kSchoolZone,                ///< School zone
    kRailroadCrossing,          ///< Railroad crossing
    kMannedRailroadCrossing,    ///< Manned railroad crossing
    kUnmannedRailroadCrossing,  ///< Unmanned railroad crossing
    kRoadNarrowsLeft,           ///< Road narrows on the left
    kRoadNarrowsRight,          ///< Road narrows on the right
    kRoadNarrowsBoth,           ///< Road narrows on both sides
    kSharpLeftTurn,             ///< Sharp left turn
    kSharpRightTurn,            ///< Sharp right turn
    kMultipleCurves,            ///< Multiple curves
    kMergeLeft,                 ///< Left merge sign
    kMergeRight,                ///< Right merge sign
    kNoOvertaking,              ///< No overtaking
    kNarrowBridge,              ///< Narrow bridge
    kAroundRouteLeft,           ///< Left detour
    kAroundRouteRight,          ///< Right detour
    kAroundRouteBoth,           ///< Left and right detour
    kDangerousCurveLeft,        ///< Dangerous curve (left)
    kDangerousCurveRight,       ///< Dangerous curve (right)
    kSteepAscent,               ///< Steep ascent
    kSteepDescent,              ///< Steep descent
    kFordRoad,                  ///< Flooded road
    kUnlevelRoad,               ///< Uneven road
    kSlowDown,                  ///< Slow down
    kDangerAhead,               ///< Beware of danger ahead
    kCrosswind,                 ///< Beware of crosswind
    kTunnel,                    ///< Tunnel
    kFerry                     ///< Ferry
};

/**
 * @struct NavigationFacilityInfo
 * @brief Structure to store navigation facility information.
 */
struct NavigationFacilityInfo {
    std::string id;  ///< Facility ID
    GuidanceFacilityType type;  ///< Facility type
    float remain_dist;  ///< Remaining distance
    PointLL lnglat;  ///< Latitude and longitude coordinates
};

/**
 * @typedef NavigationFacilityInfoPtr
 * @brief Smart pointer type for NavigationFacilityInfo.
 */
using NavigationFacilityInfoPtr = std::shared_ptr<NavigationFacilityInfo>;

/**
 * @enum GuidanceCameraType
 * @brief Enum for guidance camera types.
 */
enum class GuidanceCameraType {
    kTypeNone = 0,  ///< None type
    kTypeSpeedCamera,  ///< Speed camera
    kTypeViolationCamera,  ///< Violation camera
    kTypeTrafficLightCamera,  ///< Red light camera
    kTypeMonitorCamera,  ///< Monitoring camera
    kTYpeMonitorRecording,  ///< Monitoring recording <Not Support>
    kTypeEmergencyLaneCamera,  ///< Emergency lane camera
    kTypeOneWayLaneCamera  ///< One-way lane camera <Not Support>
};

/**
 * @struct NavigationCameraInfo
 * @brief Structure to store navigation camera information.
 */
struct NavigationCameraInfo {
    std::string id;  ///< Camera ID
    GuidanceCameraType type;  ///< Camera type
    float remain_dist;  ///< Remaining distance
    uint16_t angle;  ///< Illumination angle, north is 0, clockwise [0, 360)
    uint16_t speed_limit;  ///< Speed limit for speed cameras
    PointLL lnglat;  ///< Latitude and longitude coordinates
};

/**
 * @typedef NavigationCameraInfoPtr
 * @brief Smart pointer type for NavigationCameraInfo.
 */
using NavigationCameraInfoPtr = std::shared_ptr<NavigationCameraInfo>;

/**
 * @enum TTSScenePlay
 * @brief Enum for TTS scene play.
 */
enum class TTSScenePlay : int8_t {
    kTTSScenePlayNull = -1,  ///< Null scene
    kTTSScenePlayJoinLeft,  ///< Left lane join
    kTTSScenePlayJoinRight,  ///< Right lane join
    kTTSScenePlayZigzag,  ///< Sharp turn
    kTTSScenePlayAccident,  ///< Accident-prone area
    kTTSScenePlayRockFall,  ///< Beware of falling rocks
    kTTSScenePlayRailway,  ///< Railway crossing
    kTTSScenePlayVillage,  ///< Village
    kTTSScenePlaySchool,  ///< School
    kTTSScenePlayReady,  ///< Ready
    kTTSScenePlaySpeedCamera,  ///< Speed camera
    kTTSScenePlayServiceArea,  ///< Service area
    kTTSScenePlayDeviation  ///< Deviation
};

/**
 * @enum NavigationStopCode
 * @brief Enum for navigation stop codes.
 */
enum class NavigationStopCode {
    kStopCodeNone = 0,  ///< None
    kStopCodeDestination,  ///< Reached destination
    kStopCodeAppTrigger,  ///< Application triggered
    kStopCodeModuleUnInit  ///< Module uninitialized
};

/**
 * @struct GuidanceSoundInfo
 * @brief Structure to store guidance sound information.
 */
struct GuidanceSoundInfo {
    std::string text;  ///< Guidance text
};

using GuidanceSoundInfoPtr = std::shared_ptr<GuidanceSoundInfo>;

/**
 * @struct CruiseInfo
 * @brief Structure to store cruise information.
 */
struct CruiseInfo {};
using CruiseInfoPtr = std::shared_ptr<CruiseInfo>;

/**
 * @enum GuidanceSAPAType
 * @brief Enum for guidance service area types.
 */
enum class GuidanceSAPAType {
    kSAPATypeNone = 0,  ///< None type
    kSAPATypeSA,  ///< Service Area (SA)
    kSAPATypeGS,  ///< Gas Station (GS)
    kSAPATypePA  ///< Parking Area (PA)
};

/**
 * @struct GuidanceSAPADetail
 * @brief Structure to store guidance service area details.
 */
struct GuidanceSAPADetail {
    uint32_t has_gas : 1;  ///< Flag indicating availability of gasoline
    uint32_t has_natual_gas : 1;  ///< Flag indicating availability of natural gas
    uint32_t has_toilet : 1;  ///< Flag indicating availability of toilets
    uint32_t has_internal_rest : 1;  ///< Flag indicating availability of internal rest areas
    uint32_t has_external_rest : 1;  ///< Flag indicating availability of external rest areas
    uint32_t has_coffee : 1;  ///< Flag indicating availability of coffee services
    uint32_t has_dining : 1;  ///< Flag indicating availability of dining facilities
    uint32_t has_shop : 1;  ///< Flag indicating availability of retail shops
    uint32_t has_stay_service : 1;  ///< Flag indicating accommodation services
    uint32_t has_repair_service : 1;  ///< Flag indicating vehicle repair services
    uint32_t has_atm : 1;  ///< Flag indicating availability of ATMs
    uint32_t has_drug : 1;  ///< Flag indicating availability of pharmacies
    uint32_t has_speciality : 1;  ///< Flag indicating availability of specialty stores
    uint32_t has_disabled_facility : 1;  ///< Flag indicating accessibility for disabled individuals

    uint32_t has_public_phone : 1;  ///< Flag indicating availability of public telephones
    uint32_t has_vending_machine : 1;  ///< Flag indicating availability of vending machines
    uint32_t has_shower : 1;  ///< Flag indicating availability of shower facilities
    uint32_t has_parking : 1;  ///< Flag indicating availability of parking
    uint32_t not_used : 14;  ///< Reserved bits (not used)
};

/**
 * @struct NavigationSAPAInfo
 * @brief Structure to store navigation service area and parking area information.
 */
struct NavigationSAPAInfo {
    std::string id;  ///< Service area or parking area ID
    GuidanceSAPAType type;  ///< Service area or parking area type
    std::string name;  ///< Name of the service area or parking area
    PointLL lnglat;  ///< Latitude and longitude coordinates
    float remain_dist;  ///< Remaining distance
    GuidanceSAPADetail detail;  ///< Detailed information about the service area or parking area
};
using NavigationSAPAInfoPtr = std::shared_ptr<NavigationSAPAInfo>;

/**
 * @struct NavigationLightBarInfo
 * @brief Structure to store navigation light bar information.
 */
struct NavigationLightBarInfo {};
using NavigationLightBarInfoPtr = std::shared_ptr<NavigationLightBarInfo>;

/**
 * @struct NavigationPathTmcEventInfo
 * @brief Structure to store navigation path TMC event information.
 */
struct NavigationPathTmcEventInfo {};
using NavigationPathTmcEventInfoPtr = std::shared_ptr<NavigationPathTmcEventInfo>;

/**
 * @struct NavigationCongestionInfo
 * @brief Structure to store navigation congestion information.
 */
struct NavigationCongestionInfo {};
using NavigationCongestionInfoPtr = std::shared_ptr<NavigationCongestionInfo>;

/**
 * @struct GuidanceTollgateGateInfo
 * @brief Structure to store guidance tollgate gate information.
 */
struct GuidanceTollgateGateInfo {
    uint8_t cash : 1;  ///< Cash lane
    uint8_t etc : 1;  ///< ETC (SunTong) lane
    uint8_t auto_card : 1;  ///< Auto card issuance
    uint8_t alipay : 1;  ///< Alipay payment
    uint8_t wechat : 1;  ///< WeChat payment
    uint8_t itc : 1;  ///< ITC (Intelligent Toll Collection) lane
    uint8_t not_used1 : 2;  ///< Reserved (2 bits)

    uint8_t normal : 1;  ///< Normal lane
    uint8_t hk_macao : 1;  ///< Hong Kong/Macau (Right-hand Drive) lane
    uint8_t general : 1;  ///< General lane
    uint8_t wide_lane : 1;  ///< Wide lane
    uint8_t not_used2 : 4;  ///< Reserved (4 bits)

    GuidanceTollgateGateInfo() {
        cash = 0;
        etc = 0;
        auto_card = 0;
        alipay = 0;
        wechat = 0;
        itc = 0;
        not_used1 = 0;
        normal = 0;
        hk_macao = 0;
        general = 0;
        wide_lane = 0;
        not_used2 = 0;
    }
};

/**
 * @struct NavigationTollStationInfo
 * @brief Structure to store navigation toll station information.
 */
struct NavigationTollStationInfo {
    std::string id;  ///< Toll station ID
    PointLL lnglat;  ///< Latitude and longitude coordinates
    float remain_dist;  ///< Remaining distance
    std::string station_name;  ///< Name of the toll station
    std::vector<int8_t> view_data;  ///< View data
    std::vector<GuidanceTollgateGateInfo> gate_infos;  ///< Tollgate gate information
};
using NavigationTollStationInfoPtr = std::shared_ptr<NavigationTollStationInfo>;

/**
 * @struct NavigationManeuverInfo
 * @brief Structure to store navigation maneuver information.
 */
struct NavigationManeuverInfo {
    int32_t type;  ///< Maneuver type
    std::string action;  ///< Action description
    uint32_t length;  ///< Length of maneuver
    uint32_t time;  ///< Time of maneuver
    std::string begin_name;  ///< Name of the beginning point
    std::string name;  ///< Name of the maneuver
    std::string end_name;  ///< Name of the end point
    std::vector<PointXY<double>> geos;  ///< Geographical points
};
using NavigationManeuverInfoPtr = std::shared_ptr<NavigationManeuverInfo>;

/**
 * @struct CruiseCongestionInfo
 * @brief Structure to store cruise congestion information.
 */
struct CruiseCongestionInfo {};
using CruiseCongestionInfoPtr = std::shared_ptr<CruiseCongestionInfo>;

/**
 * @struct CruiseCameraInfo
 * @brief Structure to store cruise camera information.
 */
struct CruiseCameraInfo {};
using CruiseCameraInfoPtr = std::shared_ptr<CruiseCameraInfo>;

}  // namespace guide
}  // namespace aurora

#endif  // MAP_SRC_GUIDANCE_INCLUDE_GUIDE_DEF_H
/* EOF */
