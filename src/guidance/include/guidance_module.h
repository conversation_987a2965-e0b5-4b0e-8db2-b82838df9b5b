#ifndef MAP_SRC_GUIDANCE_INCLUDE_GUIDANCE_MODULE_H
#define MAP_SRC_GUIDANCE_INCLUDE_GUIDANCE_MODULE_H

#include <string>

#include "module.h"
#include "guidance_listener.h"
#include "guidance_param.h"
#include "path_module.h"
#include "location_def.h"

namespace aurora {
namespace guide {

/**
 * @brief Guidance control class
 */
class GuidanceControl;

/**
 * @brief Guidance module class
 */
class AURORA_EXPORT GuidanceModule : public Module {
public:
    GuidanceModule();
    virtual ~GuidanceModule();

    /**
     * @brief Prepare the module with the given configuration
     * @param config: Configuration string
     * @return int32_t: Result code
     */
    virtual int32_t Prepare(const std::string &config);

    /**
     * @brief Initialize the module with the given finder
     * @param finder: Interface finder
     * @return int32_t: Result code
     */
    virtual int32_t Init(const InterfaceFinder &finder);

    /**
     * @brief Start the module
     * @return int32_t: Result code
     */
    virtual int32_t Start();

    /**
     * @brief Stop the module
     * @return int32_t: Result code
     */
    virtual int32_t Stop();

    /**
     * @brief Uninitialize the module
     * @return int32_t: Result code
     */
    virtual int32_t UnInit();

    /**
     * @brief Check if the module is initialized
     * @return ModuleInitStatus: Initialization status
     */
    virtual ModuleInitStatus IsInit() const;

    /**
     * @brief Get the interface of the module
     * @return std::shared_ptr<IInterface>: Shared pointer to the interface
     */
    virtual std::shared_ptr<IInterface> GetInterface() const;

protected:
private:
    ModuleInitStatus status_;
    std::shared_ptr<GuidanceControl> control_;
};

/**
 * @brief Guidance interface class
 */
class AURORA_EXPORT IGuidance : public IInterface {
public:
    virtual ~IGuidance() = default;

    /**
     * @brief Set or get parameters
     * @param param: Parameter to set
     * @return int32_t: Result code
     */
    virtual int32_t SetParam(const Param &param) = 0;
    virtual Param GetParam(const ParamType &type) const = 0;

    /**
     * @brief Set navigation route (main route + alternative routes, including main route index)
     * @param query: Path query
     * @param result: Path result
     * @param main_path_id: Main route ID
     * @return int32_t: Result code
     */
    virtual int32_t SetNavPath(path::PathQueryPtr query, path::PathResultPtr result, uint64_t main_path_id) = 0;

    /**
     * @brief Switch main route ID
     * @param target_path_id: Target main route ID
     * @return int32_t: Result code
     */
    virtual int32_t SwitchMainPathId(uint64_t target_path_id) = 0;

    /**
     * @brief Request route turn details
     * @param path_id: Route ID
     * @return int32_t: Result code
     */
    virtual int32_t RequestPathManeuverInfos(uint64_t path_id) = 0;

    /**
     * @brief Update map matching position
     * @param map_matching: Map matching result
     * @return int32_t: Result code
     */
    virtual int32_t UpdateMapMatchingPos(const loc::MatchResult &map_matching) = 0;

    /**
     * @brief Start navigation
     * @param type: Navigation mode
     * @return int32_t: Result code
     */
    virtual int32_t StartNavigation(NavigationMode type) = 0;

    /**
     * @brief Stop navigation
     * @param code: Stop code
     * @return int32_t: Result code
     */
    virtual int32_t StopNavigation(NavigationStopCode code) = 0;

    /**
     * @brief Pause navigation
     * @return int32_t: Result code
     */
    virtual int32_t PauseNavigation() = 0;

    /**
     * @brief Resume navigation
     * @return int32_t: Result code
     */
    virtual int32_t ResumeNavigation() = 0;

    /**
     * @brief Manually trigger navigation announcement
     * @return int32_t: Result code
     */
    virtual int32_t PlayNavigationManual() = 0;

    /**
     * @brief Add or remove navigation mode observer
     * @param obs: Observer to add or remove
     * @return int32_t: Result code
     */
    virtual int32_t AddListener(IGuidanceListenerPtr obs) = 0;
    virtual int32_t RemoveListener(IGuidanceListenerPtr obs) = 0;
};

}
}

#endif  // MAP_SRC_GUIDANCE_INCLUDE_GUIDANCE_MODULE_H
/* EOF */
