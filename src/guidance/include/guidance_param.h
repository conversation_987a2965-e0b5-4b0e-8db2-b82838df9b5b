#ifndef MAP_SRC_GUIDANCE_INCLUDE_GUIDANCE_PARAM_H
#define MAP_SRC_GUIDANCE_INCLUDE_GUIDANCE_PARAM_H

#include <cstdint>
#include <utility>

#define BIT(n)  ((static_cast<uint64_t>(1ul)) << (n))

namespace aurora {
namespace guide {

/**
 * @brief Enum for parameter types
 */
enum class ParamType : uint64_t {
    kGuideParamInvalid  = 0,  // Invalid parameter type
    kGuideParamTMC      = BIT(0),  // TMC real-time traffic configuration parameter type
    kGuideParamSAPA     = BIT(1),  // Service area and parking configuration parameter type
    kGuideParamCruise   = BIT(2),  // Cruise mode configuration parameter type
    kGuideParamNav      = BIT(3),  // Navigation mode configuration parameter type
    kGuideParamCamera   = BIT(4),  // Camera configuration parameter type
    kGuideParamExit     = BIT(5),  // Exit signpost configuration parameter type
    kGuideParamCrossing = BIT(6),  // Junction view configuration parameter type
    kGuideParamTTSPlay  = BIT(7),  // TTS announcement configuration parameter type
};

/**
 * @brief Navigation mode parameter configuration
 */
struct NavParam {
    int32_t nav_info_count;  // Number of navigation information items to display, default is 1 current navigation segment
    int32_t nav_scene;       // Navigation scene, 0: normal navigation, 1: scouting navigation, 2: escort navigation
    int32_t aim_driver;      // Driving object, person or vehicle. 1: person, 2: vehicle, default is 1
};

/**
 * @brief Distance range structure
 */
struct DistanceRange {
    double min;  // Minimum distance
    double max;  // Maximum distance
};

/**
 * @brief Cruise mode parameter configuration
 */
struct CruiseParam {
    bool enable_ad_code;     // Enable area announcement in cruise mode, default is true
    bool enable_camera;      // Enable camera announcement and display control, default is true
    bool enable_facility;    // Enable road facility control, default is true
    bool enable_traffic;     // Enable traffic condition announcement, default is true
    int32_t camera_num;      // Number of cameras to display, default is 10, maximum is 10
    int32_t facility_num;    // Number of facilities to display, default is 2
    DistanceRange camera_freeway_distance;  // Distance range for highway camera display and announcement
    DistanceRange camera_city_distance;     // Distance range for city road camera display and announcement
    DistanceRange default_camera_distance;  // Default distance range for camera display and announcement
};

/**
 * @brief TMC parameter configuration
 */
struct TMCParam {
    bool enable;                // Enable dynamic traffic function, default is true
    bool congestion_time;       // Enable congestion duration display, default is true
    bool road_close_reroute;    // Enable silent rerouting for road closure, default is true
    bool forbid_area_reroute;   // Enable silent rerouting for no-go areas, default is true
    bool restrict_area_route;   // Enable silent rerouting for restricted areas, default is true
    int32_t update_frequency;   // Update frequency in seconds, default is 65, must be greater than or equal to 30s
};

/**
 * @brief Service area and toll gate configuration parameters
 */
struct SAPAParam {
    bool enable_service_area;  // Enable service area display, default is true
    bool enable_toll_gate;     // Enable toll gate display, default is true
    int32_t max_count;         // Maximum number of items to display, default is 2
};

/**
 * @brief Lane parameter configuration
 */
struct LaneParam {
    bool enable;               // Enable lane parameter configuration
};

/**
 * @brief Exit parameter configuration
 */
struct ExitParam {
    bool enable_exit;          // Enable exit display, default is true
};

/**
 * @brief General parameter structure
 */
struct Param {
    ParamType type;            // Parameter type
    NavParam nav;              // Navigation parameters
    TMCParam tmc;              // TMC parameters
    SAPAParam sapa;            // Service area and toll gate parameters
    LaneParam lane;            // Lane parameters
};


}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDANCE_INCLUDE_GUIDANCE_PARAM_H
/* EOF */