#ifndef MAP_SRC_GUIDANCE_INCLUDE_GUIDANCE_LISTENER_H
#define MAP_SRC_GUIDANCE_INCLUDE_GUIDANCE_LISTENER_H

#include <cstdint>
#include <string>
#include <vector>
#include <memory>

#include "export_type_def.h"
#include "guidance_def.h"
#include "path_module.h"

namespace aurora {
namespace guide {

/**
 * @enum GuidanceListenerType
 * @brief Enum for guidance listener types.
 */
enum class GuidanceListenerType {
    kGuidanceListenerTypeNone = 0,  ///< None type
    kGuidanceListenerTypeNavigation,  ///< Navigation listener
    kGuidanceListenerTypeCruise,  ///< Cruise listener
    kGuidanceListenerTypeSound  ///< Sound listener
};

/**
 * @class IGuidanceListener
 * @brief Interface class for guidance listeners.
 */
class AURORA_EXPORT IGuidanceListener {
public:
    /**
     * @brief Constructor for IGuidanceListener.
     * @param type Type of the guidance listener.
     */
    IGuidanceListener(const GuidanceListenerType type)
    : type_(type) {
    }

    /**
     * @brief Get the type of the guidance listener.
     * @return The type of the guidance listener.
     */
    inline GuidanceListenerType GetType() const {
        return type_;
    }

    /**
     * @brief Virtual destructor for IGuidanceListener.
     */
    virtual ~IGuidanceListener() = default;

private:
    GuidanceListenerType type_;  ///< Type of the guidance listener
};

/**
 * @brief Navigation mode external notification callback
 */
class AURORA_EXPORT INavigationListener : public IGuidanceListener { 
public:
    INavigationListener() : IGuidanceListener(GuidanceListenerType::kGuidanceListenerTypeNavigation) {}
    virtual ~INavigationListener() = default;

    /**
     * @brief Route turn details
     * @note Trigger condition: This callback is triggered after receiving the route result.
     * @param path_id: The unique identifier for the route.
     * @param infos: A vector containing detailed maneuver information for the route.
     */
    virtual void OnPathManeuverInfos(int64_t path_id, const std::vector<NavigationManeuverInfoPtr> &infos) {}

    /**
     * @brief Navigation route basic information (ETA, distance, speed, speed limit, traffic lights, etc.)
     * @param nav_info: Navigation panel information. If it is empty, stop displaying the navigation panel.
     * @note Refresh frequency: 1 Hz
     */
    virtual void OnUpdateNavigationInfo(NavigationInfoPtr nav_info) {}

    /**
     * @brief Direction signpost information
     * @param infos: A vector containing signpost information.
     */
    virtual void OnUpdateSignPostInfos(const std::vector<GuidanceSignPostInfoPtr> &infos) {}

    /**
     * @brief Junction view information display or hide
     * @param info: Junction view information.
     */
    virtual void OnShowJunctionView(NavigationJunctionViewInfoPtr info) {}

    /**
     * @brief LaneInfo information display or hide
     * @param lane_info: Lane information.
     */
    virtual void OnShowLaneInfo(NavigationLaneInfoPtr lane_info) {}

    /**
     * @brief Update camera-related information
     * @param cameras: A vector containing camera information.
     */
    virtual void OnUpdateCamerasInfos(const std::vector<NavigationCameraInfoPtr> &cameras) {}

    /**
     * @brief Update road facility information
     * @param infos: A vector containing facility information.
     */
    virtual void OnUpdateFacilityInfos(const std::vector<NavigationFacilityInfoPtr>& infos) {}
    
    /**
     * @brief Update service area, parking, and other information
     * @param sapas: A vector containing service area, parking, and other information.
     */
    virtual void OnUpdateSAPAInfos(const std::vector<NavigationSAPAInfoPtr> &sapas) {}

    /**
     * @brief Notify waypoint index when passing through waypoints during navigation
     * @param type: The navigation mode.
     * @param via_idx: The index of the waypoint being passed.
     */
    virtual void OnUpdateWayPointPass(const NavigationMode type, int32_t via_idx) {}

    /**
     * @brief Notify arrival at destination
     * @param type: The navigation mode.
     */
    virtual void OnNavigationArrive(const NavigationMode type) {}

    /**
     * @brief Notify navigation start
     * @param type: The navigation mode.
     * @param path_id: The unique identifier for the route.
     */
    virtual void OnNavigationStart(const NavigationMode type, uint64_t path_id) {}

    /**
     * @brief Notify navigation pause
     * @param type: The navigation mode.
     */
    virtual void OnNavigationPause(const NavigationMode type) {}

    /**
     * @brief Notify navigation resume
     * @param type: The navigation mode.
     */
    virtual void OnNavigationResume(const NavigationMode type) {}

    /**
     * @brief Notify navigation stop
     * @param type: The navigation mode.
     * @param code: The stop code indicating the reason for stopping.
     */
    virtual void OnNavigationStop(const NavigationMode type, const NavigationStopCode code) {}

    /**
     * @brief Guide notification to reroute
     * @note The guide engine will notify the external system to recalculate the route due to reasons such as deviation, road restrictions, or traffic congestion.
     * @param query: The path query information for rerouting.
     */
    virtual void OnReroute(const path::PathQueryPtr query) {}

    /**
     * @brief Update route light bar
     * @param info: Light bar information.
     */
    virtual void OnUpdateTmcLightBar(NavigationLightBarInfoPtr info) {}

    /**
     * @brief Update traffic congestion duration and reason on the route
     * @param info: Congestion information.
     */
    virtual void OnUpdateTmcCongestionInfo(NavigationCongestionInfoPtr info) {}

    /**
     * @brief Update traffic event information on the route
     * @param infos: A vector containing traffic event information.
     */
    virtual void OnUpdatePathTmcEvents(const std::vector<NavigationPathTmcEventInfoPtr> &infos) {}

    /**
     * @brief Switch route callback
     * @param path_id: The unique identifier for the route.
     * @param success: Indicates whether the route switch was successful.
     */
    virtual void OnSwitchPathStatus(uint64_t path_id, bool success) {}

    /**
     * @brief Update toll station information
     * @param info: Toll station information.
     */
    virtual void OnUpdateTollStationInfo(NavigationTollStationInfoPtr info) {}
};

/**
 * @brief Cruise mode external notification callback
 */
class AURORA_EXPORT ICruiseListener : public IGuidanceListener {
public:
    ICruiseListener() : IGuidanceListener(GuidanceListenerType::kGuidanceListenerTypeCruise) {}

    // Forward turn, intersection, and traffic light information
    // Disable specific guidance information (speed cameras, speed limits, etc.)
    // Configurable enable/disable of maneuver points
    virtual ~ICruiseListener() = default;

    /**
     * @brief Update cruise panel information, 1 Hz
     * @param info: Cruise panel information.
     */
    virtual void OnUpdateCruiseInfo(CruiseInfoPtr info) {}

    /**
     * @brief Update camera information
     * @param infos: A vector containing camera information.
     */
    virtual void OnShowCamerasInfos(const std::vector<CruiseCameraInfoPtr> &infos) {}

    /**
     * @brief Update traffic condition information ahead
     * @param info: Traffic condition information.
     */
    virtual void OnUpdateCongestionInfo(CruiseCongestionInfoPtr info) {}
};

/**
 * @brief Notification callback for voice announcements
 */
class AURORA_EXPORT ISoundListener : public IGuidanceListener {
public:
    ISoundListener() : IGuidanceListener(GuidanceListenerType::kGuidanceListenerTypeSound) {}

    virtual ~ISoundListener() = default;

    /**
     * @brief Play ringtone of a specific scene
     * @param scene_id: The ID of the scene for which the ring should be played.
     */
    virtual void OnPlayRing(const TTSScenePlay scene_id) = 0;

    /**
     * @brief Request to play TTS
     * @param info: TTS information.
     */
    virtual void OnPlayTTS(GuidanceSoundInfoPtr info) = 0;
    
    /**
     * @brief Check if currently playing
     * @return true if currently playing, false otherwise.
     */
    virtual bool IsPlaying() = 0;
};

using IGuidanceListenerPtr = std::shared_ptr<IGuidanceListener>;
using INavigationListenerPtr = std::shared_ptr<INavigationListener>;
using ICruiseListenerPtr = std::shared_ptr<ICruiseListener>;
using ISoundListenerPtr = std::shared_ptr<ISoundListener>;

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDANCE_INCLUDE_GUIDANCE_LISTENER_H
/* EOF */
