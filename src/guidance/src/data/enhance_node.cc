#include "guidance/src/data/enhance_node.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/common/geo_util.h"
#include "guidance/src/common/guide_constant.h"

namespace aurora {
namespace guide {

EnhanceNode::EnhanceNode(const RouteNodeId &route_node_id, 
                        EnhanceDataProvider *provider, 
                        DirectEdgeId *next_non_intersection_edge_id,
                        DirectEdgeId *prev_edge_id) {
    
    BuildNodeProperty(provider, route_node_id);
    BuildForwardBranches(provider, route_node_id, next_non_intersection_edge_id);
    BuildBackwardBranchForNonStreetIntersection(provider, route_node_id, prev_edge_id);
}

void EnhanceNode::UpdateXEdgePrevNameConsistency(std::shared_ptr<StreetNames> prev_street_names) {
    GUIDE_ASSERT(prev_street_names != nullptr);

    for (auto &xedge : out_branches_) {
        if (prev_street_names == nullptr) {
            GUIDE_LOG_ERROR("prev_street_name is nullptr .");
            xedge.SetPrevNameConssitency(false);
        } else {
            if (xedge.GetStreetNames()->HasCommonStreetNames(*prev_street_names)) {
                xedge.SetPrevNameConssitency(true);
            } else {
                xedge.SetPrevNameConssitency(false);
            }
        }
    }
}

void EnhanceNode::UpdateXEdgeCurrNameConsistency(std::shared_ptr<StreetNames> curr_street_names) {
    GUIDE_ASSERT(curr_street_names != nullptr);

    for (auto &xedge : in_branches_) {
        if (curr_street_names == nullptr) {
            GUIDE_LOG_ERROR("curr_street_name is nullptr .");
            xedge.SetCurrNameConsistency(false);
        } else {
            if (xedge.GetStreetNames()->HasCommonStreetNames(*curr_street_names)) {
                xedge.SetCurrNameConsistency(true);
            } else {
                xedge.SetCurrNameConsistency(false);
            }
        }
    }
}

void EnhanceNode::SetDirectEdge(DirectedEdge&& edge) {
    direct_edge_ = std::move(edge);
}

void EnhanceNode::UpdateSignPosts(EnhanceDataProvider *provider, 
                                  const DirectEdgeId &from_edge_id, 
                                  const DirectEdgeId *to_edge_ids,
                                  int32_t to_edge_num) {

    GUIDE_ASSERT((to_edge_ids != nullptr) && (to_edge_num > 0));
    if ((to_edge_ids == nullptr) || (to_edge_num == 0)) {
        GUIDE_LOG_ERROR("UpdateSignPosts param error ...");
        return;
    }

    int32_t sign_post_num = 0;
    SignPostInfo *sign_posts = provider->GetSignPostInfo(from_edge_id, sign_post_num);
    DirectEdgeId to_direct_id;
    bool to_direct_id_flag = false;

    if ((sign_posts != nullptr) && (sign_post_num > 0)) {
        std::set<DirectEdgeId> all_to_edge_ids;
        for (int32_t sign_post_idx = 0; sign_post_idx < sign_post_num; ++sign_post_idx) {
            DirectEdgeId to_edge_id = from_edge_id;

            to_edge_id.edge_id.tile_id.adcode = from_edge_id.edge_id.tile_id.adcode;
            to_edge_id.edge_id.tile_id.level = 0;
            to_edge_id.edge_id.tile_id.mesh_row = sign_posts[sign_post_idx].GetBaseInfo()->out_mesh_row;
            to_edge_id.edge_id.tile_id.mesh_col = sign_posts[sign_post_idx].GetBaseInfo()->out_mesh_col;
            to_edge_id.edge_id.tile_id.tile_id = sign_posts[sign_post_idx].GetBaseInfo()->out_tile_id;
            to_edge_id.edge_id.feature_id = sign_posts[sign_post_idx].GetBaseInfo()->out_edge_id;
            to_edge_id.forward = (sign_posts[sign_post_idx].GetBaseInfo()->out_edge_dir == 0) ? 1:0;

            all_to_edge_ids.insert(to_edge_id);
        }

        // 优先查找距离自车最近的sign post
        for (int32_t sign_post_idx = 0; sign_post_idx < to_edge_num; ++sign_post_idx) {
            if (all_to_edge_ids.count(to_edge_ids[sign_post_idx]) > 0) {
                to_direct_id = to_edge_ids[sign_post_idx];
                to_direct_id_flag = true;
                break;
            }
        }

        // GUIDE_ASSERT(to_direct_id_flag);
    }

    if (!to_direct_id_flag) {
        return;
    }

    sign_post_num = 0;
    SignPostInfo *sign_post = provider->GetSignPostInfo(from_edge_id, to_direct_id, sign_post_num);
    if ((sign_post != nullptr) && (sign_post_num > 0)) {
        // 优先查找距离自车最近的sign post
        // GUIDE_LOG_INFO("++++ Found sign post +++++++");
        for (int32_t sign_post_idx = 0; sign_post_idx < sign_post_num; ++sign_post_idx) {
            GUIDE_ASSERT((sign_post + sign_post_idx) != nullptr);

            if (((sign_post + sign_post_idx) != nullptr) && ((sign_post + sign_post_idx)->GetSignInfoNameCount() > 0)) {
                EnhanceSignPostPtr ptr = std::make_shared<EnhanceSignPost>(sign_post + sign_post_idx, 
                                                        coord_, from_edge_id, to_direct_id);
                sign_posts_.emplace_back(ptr);
#if 1
                for (int32_t i = 0; i < ptr->GetNameCount(); ++i) {
                    GUIDE_LOG_INFO("SignPost[{}][{}]: name={}, type={}", ptr->GetNameCount(), i,
                        ptr->GetName(i), static_cast<int32_t>(ptr->GetType()));
                }
#endif
            } // if
        } // for
    } else {
        GUIDE_ASSERT(false);
    }
}


void EnhanceNode::UpdateJunctionViews(EnhanceDataProvider *provider, 
                                      const DirectEdgeId &from_edge_id, 
                                      const DirectEdgeId *to_edge_ids,
                                      int32_t to_edge_num) {

    GUIDE_ASSERT((to_edge_ids != nullptr) && (to_edge_num > 0));
    if ((to_edge_ids == nullptr) || (to_edge_num == 0)) {
        GUIDE_LOG_ERROR("UpdateJunctionViews param error ...");
        return;
    }

    int32_t junction_view_num = 0;
    JuncviewInfo *junction_views = provider->GetJunctionViewInfo(from_edge_id, junction_view_num);
    DirectEdgeId to_direct_id;
    bool to_direct_id_flag = false;

    if ((junction_views != nullptr) && (junction_view_num > 0)) {
        std::set<DirectEdgeId> all_to_edge_ids;
        for (int32_t junction_idx = 0; junction_idx < junction_view_num; ++junction_idx) {
            DirectEdgeId to_edge_id = from_edge_id;

            to_edge_id.edge_id.tile_id.adcode = from_edge_id.edge_id.tile_id.adcode;
            to_edge_id.edge_id.tile_id.level = 0;
            to_edge_id.edge_id.tile_id.mesh_row = junction_views[junction_idx].GetBaseInfo()->out_mesh_row;
            to_edge_id.edge_id.tile_id.mesh_col = junction_views[junction_idx].GetBaseInfo()->out_mesh_col;
            to_edge_id.edge_id.tile_id.tile_id = junction_views[junction_idx].GetBaseInfo()->out_tile_id;
            to_edge_id.edge_id.feature_id = junction_views[junction_idx].GetBaseInfo()->out_edge_id;
            to_edge_id.forward = (junction_views[junction_idx].GetBaseInfo()->out_edge_dir == 0) ? 1:0;
            
            all_to_edge_ids.insert(to_edge_id);
        }

        // 优先查找距离自车最近的junction view
        for (int32_t junction_idx = 0; junction_idx < to_edge_num; ++junction_idx) {
            if (all_to_edge_ids.count(to_edge_ids[junction_idx]) > 0) {
                to_direct_id = to_edge_ids[junction_idx];
                to_direct_id_flag = true;
                break;
            }
        }

        GUIDE_ASSERT(to_direct_id_flag);
    }

    if (!to_direct_id_flag) {
        return;
    }

    junction_view_num = 0;
    JuncviewInfo *junction_view = provider->GetJunctionViewInfo(from_edge_id, to_direct_id, junction_view_num);
    if ((junction_view != nullptr) && (junction_view_num > 0)) {
        // 优先查找距离自车最近的junction view
        GUIDE_LOG_INFO("++++ Found junction view +++++++");
        for (int32_t junction_view_idx = 0; junction_view_idx < junction_view_num; ++junction_view_idx) {
            GUIDE_ASSERT((junction_view + junction_view_idx) != nullptr);

            if ((junction_view + junction_view_idx) != nullptr) {
                EnhanceJunctionViewPtr ptr = std::make_shared<EnhanceJunctionView>(junction_view + junction_view_idx, from_edge_id, to_direct_id);
                junction_views_.emplace_back(ptr);
            } // if 
        } // for
    } else {
        GUIDE_ASSERT(false);
    }
}

void EnhanceNode::UpdateTollStations(EnhanceDataProvider *provider, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id) {
    TollGateInfo *toll_station = provider->GetTollStationInfo(from_edge_id, to_edge_id);
    if (toll_station != nullptr) {
        toll_station_ = std::make_shared<EnhanceTollStation>(toll_station, from_edge_id, to_edge_id);
        GUIDE_LOG_INFO("++++++++++ Found toll station: {} +++++++++++++", toll_station->GetName());
    } // if
}

bool EnhanceNode::HasOutIntersectionEdge() const {
    return !out_branches_.empty();
}

bool EnhanceNode::HasInIntersectionEdge() const {
    return !in_branches_.empty();
}

const OutIntersectionBranch* EnhanceNode::GetOutIntersectionBranch(int32_t index) const {
    if (index < out_branches_.size()) {
        return (out_branches_.data() + index);
    }
    return nullptr;
}

const InIntersectionBranch* EnhanceNode::GetInIntersectionBranch(int32_t index) const {
    if (index < in_branches_.size()) {
        return (in_branches_.data() + index);
    }

    return nullptr;
}

const PointLL& EnhanceNode::GetPosition() const {
    return coord_;
}

DirectedEdge* EnhanceNode::GetDirectEdge() {
    if (direct_edge_.IsValid()) {
        return &direct_edge_;
    }
    return nullptr;
}

void EnhanceNode::BuildNodeProperty(EnhanceDataProvider *provider, const RouteNodeId& route_node_id) {
    RouteNode* route_node = provider->GetRouteNode(route_node_id);
    GUIDE_ASSERT(route_node != nullptr);
//    has_traffic_light_ = route_node->GetBaseInfo()->has_traffic_light;
    coord_ = route_node->GetPosition();
}


void EnhanceNode::BuildForwardBranches(EnhanceDataProvider *provider, const RouteNodeId &node_id, 
                                DirectEdgeId *next_non_intersection_edge_id) {

    GraphTraverse traverse(provider);
    if (next_non_intersection_edge_id != nullptr) {
        traverse.ExcludeDirectEdgeIds({*next_non_intersection_edge_id});
    }

    traverse.Travsrse(node_id);
    uint32_t num = traverse.GetBranchNum();

    for(uint32_t idx = 0; idx < num; ++idx) {
        std::vector<TraverseEdgeMeta>* val = traverse.GetBranch(idx);
        if (val != nullptr) {
            out_branches_.emplace_back(OutIntersectionBranch(provider, val));
        }
    }
}

void EnhanceNode::BuildBackwardBranchForNonStreetIntersection(EnhanceDataProvider *provider,
                                                              const RouteEdgeId &node_id,
                                                              DirectEdgeId *prev_edge_id) {
    
    if (IsInternalIntesection()) {
        return;
    }

    GUIDE_ASSERT(provider != nullptr);
    if (provider == nullptr) {
        GUIDE_LOG_ERROR("provider is null .");
        return;
    }

    if (prev_edge_id == nullptr) { // first link ignore
        return;
    }

    std::vector<DirectEdgeId>  direct_ids;
    provider->GetNodeInEdgeIds(node_id, direct_ids);

    in_branches_.clear();

    for (auto & direct_id : direct_ids) {        
        if (direct_id != *prev_edge_id) {
            // 双向暂时不考虑
            TopolEdge *topo = provider->GetTopoEdge(direct_id.edge_id);
            if (topo != nullptr) {
                if (topo->GetBaseInfo()->direction == parser::EdgeDirection::kEdgeDirectionForwardPass ||
                    topo->GetBaseInfo()->direction == parser::EdgeDirection::kEdgeDirectionReversePass) {
                    in_branches_.emplace_back(provider, direct_id);
                } // if
            } // if
        } // if
    } // for
}

bool EnhanceNode::IsFork() const {
    if (out_branches_.empty()) {
        return false;
    }

    for (const auto &branch : out_branches_) {
        if (branch.HasInnerEdge()) {
            return false;
        }
    }
    return true;
}

bool EnhanceNode::IsMotorwayJunction() const {
    if (out_branches_.size() > 0 || in_branches_.size() > 0) {
        if (direct_edge_.IsValid() && direct_edge_.IsHighway()) {
            return true;
        }

        for (const auto& branch : out_branches_) {
            if (branch.IsHighway()) {
                return true;
            }
        }
        
        for (const auto& branch : in_branches_) {
            if (branch.IsHighway()) {
                return true;
            }
        }
    }

    return false;
}

bool EnhanceNode::IsInternalIntesection() const {
    if (out_branches_.empty()) {
        return false;
    }

    for (const auto &branch : out_branches_) {
        if (branch.HasInnerEdge()) {
            return true;
        }
    }
    return false;
}

bool EnhanceNode::HasXEdgeNameConssitency() const {
    for (const auto &branch : out_branches_) {
        if (branch.GetPrevNameConsistency()) {
            return true;
        }
    }

    for (const auto &branch : in_branches_) {
        if (branch.GetCurrNameConsistency()) {
            return true;
        }
    }

    return false;
}

bool EnhanceNode::HasForwardIntersectionEdge(uint32_t from_heading) {
    for (int i = 0; i < out_branches_.size(); ++i) {
        if (geo::IsForward(geo::CalcTurnDegree360(from_heading, out_branches_[i].GetBeginHeading()))) {
            return true;
        }
    }
    return false;
}

bool EnhanceNode::HasOnlyForwardIntersectionEdge(uint32_t from_heading) {
    if (out_branches_.empty()) {
        return false;
    }

    for (auto &branch : out_branches_) {
        if (branch.IsRamp()) { // Can not be a ramp or turn channel
            return false;
        }

        if (geo::IsForkForward(geo::CalcTurnDegree360(from_heading, branch.GetBeginHeading()))) {
            continue;
        } else {
            return false;
        }
    }
    return true;
}

bool EnhanceNode::HasNonBackwardTraversableSameNameRampIntersectingEdge(uint32_t from_heading) {
    for (int32_t index = 0; index < out_branches_.size(); ++index) {
        const auto& xedge = out_branches_[index];

        if (xedge.GetPrevNameConsistency() && xedge.IsRamp()) {
            uint32_t intersect_turn_degree = geo::CalcTurnDegree360(from_heading, xedge.GetBeginHeading());
            bool non_backward = !((intersect_turn_degree > kBackwardTurnDegreeLowerBound) && 
                    (intersect_turn_degree < kBackwardTurnDegreeUpperBound));
            
            if (non_backward) {
                return true;
            }
        }
    }

    return false;
}

bool EnhanceNode::HasWideForwardIntersectionEdge(uint32_t from_heading) {
    for (int32_t i = 0; i < out_branches_.size(); ++i) {
        if (geo::IsWideForward(geo::CalcTurnDegree360(from_heading, out_branches_[i].GetBeginHeading()))) {
            return true;
        }
    }
    return false;
}

bool EnhanceNode::HasSpecifiedTurnXEdge(const Turn::Type turn_type, uint32_t from_heading) {
    for (int32_t i = 0; i < out_branches_.size(); ++i) {
        if (Turn::GetType(geo::CalcTurnDegree360(from_heading, 
                                                out_branches_[i].GetBeginHeading())) == turn_type) {
            return true;
        }
    }

    return false;
}

bool EnhanceNode::HasSimilarStraightRoadClassXEdge(uint32_t path_turn_degree, 
                                                  uint32_t from_heading, 
                                                  int8_t road_class) {

    // TODO: road_class 暂未使用，后续需要做优化调整
    for (int32_t i = 0; i < out_branches_.size(); ++i) {
        const OutIntersectionBranch &xedge = out_branches_[i];
        uint32_t xedge_turn_degree = geo::CalcTurnDegree360(from_heading, xedge.GetBeginHeading());
        uint32_t path_xedge_turn_degree_delta = geo::CalcTurnDegree180(path_turn_degree, xedge_turn_degree);

        if (geo::IsRelativeStraight(path_turn_degree) && geo::IsRelativeStraight(xedge_turn_degree) &&
            path_xedge_turn_degree_delta <= kSimilarStraightThreshold) {
                return true;
            }
    }

    return false;
}

bool EnhanceNode::HasSimilarStraightNonRampOrSameNameRampXEdge(uint32_t path_turn_degree, 
                                                               uint32_t from_heading) {
    for (int32_t i = 0; i < out_branches_.size(); ++i) {
        const OutIntersectionBranch &xedge = out_branches_[i];
        uint32_t xedge_turn_degree = geo::CalcTurnDegree360(from_heading, xedge.GetBeginHeading());
        uint32_t path_xedge_turn_degree_delta = geo::CalcTurnDegree180(path_turn_degree, xedge_turn_degree);

        // if the intersecting edge is straight
        // and is traversable based on mode
        // and is not a ramp OR ramp with same previous edge name
        if (geo::IsRelativeStraight(path_turn_degree) && geo::IsRelativeStraight(xedge_turn_degree) &&
            path_xedge_turn_degree_delta <= kSimilarStraightThreshold && 
            (!xedge.IsRamp() || (xedge.IsRamp() && xedge.GetPrevNameConsistency()))) {
            return true;
        }
    }
    return false;
}

bool EnhanceNode::HasForwardRampXEdge(uint32_t from_heading) {
    for (int32_t i = 0; i < out_branches_.size(); ++i) {
        const OutIntersectionBranch &xedge = out_branches_[i];
        if (geo::IsForward(geo::CalcTurnDegree360(from_heading, xedge.GetBeginHeading())) && xedge.IsRamp()) {
            return true;
        }
    }
    return false;
}

uint32_t EnhanceNode::GetStraightestIntersectionEdgeTurnDegree(uint32_t from_heading) {
    uint32_t straightest_turn_degree = 180;
    uint32_t straightest_delta = 180;

    for (int i = 0; i < out_branches_.size(); ++i) {
        uint32_t intersect_turn_degree = geo::CalcTurnDegree360(from_heading, out_branches_[i].GetBeginHeading());
        uint32_t straight_delta = (intersect_turn_degree > 180) ? (360 - intersect_turn_degree) : intersect_turn_degree;
        if (straight_delta < straightest_delta) {
            straightest_delta = straight_delta;
            straightest_turn_degree = intersect_turn_degree;
        }
    }

    return straightest_turn_degree;
}

bool EnhanceNode::IsStraightestIntersectingEdgeReversed(uint32_t from_heading) {
    // 判断最直的道路跟prev_edge是否是反向
    uint32_t straightest_turn_degree = GetStraightestIntersectionEdgeTurnDegree(from_heading);
    if (straightest_turn_degree > 124 && straightest_turn_degree < 236) {
        return true;
    }
    return false;
}

uint32_t EnhanceNode::GetRightMostTurnDegree(uint32_t turn_degree, uint32_t from_heading) {
    // calc delta to right axis
    auto get_right_delta = [](uint32_t turn_degree) -> uint32_t {
        if (turn_degree < 90) {
            return (90 - turn_degree);
        } else if (turn_degree > 270) {
            return (360 - turn_degree + 90);
        } else {
            return (turn_degree - 90);
        }
    };

    uint32_t right_most_turn_degree = turn_degree;
    uint32_t right_most_delta = get_right_delta(turn_degree);

    for (int i = 0; i < out_branches_.size(); ++i) {
        uint32_t xturn_degree = geo::CalcTurnDegree360(from_heading, out_branches_[i].GetBeginHeading());

        uint32_t right_delta = get_right_delta(xturn_degree);
        // Determine if the intersecting edge turn degree is closer to true right (90)
        if (right_delta < right_most_delta) {
            right_most_delta = right_delta;
            right_most_turn_degree = xturn_degree;
        }
    }
    return right_most_turn_degree;
}

uint32_t EnhanceNode::GetLeftMostTurnDegree(uint32_t turn_degree, uint32_t from_heading) {
    auto get_left_delta = [](uint32_t turn_degree) -> uint32_t {
        if (turn_degree < 90) {
            return (90 + turn_degree);
        } else if (turn_degree < 270) {
            return (270 - turn_degree);
        } else {
            return (turn_degree - 270);
        }
    };

    uint32_t left_most_turn_degree = turn_degree;
    uint32_t left_most_delta = get_left_delta(turn_degree);

    for (int i = 0; i < out_branches_.size(); ++i) {
        uint32_t xturn_degree = geo::CalcTurnDegree360(from_heading, out_branches_[i].GetBeginHeading());
        uint32_t left_delta = get_left_delta(xturn_degree);
        // Determine if the intersecting edge turn degree is closer to true left (270)
        if (left_delta < left_most_delta) {
            left_most_delta = left_delta;
            left_most_turn_degree = xturn_degree;
        }
    }
    return left_most_turn_degree;
}


void EnhanceNode::CalculateRightLeftIntersectingEdgeCounts(int16_t from_heading, IntersectingEdgeCounts &xedge_count) {
    xedge_count.Clear();
    if (out_branches_.size() == 0) {
        return;
    }

    if (!direct_edge_.IsValid()) {
        GUIDE_ASSERT(false);
        return;
    }

    uint32_t path_turn_degree = geo::CalcTurnDegree360(from_heading, direct_edge_.GetBeginHeading());
    for (int32_t i = 0; i < out_branches_.size(); ++i) {
        uint32_t intersection_turn_degree = geo::CalcTurnDegree360(from_heading, out_branches_[i].GetBeginHeading());

        if (path_turn_degree > 180) { // left turn
            if ((intersection_turn_degree > path_turn_degree) || (intersection_turn_degree < 180)) {
                ++xedge_count.right;

                if (geo::IsSimilarTurnDegree(path_turn_degree, intersection_turn_degree, true)) {
                    ++xedge_count.right_similar;
                }
            } else if ((intersection_turn_degree < path_turn_degree) && (intersection_turn_degree > 180)) {
                ++xedge_count.left;

                if (geo::IsSimilarTurnDegree(path_turn_degree, intersection_turn_degree, false)) {
                    ++xedge_count.left_similar;
                }
            }
        } else { // right turn
            if ((intersection_turn_degree > path_turn_degree) && (intersection_turn_degree < 180)) {
                ++xedge_count.right;

                if (geo::IsSimilarTurnDegree(path_turn_degree, intersection_turn_degree, true)) {
                    ++xedge_count.right_similar;
                }
            } else if ((intersection_turn_degree < path_turn_degree) || (intersection_turn_degree > 180)) {
                ++xedge_count.left;
                if (geo::IsSimilarTurnDegree(path_turn_degree, intersection_turn_degree, false)) {
                    ++xedge_count.left_similar;
                }
            }
        }
    } // for
}

void EnhanceNode::CalculateInEdgeRightLeftForNonStreetIntersection(int16_t prev_end_heading, 
                                                                   IntersectingEdgeSimpleCounts &xedge_count) {

    xedge_count.Clear();
    if (IsInternalIntesection()) {
        return;
    }

    if (in_branches_.empty()) {
        return;
    }

    for (const auto &in_branch : in_branches_) {
        uint16_t xedge_heading = in_branch.GetEndHeading();

        uint32_t turn_degree = geo::CalcTurnDegree360(xedge_heading, prev_end_heading);
        if (turn_degree > 180) { 
            ++xedge_count.left;
        } else {
            ++xedge_count.right;
        }
    }
}

int32_t EnhanceNode::GetSignPostNum() const {
    return sign_posts_.size();
}

EnhanceSignPostPtr EnhanceNode::GetSignPost(int32_t index) const {
    if (index < sign_posts_.size()) {
        return sign_posts_.at(index);
    }
    GUIDE_ASSERT(false);
    return nullptr;
}

const std::vector<EnhanceSignPostPtr>& EnhanceNode::GetSignPosts() const {
    return sign_posts_;
}

int32_t EnhanceNode::GetJunctionViewNum() const {
    return junction_views_.size();
}

EnhanceJunctionViewPtr EnhanceNode::GetJunctionView(int32_t index) const {
    if (index < junction_views_.size()) {
        return junction_views_.at(index);
    }
    GUIDE_ASSERT(false);
    return nullptr;
}

const std::vector<EnhanceJunctionViewPtr>& EnhanceNode::GetJunctionViews() const {
    return junction_views_;
}

EnhanceJunctionViewPtr EnhanceNode::GetRecommandJunctionView() const {
    if (junction_views_.size() == 0) {
        return nullptr;
    }

    if (junction_views_.size() == 1U) {
        return junction_views_[0];
    }

    int32_t real_view_idx = -1;
    int32_t direction_board_idx = -1;
    int32_t diagram_view_idx = -1;

    int32_t index = 0;
    for (const auto& view : junction_views_) {
        if (view->GetType() == JunctionViewType::kRealView) {
            real_view_idx = index;
        } else if (view->GetType() == JunctionViewType::kDirectionBoard) {
            direction_board_idx = index;
        } else if (view->GetType() == JunctionViewType::kDiagramView) {
            diagram_view_idx = index;
        }
        ++index;
    }

    if (real_view_idx >= 0) {
        return junction_views_[real_view_idx];
    }

    if (direction_board_idx >= 0) {
        return junction_views_[direction_board_idx];
    }

    if (diagram_view_idx >= 0) {
        return junction_views_[diagram_view_idx];
    }

    return junction_views_[0];
}


bool EnhanceNode::HasTollStations() const {
    return (toll_station_ != nullptr);
}

EnhanceTollStationPtr EnhanceNode::GetTollStation() const {
    return toll_station_;
}


}  // namespace guide
}  // namespace aurora
/* EOF */
