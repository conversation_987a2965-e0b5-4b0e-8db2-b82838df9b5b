#ifndef MAP_SRC_GUIDE_SRC_DATA_ENHANCE_SIGN_POST_H
#define MAP_SRC_GUIDE_SRC_DATA_ENHANCE_SIGN_POST_H

#include <cstdint>
#include <memory>

#include "guidance_def.h"
#include "route_data/route_data_def.h"
#include "guidance/src/data/enhance_data_provider.h"


namespace aurora {
namespace guide {

class EnhanceSignPost {
public:
    struct Name {
        std::string name;
        uint32_t consecutive_count;

        Name(const std::string& name, uint32_t count) {
            this->name = name;
            this->consecutive_count = count;
        }
    };
    
    EnhanceSignPost(SignPostInfo *post_info, const PointLL &lnglat, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_end_id);
    ~EnhanceSignPost() = default;

    GuidanceSignPostType GetType() const;
    std::string GetName(int32_t index) const;
    int32_t GetNameCount() const;
    std::vector<std::string> GetNames() const;
    uint32_t GetConsecutiveCount(uint32_t index) const;
    void     SetConsecutiveCount(uint32_t index, uint32_t num);

    DirectEdgeId GetFromEdgeId() const;
    DirectEdgeId GetToEdgeId() const;

    std::string GetId() const;
    PointLL GetPosition() const;

protected:
    void BuildSignPost(SignPostInfo *post_info);

protected:
    GuidanceSignPostType type_;
    PointLL lnglat_;
    std::vector<Name> names_;
    DirectEdgeId from_edge_id_;
    DirectEdgeId to_edge_id_;
};

using EnhanceSignPostPtr = std::shared_ptr<EnhanceSignPost>;

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_ENHANCE_SIGN_POST_H
/* EOF */
