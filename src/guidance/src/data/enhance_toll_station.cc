#include "guidance/src/data/enhance_toll_station.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

EnhanceTollStation::EnhanceTollStation(TollGateInfo *info, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id) {
    GUIDE_ASSERT(info != nullptr);

    from_edge_id_ = from_edge_id;
    to_edge_id_ = to_edge_id;

    if (info != nullptr) {
        BuildTollStation(info);
    }
}

ImagePtr EnhanceTollStation::GetImage() const {
    return image_;
}
    
std::string EnhanceTollStation::GetName() const {
    return name_;
}

    
DirectEdgeId EnhanceTollStation::GetFromEdgeId() const {
    return from_edge_id_;
}
    
DirectEdgeId EnhanceTollStation::GetToEdgeId() const {
    return to_edge_id_;
}

    
uint32_t EnhanceTollStation::GetTollPortNum() const {
    return ports_.size();
}

    
GuidanceTollgateGateInfo* EnhanceTollStation::GetTollPort(int32_t index) {
    if (index < ports_.size()) {
        return (ports_.data() + index);
    }

    GUIDE_ASSERT(false);
    return nullptr;
}

const std::vector<GuidanceTollgateGateInfo>& EnhanceTollStation::GetTollPorts() const {
    return ports_;
}

std::string EnhanceTollStation::GetId() const {
    return from_edge_id_.ToString() + "_" + to_edge_id_.ToString();
}

GuidanceTollgateGateInfo EnhanceTollStation::Convert(const parser::TollgateGateInfo& port_info) {

    GuidanceTollgateGateInfo info;
    info.cash = port_info.cash;
    info.etc = port_info.etc;
    info.auto_card = port_info.auto_card;
    info.alipay = port_info.alipay;
    info.wechat = port_info.wechat;
    info.itc = port_info.itc;
    info.not_used1 = port_info.not_used1;
    info.normal = port_info.normal;
    info.hk_macao = port_info.hk_macao;
    info.general = port_info.general;
    info.wide_lane = port_info.wide_lane;
    info.not_used2 = port_info.not_used2;

    return info;
}

void EnhanceTollStation::BuildTollStation(TollGateInfo *info) {
    GUIDE_ASSERT(info != nullptr);
    GUIDE_ASSERT(from_edge_id_.edge_id.feature_id == info->GetBaseInfo()->in_edge_id);
    GUIDE_ASSERT(from_edge_id_.forward == !(info->GetBaseInfo()->in_edge_dir));
    GUIDE_ASSERT(info->GetBaseInfo()->is_same_mesh);
    GUIDE_ASSERT(info->GetBaseInfo()->is_same_tile);


    GUIDE_ASSERT(to_edge_id_.edge_id.feature_id == info->GetBaseInfo()->out_edge_id);
    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.mesh_col == info->GetBaseInfo()->out_mesh_col);
    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.mesh_row == info->GetBaseInfo()->out_mesh_row);

    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.tile_id == info->GetBaseInfo()->out_tile_id);
    GUIDE_ASSERT(to_edge_id_.edge_id.feature_id == info->GetBaseInfo()->out_edge_id);
    GUIDE_ASSERT(to_edge_id_.forward == !(info->GetBaseInfo()->out_edge_dir));

    if (info->GetName() != nullptr) {
        name_ = info->GetName();
    } else {
        GUIDE_ASSERT(false);
        name_ = "";
    }
    image_ = info->GetBackground();

    int32_t port_num = info->GetTollgateGateCount();
    GUIDE_ASSERT(port_num > 0);
    ports_.reserve(port_num);
    for (int32_t port_index = 0; port_index < port_num; ++port_index) {
        auto port_info = info->GetTollgateGateInfo(port_index);
        GUIDE_ASSERT(port_info != nullptr);

        if (port_info != nullptr) {
            ports_.emplace_back(Convert(*port_info));
        } else {
            GUIDE_LOG_ERROR("toll station port is nullptr .");
        }
    }
}

}  // namespace guide
}  // namespace aurora
/* EOF */
