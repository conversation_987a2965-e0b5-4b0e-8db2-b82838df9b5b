#include "guidance/src/data/enhance_sign_post.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

EnhanceSignPost::EnhanceSignPost(SignPostInfo *post_info, const PointLL &lnglat, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id) 
: lnglat_(lnglat)
, from_edge_id_(from_edge_id)
, to_edge_id_(to_edge_id) {

    GUIDE_ASSERT(post_info != nullptr);

    if (post_info != nullptr) {
        BuildSignPost(post_info);
    }
}
    
GuidanceSignPostType EnhanceSignPost::GetType() const {
    return type_;
}
    
std::string EnhanceSignPost::GetName(int32_t index) const {
    if (index < names_.size()) {
        return names_.at(index).name;
    }

    GUIDE_ASSERT(false);
    return "";
}

int32_t EnhanceSignPost::GetNameCount() const {
    return names_.size();
}

std::vector<std::string> EnhanceSignPost::GetNames() const {
    std::vector<std::string> results;
    for (const auto& name : names_) {
        results.emplace_back(name.name);
    }
    return results;
}

uint32_t EnhanceSignPost::GetConsecutiveCount(uint32_t index) const {
    if (index < names_.size()) {
        return names_[index].consecutive_count;
    }
    return 0;
}
    
void EnhanceSignPost::SetConsecutiveCount(uint32_t index, uint32_t num) {
    if (index < names_.size()) {
        names_[index].consecutive_count = num;
    }
}

DirectEdgeId EnhanceSignPost::GetFromEdgeId() const {
    return from_edge_id_;
}
    
DirectEdgeId EnhanceSignPost::GetToEdgeId() const {
    return to_edge_id_;
}

std::string EnhanceSignPost::GetId() const {
    return from_edge_id_.ToString() + "_" + to_edge_id_.ToString() + "_" + std::to_string(static_cast<int32_t>(type_));
}

PointLL EnhanceSignPost::GetPosition() const {
    return lnglat_;
}

void EnhanceSignPost::BuildSignPost(SignPostInfo *post_info) {
    GUIDE_ASSERT(post_info != nullptr);
    GUIDE_ASSERT(post_info->GetBaseInfo() != nullptr);
    GUIDE_ASSERT(from_edge_id_.edge_id.feature_id == post_info->GetBaseInfo()->in_edge_id);
    GUIDE_ASSERT(from_edge_id_.forward == !(post_info->GetBaseInfo()->in_edge_dir));
    GUIDE_ASSERT(post_info->GetBaseInfo()->is_same_mesh);
    GUIDE_ASSERT(post_info->GetBaseInfo()->is_same_tile);


    GUIDE_ASSERT(to_edge_id_.edge_id.feature_id == post_info->GetBaseInfo()->out_edge_id);
    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.mesh_col == post_info->GetBaseInfo()->out_mesh_col);
    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.mesh_row == post_info->GetBaseInfo()->out_mesh_row);

    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.tile_id == post_info->GetBaseInfo()->out_tile_id);
    GUIDE_ASSERT(to_edge_id_.edge_id.feature_id == post_info->GetBaseInfo()->out_edge_id);
    GUIDE_ASSERT(to_edge_id_.forward == !(post_info->GetBaseInfo()->out_edge_dir));

    type_ = GuidanceSignPostType::kTypeNone;
    if (post_info->GetBaseInfo() != nullptr) {
        if (post_info->GetBaseInfo()->type == 0) {
            type_ = GuidanceSignPostType::kTypeExit;
        } else if (post_info->GetBaseInfo()->type == 1) {
            type_ = GuidanceSignPostType::kTypeDirection;
        } else {
            GUIDE_ASSERT(false);
            type_ = GuidanceSignPostType::kTypeNone;
        }
    }

    names_.clear();
    int32_t sign_cnt = post_info->GetSignInfoNameCount();
    for (int32_t index = 0; index < sign_cnt; ++index) {
        GUIDE_ASSERT(post_info->GetSignInfoName(index) != nullptr);
        if (post_info->GetSignInfoName(index) != nullptr) {
            names_.emplace_back(post_info->GetSignInfoName(index), 1);
            // GUIDE_LOG_INFO("type: {}, sign: {}", static_cast<int>(type_), local_names_.back());
        }
    }

    for (const auto& kv : names_) {
        GUIDE_ASSERT(!kv.name.empty());
    }
}

}  // namespace guide
}  // namespace aurora
/* EOF */
