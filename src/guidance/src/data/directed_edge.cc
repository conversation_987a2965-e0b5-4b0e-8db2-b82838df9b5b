#include "guidance/src/data/directed_edge.h"
#include <algorithm>
#include "guidance/src/common/geo_util.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/common/common_def.h"
#include "guidance/src/common/map_data_util.h"
#include "guidance/src/data/edge_speed.h"
#include "guidance/src/data/facility_type_wrapper.h"

namespace aurora {
namespace guide {

DirectedEdge::DirectedEdge() {
    valid_ = false;
    street_names_ = std::make_shared<StreetNames>();
}

DirectedEdge::DirectedEdge(const RouteEdgeId &edge_id, uint8_t drive_dir, EnhanceDataProvider *provider) {
    edge_id_ = edge_id;
    drive_dir_ = drive_dir;

    street_names_ = std::make_shared<StreetNames>();

    GUIDE_ASSERT(drive_dir_ == drive_dir);
    GUIDE_ASSERT(drive_dir_ == parser::EdgeDirection::kEdgeDirectionForwardPass ||
           drive_dir_ == parser::EdgeDirection::kEdgeDirectionReversePass);

    if (BuildGraphEdge(provider)) {
        EdgeSpeed calc;
        speed_ = calc.GetSpeedMPerSec(this) * 0.6;
        valid_ = true;
    } else {
        valid_ = false;
    }
}

DirectEdgeId DirectedEdge::GetDirectEdgeId() const {
    DirectEdgeId ret;
    ret.edge_id = edge_id_;
    ret.forward = (drive_dir_ == parser::EdgeDirection::kEdgeDirectionForwardPass);
    return ret;
}

bool DirectedEdge::BuildGraphEdge(EnhanceDataProvider *provider) {

    TopolEdge* topo = provider->GetTopoEdge(edge_id_);
    AugmentEdge* augment = provider->GetAugmentEdge(edge_id_);

    if (topo == nullptr || augment == nullptr) {
        GUIDE_ASSERT(topo !=  nullptr);
        GUIDE_ASSERT(augment != nullptr);
        GUIDE_LOG_ERROR("BuildGraphEdge topo:{}, augment:{}", 
                topo == nullptr ? "null" : "not null",
                augment == nullptr ? "null" : "not null");
        return false;
    }

    RouteNode* s_node = provider->GetRouteNode(RouteNodeId(edge_id_.tile_id, topo->GetBaseInfo()->start_node_id));
    RouteNode* e_node = provider->GetRouteNode(RouteNodeId(edge_id_.tile_id, topo->GetBaseInfo()->end_node_id));

    if (s_node == nullptr || e_node == nullptr) {
        GUIDE_ASSERT(s_node != nullptr);
        GUIDE_ASSERT(e_node != nullptr);
        GUIDE_LOG_ERROR("BuildGraphEdge s_node:{}, e_node:{}", 
                s_node == nullptr ? "null" : "not null",
                e_node == nullptr ? "null" : "not null");
        return false;
    }

    const parser::TopolEdgeBase* topo_base = topo->GetBaseInfo();

    function_class_         = topo_base->road_class;
    edge_type_              = topo_base->edge_type;
    direction_              = topo_base->direction;
    need_toll_              = topo_base->need_toll;

    start_node_id_          = topo_base->start_node_id;
    positive_speed_limit_   = topo_base->positive_speed_limit;
    is_overhead_            = topo_base->is_overhead;
    is_inner_edge_          = topo_base->is_inner_edge;
    is_separate_            = topo_base->is_separate;
    end_node_id_            = topo_base->end_node_id;
    negtive_speed_limit_    = topo_base->negtive_speed_limit;
    is_area_edge_           = topo_base->is_area_edge;
    is_city_edge_           = topo_base->is_city_edge;
    is_ramp_                = topo_base->is_ramp;
    edge_form_              = topo_base->edge_form;
    speed_grade_            = topo_base->speed_grade;
    length_                 = topo_base->length;

    forward_lane_count_     = topo_base->forward_lane_count;
    backward_lane_count_    = topo_base->backward_lane_count;
    lane_count_             = topo_base->lane_count;

    road_class_             = topo_base->road_class;
    is_left_                = topo_base->is_left;
    is_limit_in_edge_       = topo_base->is_limit_in_edge;
    is_limit_out_edge_      = topo_base->is_limit_out_edge;
    not_used1_              = topo_base->not_used1;

    is_building_            = topo_base->is_building;
    is_paved_               = topo_base->is_paved;
    is_gate_                = topo_base->is_gate;
    no_crossing_            = topo_base->no_crossing;
    is_private_             = topo_base->is_private;
    not_used2_               = topo_base->not_used2;

    is_one_way = (topo_base->direction == parser::EdgeDirection::kEdgeDirectionForwardPass ||
                  topo_base->direction == parser::EdgeDirection::kEdgeDirectionReversePass);

    if (drive_dir_ == parser::EdgeDirection::kEdgeDirectionReversePass) {
        start_node_id_      = topo_base->end_node_id;
        end_node_id_        = topo_base->start_node_id;

        forward_lane_count_ = topo_base->backward_lane_count;
        backward_lane_count_= topo_base->forward_lane_count;

        positive_speed_limit_ = topo_base->negtive_speed_limit;
        negtive_speed_limit_  = topo_base->positive_speed_limit;
    }

    if (drive_dir_ == parser::EdgeDirection::kEdgeDirectionForwardPass) {
        SetBeginHeading(s_node->GetEdgeAngle(edge_id_.feature_id));
        SetEndHeading(e_node->GetEdgeAngle(edge_id_.feature_id));
    } else {
        SetBeginHeading(e_node->GetEdgeAngle(edge_id_.feature_id));
        SetEndHeading(s_node->GetEdgeAngle(edge_id_.feature_id));
    }

    if (nullptr != augment->GetLocalName()) {
        std::string local_name = augment->GetLocalName();
        street_names_->emplace_back(std::make_unique<StreetName>(local_name, false));
    }

    if (nullptr != augment->GetRoadNo()) {
        std::string road_no = augment->GetRoadNo();
        street_names_->emplace_back(std::make_unique<StreetName>(road_no, true));
    }

    geo_ = augment->GetGeoPoints();
    if (drive_dir_ == parser::EdgeDirection::kEdgeDirectionReversePass) {
        std::reverse(geo_.begin(), geo_.end());
    }

    if ((augment->GetSaPa() != nullptr) && (augment->GetSaPa()->info != nullptr)) {
        sapa_ = std::make_shared<EnhanceSAPA>(GetDirectEdgeId(), augment->GetSaPa());
    }

    slope_type_ = augment->GetBaseInfo()->slope_type;
    if (drive_dir_ == parser::EdgeDirection::kEdgeDirectionReversePass) {
        if (slope_type_ == parser::SlopeType::kSlopeTypeUpHill) {
            slope_type_ = parser::SlopeType::kSlopeTypeDownHill;
        } else if (slope_type_ == parser::SlopeType::kSlopeTypeDownHill) {
            slope_type_ = parser::SlopeType::kSlopeTypeUpHill;
        }
    }

    return true;
}

void DirectedEdge::BuildLaneInfos(EnhanceDataProvider *provider, 
                                  DirectEdgeId *to_edge_ids, 
                                  int32_t to_edge_num, 
                                  const std::set<DirectEdgeId> &edge_index) {
    DirectEdgeId from_edge_id;
    from_edge_id.edge_id = edge_id_;
    from_edge_id.forward = (drive_dir_ == EdgeDirection::kEdgeDirectionForwardPass);

    GUIDE_ASSERT(to_edge_ids != nullptr);
    GUIDE_ASSERT(to_edge_num > 0);
    GUIDE_ASSERT(edge_index.size() > 0);
    if ((provider->GetLaneInfo(from_edge_id) != nullptr) && (to_edge_ids != nullptr) && (to_edge_num > 0)) {
        lane_info_ = std::make_shared<EnhanceLaneInfo>(from_edge_id,  to_edge_ids, to_edge_num, edge_index, provider);
    }
}


void DirectedEdge::BuildFacilities(EnhanceDataProvider *provider, float start_offset, float end_offset) {
    DirectEdgeId edge_id = GetDirectEdgeId();

    int32_t facility_num = 0;
    FacilityInfo *facility_info = provider->GetFacility(edge_id, facility_num);
    if (facility_info != nullptr) {
        for (int32_t index = 0; index < facility_num; ++index) {
            GUIDE_ASSERT((facility_info + index) != nullptr);
            if ((facility_info + index) != nullptr) {
                const parser::FacilityBase *base = (facility_info + index)->GetBaseInfo();
                if (base == nullptr) {
                    GUIDE_ASSERT(base != nullptr);
                    GUIDE_LOG_ERROR("facility GetBaseInfo is nullptr .");
                    continue;
                }

                if (!FacilityTypeWrapper::IsFacility(static_cast<FacilityType>(base->type)) &&
                        !FacilityTypeWrapper::IsCamera(static_cast<FacilityType>(base->type))) {
                    continue;
                }

                if (base->distance_to_start <= start_offset) { // start link
                    continue;
                }

                if (base->distance_to_start > end_offset) { // end link
                    continue;
                }

                facilities_.emplace_back(std::make_shared<EnhanceFacility>(facility_info + index, edge_id, start_offset));
               //  GUIDE_LOG_INFO("+++++++ Found Facility :num={}, idx={}, type={}", facility_num, index, (facility_info + index)->GetBaseInfo()->type);
            } else {
                GUIDE_ASSERT(false);
                GUIDE_LOG_ERROR("facility data error ...");
            }
        }

        // sort by offset
        if (facilities_.size() > 1U) {
            std::sort(facilities_.begin(), facilities_.end(), [](const EnhanceFacilityPtr &lhs, const EnhanceFacilityPtr &rhs) {
                return (lhs->GetOffset() < rhs->GetOffset());
            });

            // for (int i = 0; i < facilities_.size(); ++i) {
            //     GUIDE_LOG_INFO("Facility:num={},index={}, heading={}, offset={}", facilities_.size(), i, facilities_[i]->GetHeading(), facilities_[i]->GetOffset());
            // }
        }
    }
}

void DirectedEdge::SetBeginHeading(int16_t heading) {
    GUIDE_ASSERT(heading >=0 && heading <= 360);
    while (heading >= 360) {
        heading -= 360;
    }
    begin_heading_ = heading;
}

void DirectedEdge::SetEndHeading(int16_t heading) {
    GUIDE_ASSERT(heading >=0 && heading <= 360);
    
    heading += 180;

    while (heading >= 360) {
        heading -=360;
    }
    end_heading_ = heading;
}

double DirectedEdge::GetLengthMeter() const {
    return length_;
}

double DirectedEdge::GetLength(DistaneUnit unit) const {
    if (unit == DistaneUnit::kMile) {
        return length_ * kKmPerMile;
    }
    return length_;
}

bool DirectedEdge::IsTurnChannel() const {
    return MapDataUtil::IsTurnChannel(edge_form_);
}

bool DirectedEdge::IsHighway() const {
    return MapDataUtil::IsHighway(road_class_, edge_form_, is_ramp_);
}

bool DirectedEdge::HasSign() const {
    // TODO: 增强预处理需要补充sign处理
    // GUIDE_ASSERT(false);
    return false;
}

float DirectedEdge::GetDefaultSpeed() const {
    return speed_;
}

float DirectedEdge::GetBasicTime() const {
    return (GetLengthMeter() / GetDefaultSpeed());
}

bool DirectedEdge::IsStraightest(uint32_t prev2cur_turn_degree, uint32_t straight_xedge_turn_degree) const {
    static const int32_t kIsStraightBuffer = 15;

    if (geo::IsWideForward(prev2cur_turn_degree)) {
        uint32_t path_straight_delta = (prev2cur_turn_degree > 180) ? (360 - prev2cur_turn_degree):prev2cur_turn_degree;
        uint32_t xedge_straight_delta = (straight_xedge_turn_degree > 180) ? (360 - straight_xedge_turn_degree):straight_xedge_turn_degree;

        int path_straight_xedge_straight_delta = geo::CalcTurnDegree180(path_straight_delta, xedge_straight_delta);

        if (path_straight_xedge_straight_delta <= kIsStraightBuffer) {
            return true;
        }
        return (path_straight_delta <= xedge_straight_delta);
    }
    return false;
}

uint32_t DirectedEdge::GetFacilityNum() const {
    return facilities_.size();
}

EnhanceFacilityPtr DirectedEdge::GetFacility(int32_t index) const {
    if (index < facilities_.size()) {
        return facilities_.at(index);
    }
    GUIDE_ASSERT(false);
    return nullptr;
}

const std::vector<EnhanceFacilityPtr> DirectedEdge::GetFacilities() const {
    return facilities_;
}

const EnhanceSAPAPtr DirectedEdge::GetSAPA() const {
    return sapa_;
}

bool DirectedEdge::CutHead(float offset_m) {
    length_ = geo::CalcPolylineDistance(geo_);

    GUIDE_ASSERT(offset_m <= length_);
    if (offset_m >= length_) {
        std::vector<PointLL> res = {geo_.back(), geo_.back()};
        geo_ = res;
        length_ = geo::CalcPolylineDistance(geo_);
    } else {
        float acc_dis = 0;
        float edge_dis = 0;
        PointLL interpolate_pt;
        int32_t interpolate_idx = -1;
        for (int32_t idx = 0; (idx + 1) < geo_.size(); ++idx) {
            edge_dis = geo_[idx].Distance(geo_[idx + 1]);

            if (edge_dis <= 0) {
                GUIDE_ASSERT(false);
                continue;
            }

            if (offset_m >= acc_dis && offset_m <= (acc_dis + edge_dis)) {
                interpolate_idx = idx;

                float ratio = (offset_m - acc_dis) / edge_dis;
                interpolate_pt = geo_[idx] + (geo_[idx + 1] - geo_[idx]) * ratio;
                break;
            }
            acc_dis += edge_dis;
        }

        if (interpolate_idx < 0) {
            std::vector<PointLL> res = {geo_.back(), geo_.back()};
            geo_ = res;
            length_ = geo::CalcPolylineDistance(geo_);
        } else {
            geo_.erase(geo_.begin(), geo_.begin() + interpolate_idx + 1);
            geo_.insert(geo_.begin(), interpolate_pt);
            length_ = geo::CalcPolylineDistance(geo_);
        }
    }
    return true;
}

bool DirectedEdge::CutTail(float offset_m) {
   length_ = geo::CalcPolylineDistance(geo_);

    GUIDE_ASSERT(offset_m <= length_);
    if (offset_m >= length_) {
        std::vector<PointLL> res = {geo_.front(), geo_.front()};
        geo_ = res;
        length_ = geo::CalcPolylineDistance(geo_);
    } else {
        float acc_dis = 0;
        float edge_dis = 0;
        PointLL interpolate_pt;
        int32_t interpolate_idx = -1;
        for (int32_t idx = geo_.size() - 1; idx > 0; --idx) {
            edge_dis = geo_[idx].Distance(geo_[idx - 1]);

            if (edge_dis <= 0) {
                GUIDE_ASSERT(false);
                continue;
            }

            if (offset_m >= acc_dis && offset_m <= (acc_dis + edge_dis)) {
                interpolate_idx = idx;

                float ratio = (offset_m - acc_dis) / edge_dis;
                interpolate_pt = geo_[idx] + (geo_[idx-1] - geo_[idx]) * ratio;
                break;
            }
            acc_dis += edge_dis;
        }

        if (interpolate_idx < 0) {
            std::vector<PointLL> res = {geo_.front(), geo_.front()};
            geo_ = res;
            length_ = geo::CalcPolylineDistance(geo_);
        } else {
            geo_.erase(geo_.begin() + interpolate_idx, geo_.end());
            geo_.insert(geo_.end(), interpolate_pt);
            length_ = geo::CalcPolylineDistance(geo_);
        }
    }
    return true;
}

void DirectedEdge::RefreshLength() {
    length_ = geo::CalcPolylineDistance(geo_);
}

}  // namespace guide
}  // namespace aurora
/* EOF */
