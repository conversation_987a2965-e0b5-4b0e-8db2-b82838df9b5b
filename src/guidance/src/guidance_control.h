#ifndef MAP_SRC_GUIDE_SRC_GUIDE_CONTROL_H
#define MAP_SRC_GUIDE_SRC_GUIDE_CONTROL_H

#include <vector>

#include "guidance_module.h"
#include "path_module.h"
#include "location_module.h"
#include "guidance/src/maker/direction_builder.h"
#include "guidance/src/guidance_event.h"
#include "guidance/src/guide/guide_runtime.h"
#include "guidance/src/debug/debug_manager.h"

namespace aurora {
namespace guide {

class GuidanceControl : public IGuidance {
public:
    GuidanceControl();
    ~GuidanceControl();

    int32_t  Prepare(const std::string &config);
    int32_t  Init(const InterfaceFinder &finder);
    int32_t  Start();
    int32_t  Stop();
    int32_t  UnInit();

    ModuleId GetModuleId() const override;

    int32_t SetParam(const Param &param) override;
    Param   GetParam(const ParamType &type) const override;

    // 更新匹配结果
    int32_t UpdateMapMatchingPos(const loc::MatchResult &result) override;

    // 更新算路结果
    int32_t SetNavPath(path::PathQueryPtr query, path::PathResultPtr result, uint64_t main_path_id) override;

    int32_t SwitchMainPathId(uint64_t path_id) override;
    
    int32_t RequestPathManeuverInfos(uint64_t path_id) override;
    int32_t StartNavigation(NavigationMode type) override;
    int32_t StopNavigation(NavigationStopCode code) override;
    int32_t PauseNavigation() override;
    int32_t ResumeNavigation() override;
    int32_t PlayNavigationManual() override;

    int32_t AddListener(IGuidanceListenerPtr obs) override;
    int32_t RemoveListener(IGuidanceListenerPtr obs) override;

protected:
    void InitOptions();

private:
    // loc::MapMatchingListenerPtr mm_listener_;
    // path::PathListenerPtr path_listener_;
    // std::shared_ptr<loc::ILocation> loc_;
    // std::shared_ptr<path::PathInterface> path_;
    bool             started_;
    GuidanceOptions  options_;
    DebugManager     debug_manager_;
    GuidanceEvent    guidance_event_;
    DirectionBuilder direction_builder_;
    GuideRuntime     guidance_runtime_;
};  // class GuideModuleImpl

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_CONTROL_H
/* EOF */
