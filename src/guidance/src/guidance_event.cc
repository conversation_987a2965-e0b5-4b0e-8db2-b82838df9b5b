#include "guidance/src/guidance_event.h"
#include "errorcode.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/common/time_tracker.h"


namespace aurora {
namespace guide {

GuidanceEvent::GuidanceEvent() 
: thread_("guidane_event") {

}

GuidanceEvent::~GuidanceEvent() {
    Stop();
}

void GuidanceEvent::Start() {
    thread_.Start();
}
    
void GuidanceEvent::Stop() {
    thread_.Stop();
}

void GuidanceEvent::NotifyPathManeuverDetail(int64_t path_id, const std::list<Maneuver> &maneuver_list) {
    std::vector<NavigationManeuverInfoPtr>  result;
    result.reserve(maneuver_list.size());

    for (const auto &maneuver : maneuver_list) {
        NavigationManeuverInfoPtr detail = std::make_shared<NavigationManeuverInfo>();
        detail->type = static_cast<int32_t>(maneuver.GetType());
        detail->action = ManeuverTypeToString(maneuver.GetType());
        if (!(maneuver.GetStreetNames()->empty())) {
            std::shared_ptr<StreetNames> ss_names = maneuver.GetStreetNames();

            if ((ss_names != nullptr) && !ss_names->empty()) {
                detail->begin_name = ss_names->front()->Value();
            }
        }

        if (detail->begin_name.empty()) {
            detail->begin_name = "无名路";
        }
        detail->length = maneuver.GetLengthMeter();
        detail->geos = maneuver.GetPoints();

        if (!result.empty() && ManeuverDetailCanMerge(result.back(), detail)) {
            result.back()->length += detail->length;
            result.back()->geos.insert(result.back()->geos.end(), detail->geos.begin(), detail->geos.end());
        } else {
            result.push_back(detail);
        }
    }

    for (uint32_t index = 0; index < result.size(); ++index) {
        if ((index + 1) != result.size()) {
            GUIDE_LOG_INFO("ManeuverList[{}]: {} 进入{}, 行驶 {} m", index, result[index]->action, result[index]->begin_name, result[index]->length);
        } else {
            GUIDE_LOG_INFO("ManeuverList[{}]: {}", index, result[index]->action);
        }
    }

    NotifyPathManeuverDetail(path_id, result);
}

bool GuidanceEvent::ManeuverDetailCanMerge(const NavigationManeuverInfoPtr lhs, const NavigationManeuverInfoPtr rhs) {
    if (lhs->type == rhs->type && lhs->begin_name == rhs->begin_name) {
        return true;
    }
    return false;
}

void GuidanceEvent::NotifyPathManeuverDetail(int64_t path_id, const std::vector<NavigationManeuverInfoPtr> &details) {
    thread_.Post([this, path_id, details]() {
        GUIDE_LOG_INFO("----- GuidanceEvent: NotifyPathManeuverDetail is trigger .[{}]", nav_obs_.size());

        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("OnPathManeuverDetail", 30);
                obj->OnPathManeuverInfos(path_id, details);
            }
        } //
    });
}

int32_t GuidanceEvent::AddListener(IGuidanceListenerPtr obs) {
     if (obs == nullptr) {
        return ErrorCode::kErrorCodeInvalidParam;
    }

    auto type = obs->GetType();
    switch (type)
    {
    case GuidanceListenerType::kGuidanceListenerTypeNavigation:
    {
        auto ptr = std::dynamic_pointer_cast<INavigationListener>(obs);
        if (ptr == nullptr) {
            return ErrorCode::kErrorCodeInvalidParam;
        }
        nav_obs_.push_back(ptr);
        GUIDE_LOG_INFO("navigation listener num: {}", nav_obs_.size());
    }
        break;

    case GuidanceListenerType::kGuidanceListenerTypeCruise:
    {
        auto ptr = std::dynamic_pointer_cast<ICruiseListener>(obs);
        if (ptr == nullptr) {
            return ErrorCode::kErrorCodeInvalidParam;
        }
        cruise_obs_.push_back(ptr);
        GUIDE_LOG_INFO("cruise listener num: {}", nav_obs_.size());

    }
        break;

    case GuidanceListenerType::kGuidanceListenerTypeSound:
    {
        auto ptr = std::dynamic_pointer_cast<ISoundListener>(obs);
        if (ptr == nullptr) {
            return ErrorCode::kErrorCodeInvalidParam;
        }
        sound_obs_.push_back(ptr);
        GUIDE_LOG_INFO("sound listener num: {}", nav_obs_.size());
    }
        break;
    
    default:
        return ErrorCode::kErrorCodeInvalidParam;
    }

    return ErrorCode::kErrorCodeOk;
}
    
int32_t GuidanceEvent::RemoveListener(IGuidanceListenerPtr obs) {
    if (obs == nullptr) {
        return ErrorCode::kErrorCodeInvalidParam;
    }

    auto type = obs->GetType();
    switch (type)
    {
    case GuidanceListenerType::kGuidanceListenerTypeNavigation:
    {
        auto ptr = std::dynamic_pointer_cast<INavigationListener>(obs);
        if (ptr == nullptr) {
            return ErrorCode::kErrorCodeInvalidParam;
        }
        std::remove_if(nav_obs_.begin(), nav_obs_.end(), [ptr](const INavigationListenerPtr &p) {
            return p == ptr;
        });
        LOG_INFO("navigation listener num: {}", nav_obs_.size());
    }
        break;

    case GuidanceListenerType::kGuidanceListenerTypeCruise:
    {
        auto ptr = std::dynamic_pointer_cast<ICruiseListener>(obs);
        if (ptr == nullptr) {
            return ErrorCode::kErrorCodeInvalidParam;
        }
        std::remove_if(cruise_obs_.begin(), cruise_obs_.end(), [ptr](const ICruiseListenerPtr &p) {
            return p == ptr;
        });           
            LOG_INFO("cruise listener num: {}", cruise_obs_.size());

    }
        break;

    case GuidanceListenerType::kGuidanceListenerTypeSound:
    {
        auto ptr = std::dynamic_pointer_cast<ISoundListener>(obs);
        if (ptr == nullptr) {
            return ErrorCode::kErrorCodeInvalidParam;
        }
        std::remove_if(sound_obs_.begin(), sound_obs_.end(), [ptr](const ISoundListenerPtr &p) {
            return p == ptr;
        });

        LOG_INFO("sound listener num: {}", sound_obs_.size());
    }
        break;
    
    default:
        return ErrorCode::kErrorCodeInvalidParam;
    }
    return ErrorCode::kErrorCodeOk;
}

void GuidanceEvent::PlayVoice(const std::string &text) {
    thread_.Post([this, text]() {
        GuidanceSoundInfoPtr sound_info = std::make_shared<GuidanceSoundInfo>();
        sound_info->text = text;

        GUIDE_LOG_INFO("----- GuidanceEvent: PlayVoice is trigger .[{}] [{}]", text, nav_obs_.size());

        for (const auto &sound : sound_obs_) {
            if (sound != nullptr) {
                TimeTracker  tracker("PlayVoice", 30);
                sound->OnPlayTTS(sound_info);
                // GUIDE_LOG_INFO("------------------------------------------------");
                // GUIDE_LOG_INFO("---- {} -----", text);
                // GUIDE_LOG_INFO("------------------------------------------------");

            }
        } //
    });
}

void GuidanceEvent::PlayRing(const TTSScenePlay scene_id) {
    thread_.Post([this, scene_id]() {
        GUIDE_LOG_INFO("----- GuidanceEvent: PlayRing  is trigger .[{}]", nav_obs_.size());
        for (const auto &sound : sound_obs_) {
            if (sound != nullptr) {
                TimeTracker  tracker("PlayRing", 10);
                sound->OnPlayRing(scene_id);
            }
        } //
    });
}

void GuidanceEvent::StartNavigation(NavigationMode mode, uint64_t path_id) {
    thread_.Post([this, mode, path_id]() {
        GUIDE_LOG_INFO("----- GuidanceEvent: StartNavigation is trigger .[{}]", nav_obs_.size());
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("StartNavigation", 10);
                obj->OnNavigationStart(mode, path_id);
            }
        } //
    });
}

void GuidanceEvent::PauseNavigation(NavigationMode mode) {
    thread_.Post([this, mode]() {
        GUIDE_LOG_INFO("----- GuidanceEvent: PauseNavigation is trigger .[{}]", nav_obs_.size());
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("PauseNavigation", 10);
                obj->OnNavigationPause(mode);
            }
        } //
    });
}

void GuidanceEvent::ResumeNavigation(NavigationMode mode) {
    thread_.Post([this, mode]() {
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("ResumeNavigation", 10);
                obj->OnNavigationResume(mode);
            }
        } //
    });
}

void GuidanceEvent::StopNavigation(NavigationMode mode, NavigationStopCode code) {
    thread_.Post([this, mode, code]() {
        GUIDE_LOG_INFO("----- GuidanceEvent: StopNavigation is trigger [{}].", nav_obs_.size());
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("StopNavigation", 10);
                obj->OnNavigationStop(mode, code);
            }
        } //
    });
}

void GuidanceEvent::ArriveDestiontion(NavigationMode mode) {
    thread_.Post([this, mode]() {
        GUIDE_LOG_INFO("----- GuidanceEvent: ArriveDestiontion is trigger .[{}]", nav_obs_.size());
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("ArriveDestiontion", 10);
                obj->OnNavigationArrive(mode);
            }
        } //
    });
}

void GuidanceEvent::SwitchPathStatus(uint64_t path_id, bool success) {
    GUIDE_LOG_INFO("SwitchPathStatus: path_id={}, status:{}", path_id, success ? "success" : "failure");
    thread_.Post([this, path_id, success]() {
        GUIDE_LOG_INFO("----- GuidanceEvent: SwitchPathStatus is trigger .[{}]", nav_obs_.size());
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("ResumeNavigation", 10);
                obj->OnSwitchPathStatus(path_id, success);
            }
        } //
    });
}

void GuidanceEvent::ShowJunctionView(NavigationJunctionViewInfoPtr info) {
    thread_.Post([this, info]() {
        // GUIDE_LOG_INFO("----- GuidanceEvent: ShowJunctionView is trigger .[{}]", nav_obs_.size());
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("ShowJunctionView", 10);
                obj->OnShowJunctionView(info);
            }
        } //
    });
}

void GuidanceEvent::ShowLaneInfo (NavigationLaneInfoPtr info) {
    thread_.Post([this, info]() {
        // GUIDE_LOG_INFO("----- GuidanceEvent: ShowLaneInfo is trigger .[{}]", nav_obs_.size());
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("ShowLaneInfo", 10);
                obj->OnShowLaneInfo(info);
            }
        } //
    });
}

void GuidanceEvent::UpdateTollStation(NavigationTollStationInfoPtr info) {
    if (info != nullptr) {
        GUIDE_LOG_INFO("----- GuidanceEvent: UpdateTollStation .");
    }

    thread_.Post([this, info]() {
        // GUIDE_LOG_INFO("----- GuidanceEvent: ShowLaneInfo is trigger .[{}]", nav_obs_.size());
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("UpdateTollStation", 10);
                obj->OnUpdateTollStationInfo(info);
            }
        } //
    });
}

void GuidanceEvent::UpdateNavigationInfo(NavigationInfoPtr info) {
    GUIDE_LOG_INFO("NavigationInfo: remain_distance={}, remain_time={}, road_name={}", 
                        info->path_remain.dis, 
                        info->path_remain.sec, 
                        info->curr_road_name);

    thread_.Post([this, info]() {
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("UpdateNavigationInfo", 10);
                obj->OnUpdateNavigationInfo(info);
            }
        } //
    });
}

void GuidanceEvent::UpdateFacilityInfos(const std::vector<NavigationFacilityInfoPtr> &infos) {
    if (infos.size() > 0) {
        GUIDE_LOG_INFO(" +++++ found facility +++");
    }
    thread_.Post([this, infos]() {
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("UpdateFacilityInfos", 10);
                obj->OnUpdateFacilityInfos(infos);
            }
        } //
    });
}

void GuidanceEvent::UpdateSAPAInfos(const std::vector<NavigationSAPAInfoPtr> &infos) {
    if (infos.size() > 0) {
        GUIDE_LOG_INFO(" +++++ UpdateSAPAInfos +++");
    }

    thread_.Post([this, infos]() {
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("OnUpdateSAPAInfo", 10);
                obj->OnUpdateSAPAInfos(infos);
            }
        } //
    });
}

void GuidanceEvent::UpdateCameraInfos(const std::vector<NavigationCameraInfoPtr> &infos) {
    if (infos.size() > 0) {
        GUIDE_LOG_INFO("+++ UpdateCameraInfos +++");
    }

    thread_.Post([this, infos]() {
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("UpdateCameraInfos", 10);
                obj->OnUpdateCamerasInfos(infos);
            }
        } //
    });
}

void GuidanceEvent::UpdateSignPostInfos(const std::vector<GuidanceSignPostInfoPtr> &infos) {
    if (infos.size() > 0) {
        GUIDE_LOG_INFO("+++ UpdateSignPostInfos +++");
    }

    thread_.Post([this, infos]() {
        for (const auto &obj : nav_obs_) {
            if (obj != nullptr) {
                TimeTracker  tracker("UpdateSignPostInfos", 10);
                obj->OnUpdateSignPostInfos(infos);
            }
        } //
    });
}


}  // namespace guide
}  // namespace aurora
/* EOF */
