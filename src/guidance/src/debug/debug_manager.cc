#include "guidance/src/debug/debug_manager.h"
#include "guidance/src/debug/data/enhance_path_writter.h"
#include "guidance/src/debug/data/guide_voice_writter.h"
#include "guidance/src/debug/data/junction_view_writter.h"
#include "guidance/src/debug/data/lane_info_writter.h"
#include "guidance/src/debug/data/matching_writter.h"
#include "guidance/src/debug/data/maneuvers_writter.h"
#include "guidance/src/debug/data/nav_board_writter.h"
#include "guidance/src/debug/data/sapa_writter.h"
#include "guidance/src/debug/data/toll_station_writter.h"
#include "guidance/src/debug/data/facility_writter.h"
#include "guidance/src/debug/data/camera_writter.h"
#include "guidance/src/debug/data/sign_post_writter.h"


#include "guidance/src/common/file_util.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

static std::string wsl_dir = "/mnt/d/tmp/route/debug/";
static std::string ubuntu_dir = "/mnt/hgfs/tmp/route/debug/"; 
// static std::string debug_dir;

DebugManager::DebugManager(const std::string & debug_config) {
    debug_ = false;

    if (debug_config.empty()) {
        if (FileUtil::IsDirectoryExist("/home/<USER>")) {
            if (FileUtil::IsDirectoryExist(wsl_dir)) {
                debug_ = true;
                data_dir_ = wsl_dir;
            } else if (FileUtil::IsDirectoryExist(ubuntu_dir)) {
                debug_ = true;
                data_dir_ = ubuntu_dir;
            }
        }
    }

    GUIDE_LOG_INFO("DebugManager ==> IsDebug: {} ", debug_);
    GUIDE_LOG_INFO("DebugManager ==> data_dir_: {} ", data_dir_);

    if (debug_) {
        writter_map_[DebugDataId::KDataIdEnhancePath] = std::make_shared<EnhancePathWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdManeuver] = std::make_shared<ManeuversWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdMatching] = std::make_shared<MatchingWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdVoiceData] = std::make_shared<GuideVoiceWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdNavBoard] = std::make_shared<NavBoardWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdLaneInfo] = std::make_shared<LaneInfoWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdJunctionView] = std::make_shared<JunctionViewWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdTollStation] = std::make_shared<TollStationWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdSAPA] = std::make_shared<SAPAWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdCamera] = std::make_shared<CameraWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdFacility] = std::make_shared<FacilityWritter>(data_dir_);
        writter_map_[DebugDataId::kDataIdSignPost] = std::make_shared<SignPostWritter>(data_dir_);
    }

}
    
DebugManager::~DebugManager() {
}

    
bool DebugManager::IsDebugOn() const {
    return debug_;
}
    
bool DebugManager::Reset() {
    if (IsDebugOn()) {
        for (auto &kv : writter_map_) {
            kv.second->Reset();
        }
    }
    return true;
}
    
bool DebugManager::ForceFlush() {
    if (IsDebugOn()) {
        for (auto &kv : writter_map_) {
            kv.second->Flush();
        }
    }
    return true;
}

void DebugManager::WritePathQuery(uint64_t path_id, EnhancePathQueryPtr enhance_query) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::KDataIdEnhancePath) > 0) {
        std::shared_ptr<EnhancePathWritter> ptr = std::dynamic_pointer_cast<EnhancePathWritter>(writter_map_.at(DebugDataId::KDataIdEnhancePath));
        ptr->WritePathQuery(path_id, enhance_query);
    }
}

void DebugManager::WriteEnhancePath(EnhanceDataProvider *provider, EnhancePathResultPtr enhance_path) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::KDataIdEnhancePath) > 0) {
        std::shared_ptr<EnhancePathWritter> ptr = std::dynamic_pointer_cast<EnhancePathWritter>(writter_map_.at(DebugDataId::KDataIdEnhancePath));
        ptr->WritePathResult(provider, enhance_path);
    }
}

void DebugManager::WriteManeuvers(EnhancePathResultPtr enhance_path, const std::list<Maneuver> &maneuvers) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::kDataIdManeuver) > 0) {
        std::shared_ptr<ManeuversWritter> ptr = std::dynamic_pointer_cast<ManeuversWritter>(writter_map_.at(DebugDataId::kDataIdManeuver));
        ptr->Write(enhance_path, maneuvers);
    }
}

void DebugManager::WriteMatching(uint64_t path_id, const VehiclePosition &match_result) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::kDataIdMatching) > 0) {
        std::shared_ptr<MatchingWritter> ptr = std::dynamic_pointer_cast<MatchingWritter>(writter_map_.at(DebugDataId::kDataIdMatching));
        ptr->Write(path_id, match_result);
    }
}

void DebugManager::WriteGuideData(uint64_t path_id, const GuideData &guide_data) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::kDataIdVoiceData) > 0) {
        std::shared_ptr<GuideVoiceWritter> ptr = std::dynamic_pointer_cast<GuideVoiceWritter>(writter_map_.at(DebugDataId::kDataIdVoiceData));
        ptr->Write(path_id, guide_data);
    }
}

void DebugManager::WriteNavBoard(uint64_t path_id, NavigationInfoPtr ptr) {

}

void DebugManager::WriteLaneInfo(uint64_t path_id, const VehiclePosition &ccp, const LaneInfoState *state) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::kDataIdLaneInfo) > 0) {
        std::shared_ptr<LaneInfoWritter> ptr = std::dynamic_pointer_cast<LaneInfoWritter>(writter_map_.at(DebugDataId::kDataIdLaneInfo));
        ptr->Write(path_id, ccp, state);
    }
}

void DebugManager::WriteJunctionView(uint64_t path_id, const VehiclePosition& ccp, const JunctionViewState *state) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::kDataIdJunctionView) > 0) {
        std::shared_ptr<JunctionViewWritter> ptr = std::dynamic_pointer_cast<JunctionViewWritter>(writter_map_.at(DebugDataId::kDataIdJunctionView));
        ptr->Write(path_id, ccp, state);
    }
}

void DebugManager::WriteTollStation(uint64_t path_id, const VehiclePosition& ccp, const TollStationState *state) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::kDataIdTollStation) > 0) {
        std::shared_ptr<TollStationWritter> ptr = std::dynamic_pointer_cast<TollStationWritter>(writter_map_.at(DebugDataId::kDataIdTollStation));
        ptr->Write(path_id, ccp, state);
    }
}

void DebugManager::WriteSAPAInfo(uint64_t path_id, const VehiclePosition &ccp, const SAPAState *state) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::kDataIdSAPA) > 0) {
        std::shared_ptr<SAPAWritter> ptr = std::dynamic_pointer_cast<SAPAWritter>(writter_map_.at(DebugDataId::kDataIdSAPA));
        ptr->Write(path_id, ccp, state);
    }
}

void DebugManager::WriteSignPost(uint64_t path_id, const VehiclePosition &ccp, const SignPostState *state) {
    if (IsDebugOn() && writter_map_.count(DebugDataId::kDataIdSignPost) > 0) {
        std::shared_ptr<SignPostWritter> ptr = std::dynamic_pointer_cast<SignPostWritter>(writter_map_.at(DebugDataId::kDataIdSignPost));
        ptr->Write(path_id, ccp, state);
    }
}


}  // namespace guide
}  // namespace aurora
/* EOF */
