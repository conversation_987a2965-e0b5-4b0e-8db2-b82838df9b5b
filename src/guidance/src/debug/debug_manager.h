#ifndef MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DEBUG_MANAGER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DEBUG_MANAGER_H

#include <string>
#include <vector>
#include <list>
#include <map>
#include <memory>

#include "guidance_def.h"
#include "guidance/src/debug/data/geojson_writter.h"
#include "guidance/src/data/enhance_data_provider.h"
#include "guidance/src/data/enhance_path_result.h"
#include "guidance/src/common/maneuver.h"
#include "guidance/src/common/guide_data.h"

namespace aurora {
namespace guide {

class DebugManager {
public:
    enum class DebugDataId {
        KDataIdEnhancePath = 0,
        kDataIdManeuver,
        kDataIdMatching,
        kDataIdVoiceData,
        kDataIdNavBoard,
        kDataIdLaneInfo,
        kDataIdJunctionView,
        kDataIdTollStation,
        kDataIdSAPA,
        kDataIdFacility,
        kDataIdCamera,
        kDataIdSignPost
    };

    DebugManager(const std::string & debug_config = "");
    ~DebugManager();

    bool IsDebugOn() const;
    bool Reset();
    bool ForceFlush();

    void WritePathQuery(uint64_t path_id , EnhancePathQueryPtr enhance_query);
    void WriteEnhancePath(EnhanceDataProvider *provider, EnhancePathResultPtr enhance_path);
    void WriteManeuvers(EnhancePathResultPtr enhance_path, const std::list<Maneuver> &maneuvers);

    void WriteMatching(uint64_t path_id, const VehiclePosition &match_result);
    void WriteGuideData(uint64_t path_id, const GuideData &guide_data);

    void WriteNavBoard(uint64_t path_id, NavigationInfoPtr ptr);
    void WriteLaneInfo(uint64_t path_id, const VehiclePosition &ccp, const LaneInfoState *state);

    void WriteJunctionView(uint64_t path_id, const VehiclePosition &ccp, const JunctionViewState *state);
    void WriteTollStation(uint64_t path_id, const VehiclePosition &ccp, const TollStationState *state);

    void WriteSAPAInfo(uint64_t path_id, const VehiclePosition &ccp, const SAPAState *state);
    void WriteSignPost(uint64_t path_id, const VehiclePosition &ccp, const SignPostState *state);

    void WriteFacility(uint64_t path_id, NavigationFacilityInfoPtr ptr);
    void WriteCamera(uint64_t path_id, NavigationCameraInfoPtr ptr);

protected:
    bool debug_;
    std::string data_dir_;
    std::map<DebugDataId, GeojsonWritterPtr> writter_map_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DEBUG_MANAGER_H
/* EOF */
