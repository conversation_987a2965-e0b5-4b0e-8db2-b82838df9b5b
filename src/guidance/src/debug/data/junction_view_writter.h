#ifndef MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_JUNCTION_VIEW_WRITTER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_JUNCTION_VIEW_WRITTER_H

#include <list>
#include "guidance/src/debug/data/geojson_writter.h"
#include "guidance_def.h"
#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/common/guide_data.h"

namespace aurora {
namespace guide {

class JunctionViewWritter : public GeojsonWritter {
public:
    struct JunctionViewData {
        std::string         id;
        int32_t             type; 
        float               distance;
        PointLL             lnglat;
    };

    JunctionViewWritter(const std::string& dir);
    void Write(uint64_t path_id, const VehiclePosition &ccp, const JunctionViewState *data);
    void Flush();
    void Reset();

protected:
    void WriteJunctionViewImage(EnhanceJunctionViewPtr data);
    void DoWrite();

protected:
    int32_t index_;
    uint64_t path_id_;
    std::list<JunctionViewData> datas_;
};

}  // namespace guide
}  // namespace aurora

#endif  // MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_JUNCTION_VIEW_WRITTER_H
/* EOF */
