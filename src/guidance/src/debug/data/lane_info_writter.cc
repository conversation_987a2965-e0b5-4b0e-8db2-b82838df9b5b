// <PERSON><PERSON><PERSON> asserts by default but we dont want to crash running server
// its more useful to throw and catch for our use case
#define RAPIDJSON_ASSERT_THROWS
#undef RAPIDJSON_ASSERT
#define RAPIDJSON_ASSERT(x)                                                                          \
  if (!(x))                                                                                          \
  throw std::logic_error(RAPIDJSON_STRINGIFY(x))
// Because we now throw exceptions, we need to turn off RAPIDJSON_NOEXCEPT
#define RAPIDJSON_HAS_CXX11_NOEXCEPT 0
// Enable std::string overloads
#define RAPIDJSON_HAS_STDSTRING 1

#include "guidance/src/debug/data/lane_info_writter.h"

#include <fstream>

#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/prettywriter.h"

#include "guidance/src/common/file_util.h"


namespace aurora {
namespace guide {

LaneInfoWritter::LaneInfoWritter(const std::string& dir) 
: G<PERSON><PERSON><PERSON>Writter(dir) {
    
}

void LaneInfoWritter::Write(uint64_t path_id, const VehiclePosition &ccp, const LaneInfoState *state) {
    if (path_id == PathID::kInvalidPathId) {
        GUIDE_ASSERT(false);
        return;
    }

    if (path_id_ != PathID::kInvalidPathId && path_id_ != path_id) {
        GUIDE_ASSERT(false);
        return;
    }

    if (state == nullptr) {
        GUIDE_ASSERT(false);
        return;
    }

    path_id_ = path_id;

    LaneInfoData lane_data;
    lane_data.id = state->ptr->GetId();
    lane_data.lnglat = ccp.GetProj();
    lane_data.distance = state->remain_dist;
    lane_data.front = state->ptr->GetFrontLaneActions();
    lane_data.back = state->ptr->GetBackLaneActions();

    datas_.push_back(lane_data);

    if (datas_.size() >= 600) {
        DoWrite();
        datas_.clear();
    }
}
    
void LaneInfoWritter::Flush() {
    if (!datas_.empty()) {
        DoWrite();
        datas_.clear();
    }
}
    
void LaneInfoWritter::Reset() {
    path_id_ = PathID::kInvalidPathId;
    index_ = 0;
    datas_.clear();
}

void LaneInfoWritter::DoWrite() {
    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();
    doc.AddMember("type", "FeatureCollection", allocator);

    rapidjson::Value features(rapidjson::kArrayType);

    // process directed edge
    for (auto &result : datas_) {        
        // add edge info
        rapidjson::Value feature(rapidjson::kObjectType);
        feature.AddMember("type", "Feature", allocator);
            
        rapidjson::Value geometry(rapidjson::kObjectType);
        geometry.AddMember("type", "Point", allocator);

        rapidjson::Value coordinates(rapidjson::kArrayType);
        coordinates.PushBack(static_cast<double>(result.lnglat.first), allocator);
        coordinates.PushBack(static_cast<double>(result.lnglat.second), allocator);
        geometry.AddMember("coordinates", coordinates, allocator);
        feature.AddMember("geometry", geometry, allocator);

        rapidjson::Value properties(rapidjson::kObjectType);

        properties.AddMember("id", result.id, allocator);
        properties.AddMember("distance", result.distance, allocator);
        properties.AddMember("front_lane", "not realize", allocator);
        properties.AddMember("back_lane", "not realize", allocator);

        feature.AddMember("properties", properties, allocator);
        features.PushBack(feature, allocator);
    }

    doc.AddMember("features", features, allocator);

    std::string dir = dir_ + "/" + std::to_string(path_id_);
    if (!FileUtil::IsDirectoryExist(dir)) {
        FileUtil::CreateDirectory(dir);
    }

    std::string filename = dir + "/lane_info_" + std::to_string(index_) + ".geojson";
    std::ofstream file(filename.c_str());
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);

    file << buffer.GetString();
    file.close();

    ++index_;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
