#ifndef MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_SAPA_WRITTER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_SAPA_WRITTER_H

#include "guidance/src/debug/data/geojson_writter.h"
#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/common/guide_data.h"

namespace aurora {
namespace guide {

class SAPAWritter : public GeojsonWritter {
public:
    SAPAWritter(const std::string& dir);

    void Write(uint64_t path_id, const VehiclePosition &ccp, const SAPAState *state);
    void Flush();
    void Reset();

};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_SAPA_WRITTER_H
/* EOF */
