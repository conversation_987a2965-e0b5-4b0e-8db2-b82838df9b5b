#ifndef MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_CAMERA_WRITTER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_CAMERA_WRITTER_H

#include <string>
#include <list>

#include "guidance_def.h"
#include "guidance/src/debug/data/geojson_writter.h"

namespace aurora {
namespace guide {

class CameraWritter : public GeojsonWritter {
public:
    CameraWritter(const std::string& dir);

    void Write(uint64_t path_id, const NavigationCameraInfoPtr data);
    void Flush();
    void Reset();
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_CAMERA_WRITTER_H
/* EOF */
