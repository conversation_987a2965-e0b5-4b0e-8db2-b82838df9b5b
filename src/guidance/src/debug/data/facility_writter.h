#ifndef MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_FACILITY_WRITTER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_FACILITY_WRITTER_H

#include <list>
#include "guidance_def.h"
#include "guidance/src/debug/data/geojson_writter.h"

namespace aurora {
namespace guide {

class FacilityWritter : public GeojsonWritter {
public:
    FacilityWritter(const std::string& dir);

    void Write(uint64_t path_id, const NavigationFacilityInfoPtr data);
    void Flush();
    void Reset();

protected:
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_FACILITY_WRITTER_H
/* EOF */
