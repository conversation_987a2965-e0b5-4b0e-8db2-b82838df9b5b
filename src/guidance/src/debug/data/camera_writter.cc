// <PERSON><PERSON><PERSON> asserts by default but we dont want to crash running server
// its more useful to throw and catch for our use case
#define RAPIDJSON_ASSERT_THROWS
#undef RAPIDJSON_ASSERT
#define RAPIDJSON_ASSERT(x)                                                                          \
  if (!(x))                                                                                          \
  throw std::logic_error(RAPIDJSON_STRINGIFY(x))
// Because we now throw exceptions, we need to turn off RAPIDJSON_NOEXCEPT
#define RAPIDJSON_HAS_CXX11_NOEXCEPT 0
// Enable std::string overloads
#define RAPIDJSON_HAS_STDSTRING 1

#include "guidance/src/debug/data/camera_writter.h"

#include <fstream>

#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/prettywriter.h"


namespace aurora {
namespace guide {

CameraWritter::CameraWritter(const std::string& dir) 
: <PERSON><PERSON><PERSON><PERSON><PERSON>rit<PERSON>(dir) {

}

void CameraWritter::Write(uint64_t path_id, const NavigationCameraInfoPtr data) {

}
    
void CameraWritter::Flush() {

}
    
void CameraWritter::Reset() {

}

}  // namespace guide
}  // namespace aurora
/* EOF */
