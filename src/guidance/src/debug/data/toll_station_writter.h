#ifndef MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_TOLL_STATION_WRITTER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_TOLL_STATION_WRITTER_H

#include <cstdint>
#include <list>

#include "guidance_def.h"
#include "guidance/src/debug/data/geojson_writter.h"
#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/common/guide_data.h"

namespace aurora {
namespace guide {

class TollStationWritter : public GeojsonWritter {
public:
    struct TollStationData {
        std::string id;
        PointLL     lnglat;
        float       distance;
        std::string station_name;
    };

    TollStationWritter(const std::string& dir);

    void Write(uint64_t path_id, const VehiclePosition &ccp, const TollStationState *data);
    void Flush();
    void Reset();

protected:
    void WriteTollStationImage(EnhanceTollStationPtr ptr);
    void DoWrite();

protected:
    int32_t index_;
    uint64_t path_id_;
    std::list<TollStationData> datas_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_TOLL_STATION_WRITTER_H
/* EOF */
