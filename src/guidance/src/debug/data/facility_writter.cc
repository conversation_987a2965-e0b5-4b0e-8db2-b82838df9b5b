// <PERSON><PERSON><PERSON> asserts by default but we dont want to crash running server
// its more useful to throw and catch for our use case
#define RAPIDJSON_ASSERT_THROWS
#undef RAPIDJSON_ASSERT
#define RAPIDJSON_ASSERT(x)                                                                          \
  if (!(x))                                                                                          \
  throw std::logic_error(RAPIDJSON_STRINGIFY(x))
// Because we now throw exceptions, we need to turn off RAPIDJSON_NOEXCEPT
#define RAPIDJSON_HAS_CXX11_NOEXCEPT 0
// Enable std::string overloads
#define RAPIDJSON_HAS_STDSTRING 1

#include "guidance/src/debug/data/facility_writter.h"

#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/prettywriter.h"


namespace aurora {
namespace guide {

FacilityWritter::FacilityWritter(const std::string& dir)
: G<PERSON><PERSON><PERSON>Writter(dir) {

}

void FacilityWritter::Write(uint64_t path_id, const NavigationFacilityInfoPtr data) {

}
    
void FacilityWritter::Flush() {

}
    
void FacilityWritter::Reset() {

}

}  // namespace guide
}  // namespace aurora
/* EOF */
