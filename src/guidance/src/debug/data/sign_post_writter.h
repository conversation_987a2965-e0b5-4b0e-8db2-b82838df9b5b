#ifndef MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_SIGN_POST_WRITTER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_SIGN_POST_WRITTER_H

#include <string>
#include <list>
#include <vector>

#include "guidance/src/debug/data/geojson_writter.h"
#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/common/guide_data.h"

namespace aurora {
namespace guide {

class SignPostWritter : public GeojsonWritter {
public:
    struct SignPostData {
        std::string id;
        int32_t     type;
        PointLL     lnglat;
        float       distance;
        std::vector<std::string> names;
    };

    SignPostWritter(const std::string &dir);

    void Write(uint64_t path_id, const VehiclePosition &ccp, const SignPostState *state);
    void Flush();
    void Reset();

protected:
    void DoWrite();

protected:
    int32_t  index_;
    uint64_t path_id_;
    std::list<SignPostData> datas_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_DATA_SIGN_POST_WRITTER_H
/* EOF */
