// <PERSON><PERSON><PERSON> asserts by default but we dont want to crash running server
// its more useful to throw and catch for our use case
#define RAPIDJSON_ASSERT_THROWS
#undef RAPIDJSON_ASSERT
#define RAPIDJSON_ASSERT(x)                                                                          \
  if (!(x))                                                                                          \
  throw std::logic_error(RAPIDJSON_STRINGIFY(x))
// Because we now throw exceptions, we need to turn off RAPIDJSON_NOEXCEPT
#define RAPIDJSON_HAS_CXX11_NOEXCEPT 0
// Enable std::string overloads
#define RAPIDJSON_HAS_STDSTRING 1

#include "guidance/src/debug/data/junction_view_writter.h"
#include <fstream>
#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/prettywriter.h"

#include "guidance/src/common/file_util.h"

namespace aurora {
namespace guide {

JunctionViewWritter::JunctionViewWritter(const std::string& dir)
: G<PERSON><PERSON><PERSON>Writter(dir) {

}
    
void JunctionViewWritter::Write(uint64_t path_id, const VehiclePosition &ccp, const JunctionViewState *data) {
    if (path_id == PathID::kInvalidPathId) {
        GUIDE_ASSERT(false);
        return;
    }

    if (path_id_ != PathID::kInvalidPathId && path_id != path_id_) {
        GUIDE_ASSERT(false);
        return;
    }

    if (data == nullptr) {
        return;
    }

    path_id_ = path_id;
    WriteJunctionViewImage(data->ptr);

    JunctionViewData jvd;
    jvd.id = data->ptr->GetId();
    jvd.distance = data->remain_dist;
    jvd.type = data->ptr->GetType();
    jvd.lnglat = ccp.GetProj();
    datas_.emplace_back(jvd);

    if (datas_.size() > 200) {
        DoWrite();
        datas_.clear();
    }
}
    
void JunctionViewWritter::Flush() {
    if (!datas_.empty()) {
        DoWrite();
        datas_.clear();
    }
}
    
void JunctionViewWritter::Reset() {
    path_id_ = PathID::kInvalidPathId;
    index_ = 0;
    datas_.clear();
}

void JunctionViewWritter::WriteJunctionViewImage(EnhanceJunctionViewPtr ptr) {
    if (ptr == nullptr) {
        return;
    }

    std::string dir = dir_ + "/" + std::to_string(path_id_) + "/junction_views/";
    if (!FileUtil::IsDirectoryExist(dir)) {
        FileUtil::CreateDirectory(dir);
    }

    if (ptr->GetBackgroundImage() != nullptr) {
        std::string file_name = dir + ptr->GetId() + "_back.png";
        if (!FileUtil::IsFileExist(file_name)) {
            std::ofstream file(file_name.c_str(), std::ios::binary);
            file.write(reinterpret_cast<const char*>(ptr->GetBackgroundImage()->data()), ptr->GetBackgroundImage()->size());
            file.close();
        }
    }

    if (ptr->GetForegroundImage() != nullptr) {
        std::string file_name = dir + ptr->GetId() + "_front.png";
        if (!FileUtil::IsFileExist(file_name)) {
            std::ofstream file(file_name.c_str(), std::ios::binary);
            file.write(reinterpret_cast<const char*>(ptr->GetForegroundImage()->data()), ptr->GetForegroundImage()->size());
            file.close();
        }
    }

}
    
void JunctionViewWritter::DoWrite() {
    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();
    doc.AddMember("type", "FeatureCollection", allocator);

    rapidjson::Value features(rapidjson::kArrayType);

    // process directed edge
    for (auto &result : datas_) {        
        // add edge info
        rapidjson::Value feature(rapidjson::kObjectType);
        feature.AddMember("type", "Feature", allocator);
            
        rapidjson::Value geometry(rapidjson::kObjectType);
        geometry.AddMember("type", "Point", allocator);

        rapidjson::Value coordinates(rapidjson::kArrayType);
        coordinates.PushBack(static_cast<double>(result.lnglat.first), allocator);
        coordinates.PushBack(static_cast<double>(result.lnglat.second), allocator);
        geometry.AddMember("coordinates", coordinates, allocator);
        feature.AddMember("geometry", geometry, allocator);

        rapidjson::Value properties(rapidjson::kObjectType);

        properties.AddMember("id", result.id, allocator);
        properties.AddMember("type", result.type, allocator);
        properties.AddMember("distance", result.distance, allocator);
        feature.AddMember("properties", properties, allocator);
        features.PushBack(feature, allocator);
    }

    doc.AddMember("features", features, allocator);

    std::string dir = dir_ + "/" + std::to_string(path_id_);
    if (!FileUtil::IsDirectoryExist(dir)) {
        FileUtil::CreateDirectory(dir);
    }

    std::string filename = dir + "/junction_view_" + std::to_string(index_) + ".geojson";
    std::ofstream file(filename.c_str());
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);

    file << buffer.GetString();
    file.close();

    ++index_;

}


}  // namespace guide
}  // namespace aurora
/* EOF */
