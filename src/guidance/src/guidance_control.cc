#include "guidance/src/guidance_control.h"

#include <list>

#include "errorcode.h"
#include "Time.h"

#include "guidance/src/common/guide_log.h"
#include "guidance/src/data/data_manager.h"
#include "guidance/src/common/maneuver.h"
#include "guidance/src/maker/direction_builder.h"
#include "guidance/src/data/vehicle_position.h"

namespace aurora {
namespace guide {

class MapMatchingListener : public loc::MapMatchingListener {
public:
    MapMatchingListener(GuidanceControl *p)
    : impl_(p) {
    }

    void OnMapMatchingResult(const loc::MatchResult &result) override {
        if (impl_) {
            impl_->UpdateMapMatchingPos(result);
        } else {
            GUIDE_LOG_ERROR("impl is empty .");
        }
    }

private:
    GuidanceControl *impl_;
};

class PathResultListener : public path::PathListener {
public:
    PathResultListener(GuidanceControl *p) 
    : impl_(p) {

    }

    void OnPathResult(const PathQueryPtr& query, const path::PathResultPtr& result) {
        if (impl_) {
            // impl_->SetNavPath(result);
        }
    }

private:
    GuidanceControl *impl_;
};


GuidanceControl::GuidanceControl() 
: started_(false) 
, debug_manager_("")
, guidance_event_()
, guidance_runtime_(&guidance_event_, &debug_manager_) {
}

GuidanceControl::~GuidanceControl() {
    UnInit();
}


int32_t  GuidanceControl::Prepare(const std::string &config) {
    InitOptions();
    
    guidance_runtime_.SetGuidanceControl(this);
    
    if (!DataManager::Instance()->InitDataProvider()) {
        return ErrorCode::kErrorCodeFailed;
    }

    direction_builder_.SetOptions(&options_);
    direction_builder_.SetNarrativeDir(config);
    return ErrorCode::kErrorCodeOk;
}

int32_t  GuidanceControl::Init(const InterfaceFinder &finder) {
#if 0
    std::shared_ptr<IInterface> ptr = finder(ModuleId::kModuleIdLocation);
    loc_ = std::dynamic_pointer_cast<loc::ILocation>(ptr);
    if (loc_ == nullptr) {
        GUIDE_LOG_ERROR("find location interface error .");
        return ErrorCode::kErrorCodeGuideMissLocationIF;
    }

    ptr = finder(ModuleId::kModuleIdPath);
    path_ = std::dynamic_pointer_cast<path::PathInterface>(ptr);
    if (path_ == nullptr) {
        GUIDE_LOG_ERROR("find path interface error .");
        return ErrorCode::kErrorCodeGuideMissPathIF;
    }

    mm_listener_ = std::dynamic_pointer_cast<loc::MapMatchingListener>(std::make_shared<MapMatchingListener>(this));
    path_listener_ = std::dynamic_pointer_cast<path::PathListener>(std::make_shared<PathResultListener>(this));

    loc_->AddMapMatchingListener(mm_listener_);
    path_->AddPathListener(path_listener_);
#endif
    return ErrorCode::kErrorCodeOk;
}

int32_t  GuidanceControl::Start() {
    if (started_) {
        GUIDE_LOG_WARN("GuidanceControl has been started ...");
        return ErrorCode::kErrorCodeOk;
    }

    guidance_event_.Start();
    direction_builder_.Start();
    guidance_runtime_.Start();
    started_ = true;
    return ErrorCode::kErrorCodeOk;
}

int32_t  GuidanceControl::Stop() {
    if (!started_) {
        GUIDE_LOG_WARN("GuidanceControl has been stopped ...");
        return ErrorCode::kErrorCodeOk;
    }

    guidance_runtime_.Stop();
    direction_builder_.Stop();
    guidance_event_.Stop();
    started_ = false;
    return ErrorCode::kErrorCodeOk;
}

int32_t  GuidanceControl::UnInit() {
    if (started_) {
        Stop();
    }

    DataManager::Destroy();

    // if (loc_) {
    //     loc_->RemoveMapMatchingListener(mm_listener_);
    // }

    // if (path_) {
    //     path_->RemovePathListener(path_listener_);
    // }
    return ErrorCode::kErrorCodeOk;
}

ModuleId GuidanceControl::GetModuleId() const {
    return ModuleId::kModuleIdGuide;
}

int32_t GuidanceControl::SetParam(const Param &param) {
    return ErrorCode::kErrorCodeNotSupport;
}
    
Param  GuidanceControl::GetParam(const ParamType &type) const {
    return Param();
}

int32_t GuidanceControl::SetNavPath(PathQueryPtr query, PathResultPtr result, uint64_t main_path_id) {        
    // #1. save path
    DataManager::Instance()->UpdatePathDataManager(query, result);
    return SwitchMainPathId(main_path_id);
}

int32_t GuidanceControl::UpdateMapMatchingPos(const loc::MatchResult &result) {
    if (guidance_runtime_.IsGuide()) {
        VehiclePosition vehicle_pos(&result);
        guidance_runtime_.DoGuide(vehicle_pos);
    } else {
        GUIDE_LOG_INFO("No Guide mode, ignore map matching info ");
    }

    return ErrorCode::kErrorCodeOk;
}

int32_t GuidanceControl::SwitchMainPathId(uint64_t target_path_id) {
    debug_manager_.Reset();

    debug_manager_.WritePathQuery(target_path_id, 
                                  DataManager::Instance()->GetPathDataManager()->GetEnhancePathQuery());

    uint64_t start_ms = Time::Now().ToMillisecond();
    bool ret = DataManager::Instance()->GetPathDataManager()->SwitchMainPath(target_path_id);
    if (!ret) {
        GUIDE_ASSERT(false);
        DataManager::Instance()->GetPathDataManager()->DeletePathResult();
        guidance_event_.SwitchPathStatus(target_path_id, false);
        return ErrorCode::kErrorCodeGuidePathIdNotExist;
    }

    // #2. notify maneuver builder
    std::list<Maneuver> maneuver_list;
    PathDataManagerPtr path_manager = DataManager::Instance()->GetPathDataManager();
    GUIDE_ASSERT(path_manager != nullptr);
    if (path_manager == nullptr) {
        GUIDE_LOG_ERROR("GetPathDataManager is nullptr .");
        DataManager::Instance()->GetPathDataManager()->DeletePathResult();
        guidance_event_.SwitchPathStatus(target_path_id, false);
        return ErrorCode::kErrorCodeParamNullPointer;
    }

    debug_manager_.WriteEnhancePath(DataManager::Instance()->GetEnhanceProvider(), 
                                   path_manager->GetEnhancePathResult());

    int32_t code = direction_builder_.Build(path_manager, maneuver_list);
    if (code != ErrorCode::kErrorCodeOk) {
        GUIDE_LOG_ERROR("switch path:{}, direction build error .", target_path_id);
        DataManager::Instance()->GetPathDataManager()->DeletePathResult();
        guidance_event_.SwitchPathStatus(target_path_id, false);
        return code;
    }

    if (!path_manager->UpdateManeuvers(target_path_id, maneuver_list)) {
        DataManager::Instance()->GetPathDataManager()->DeletePathResult();
        guidance_event_.SwitchPathStatus(target_path_id, false);
        return ErrorCode::kErrorCodeGuidePathIdNotExist;
    }

    uint64_t end_ms = Time::Now().ToMillisecond();
    guidance_event_.SwitchPathStatus(target_path_id, true);

    GUIDE_LOG_INFO("+++++++ Maneuver List Num: {} ++++", maneuver_list.size());
    GUIDE_LOG_INFO("+++++++ SetNavPath consume: {} ms", (end_ms - start_ms));

    guidance_event_.NotifyPathManeuverDetail(target_path_id, maneuver_list);    

    debug_manager_.WriteManeuvers(path_manager->GetEnhancePathResult(), maneuver_list);
    return ErrorCode::kErrorCodeOk;
}

int32_t GuidanceControl::RequestPathManeuverInfos(uint64_t path_id) {
    return ErrorCode::kErrorCodeNotSupport;
}

int32_t GuidanceControl::StartNavigation(NavigationMode mode) {
    switch(mode) {
    case NavigationMode::kNavigationModeGPS:
    case NavigationMode::kNavigationModeSimulation:{
        if (DataManager::Instance()->GetPathDataManager()->GetMainPathId() == PathID::kInvalidPathId) {
            GUIDE_ASSERT(false);
            GUIDE_LOG_ERROR("start navigation, miss path result .");
            return ErrorCode::kErrorCodeGuideNavigationMissPath;
        }

        DataManager::Instance()->SetNavigationMode(mode);
        PathDataManagerPtr path_manager = DataManager::Instance()->GetPathDataManager();
        GUIDE_ASSERT(path_manager != nullptr);

        if (path_manager != nullptr) {
            guidance_runtime_.StartNavigation(mode, path_manager);
            return ErrorCode::kErrorCodeOk;
        } else {
            GUIDE_LOG_ERROR("path_manager is nullptr .");
            return ErrorCode::kErrorCodeParamNullPointer;
        }
    }

    case NavigationMode::kNavigationModeCruise: {
        if (DataManager::Instance()->GetPathDataManager()->GetMainPathId() != PathID::kInvalidPathId) {
            GUIDE_ASSERT(false);            
            return ErrorCode::kErrorCodeGuideCruiseExistPath;
        }

        DataManager::Instance()->SetNavigationMode(mode);
        guidance_runtime_.StartNavigation(mode, nullptr);
        return ErrorCode::kErrorCodeOk;
    }

    default:
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("unexpect navigation mode .");
        return ErrorCode::kErrorCodeInvalidParam;
    }
}

int32_t GuidanceControl::StopNavigation(NavigationStopCode code) {
    debug_manager_.ForceFlush();
    DataManager::Instance()->SetNavigationStatus(NavigationStatus::KNavigationStatusOff);
    guidance_runtime_.StopNavigation(code);
    return ErrorCode::kErrorCodeOk;
}

int32_t GuidanceControl::PauseNavigation() {
    DataManager::Instance()->SetNavigationStatus(NavigationStatus::kNavigationStatusPause);
    guidance_runtime_.PauseNavigation();
    return ErrorCode::kErrorCodeOk;
}

int32_t GuidanceControl::ResumeNavigation() {
    DataManager::Instance()->SetNavigationStatus(NavigationStatus::kNavigationStatusOn);
    guidance_runtime_.ResumeNavigation();
    return ErrorCode::kErrorCodeOk;
}

int32_t GuidanceControl::PlayNavigationManual() {
    return ErrorCode::kErrorCodeNotSupport;
}

int32_t GuidanceControl::AddListener(IGuidanceListenerPtr obs) {
    return guidance_event_.AddListener(obs);
}

int32_t GuidanceControl::RemoveListener(IGuidanceListenerPtr obs) {
    return guidance_event_.RemoveListener(obs);
}

void GuidanceControl::InitOptions() {
    options_.distance_unit = DistanceUnits::kDistanceKilometers;
    options_.language_tag = "en_GB"; // TODO: zh_CN
}

}  // namespace guide
}  // namespace aurora
/* EOF */
