#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_JUNCTION_VIEW_STATE_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_JUNCTION_VIEW_STATE_H

#include <cstdint>

#include "guidance/src/data/enhance_junction_view.h"
#include "guidance/src/guide/navigation/data/maneuver_state.h"

namespace aurora {
namespace guide {

struct JunctionViewState {
    EnhanceJunctionViewPtr ptr;
    float  total_dist;
    float  remain_dist;
};

class ManeuverJunctionViewState {
public:
    ManeuverJunctionViewState(ManeuverState &state);

    void UpdateJunctionViews();

    int32_t GetTotalJunctionViewNum() const;
    // int32_t GetForwardJunctionViewNum() const;

    const JunctionViewState* GetFirstJunctionView() const;
    const JunctionViewState* GetLastJunctionView() const;

    const JunctionViewState* GetNextJunctionView() const;

protected:
    ManeuverState& state_;
    std::vector<JunctionViewState> junction_views_;
};


}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_JUNCTION_VIEW_STATE_H
/* EOF */
