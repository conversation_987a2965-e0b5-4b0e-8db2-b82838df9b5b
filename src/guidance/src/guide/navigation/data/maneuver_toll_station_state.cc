#include "guidance/src/guide/navigation/data/maneuver_toll_station_state.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

ManeuverTollStationState::ManeuverTollStationState(ManeuverState &state) 
: state_(state) {

    EnhancePathResultPtr result = state.GetPathManager()->GetEnhancePathResult();

    int32_t snode_index = state.GetManeuverIt()->GetBeginNodeIndex();
    int32_t enode_index = state.GetManeuverIt()->GetEndNodeIndex();

    float acc_dist = 0;
    for (int32_t index = snode_index + 1; index <= enode_index; ++index) {
        EnhanceNode *node = result->GetNode(index);
        if (node == nullptr) {
            GUIDE_ASSERT(node != nullptr);
            return;
        }

        DirectedEdge *prev_edge = result->GetPrevEdge(index);
        if (prev_edge == nullptr) {
            GUIDE_ASSERT(prev_edge != nullptr);
            GUIDE_LOG_ERROR("prev_edge is nullptr .");
            return;
        }

        acc_dist += prev_edge->GetLengthMeter();

        if (node->GetTollStation() != nullptr) {
            TollStationState station_state;
            station_state.ptr = node->GetTollStation();
            station_state.total_dist = state_.GetDistanceToSNode() + acc_dist;
            station_state.remain_dist = 0;
            toll_station_states_.emplace_back(station_state);
        }
    }
}

void ManeuverTollStationState::UpdateTollStations() {
    for (auto &station : toll_station_states_) {
        station.remain_dist = station.total_dist - state_.GetDriveDistance();
    }
}

int32_t ManeuverTollStationState::GetTollStationNum() const {
    return toll_station_states_.size();
}

const TollStationState* ManeuverTollStationState::GetFirsTollStation() const {
    if (!toll_station_states_.empty()) {
        return &(toll_station_states_.front());
    }
    return nullptr;
}
    
const TollStationState* ManeuverTollStationState::GetLastTollStation() const {
    if (!toll_station_states_.empty()) {
        return &(toll_station_states_.back());
    }
    return nullptr;
}
    
const TollStationState* ManeuverTollStationState::GetNextTollStation() const {
    auto status = state_.GetVehicleManeuverStatus();

    if (status == VehicleOnManeuverStatus::kVehicleOnManeuver) {
        for (int index = 0; index < toll_station_states_.size(); ++index) {
            if (toll_station_states_.at(index).remain_dist > 0) {
                return &(toll_station_states_[index]);
            }
        }
    }

    if (status == VehicleOnManeuverStatus::kVehicleNotArriveManeuver) {
        return GetFirsTollStation();
    }

    return nullptr;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
