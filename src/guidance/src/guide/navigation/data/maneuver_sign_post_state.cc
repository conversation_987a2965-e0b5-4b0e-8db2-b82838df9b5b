#include "guidance/src/guide/navigation/data/maneuver_sign_post_state.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

ManeuverSignPostState::ManeuverSignPostState(ManeuverState &state)
: state_(state) {

    EnhancePathResultPtr result = state.GetPathManager()->GetEnhancePathResult();
    if (result == nullptr) {
        GUIDE_ASSERT(result != nullptr);
        GUIDE_LOG_ERROR("path result is nullptr .");
        return;
    }

    int32_t snode_index = state.GetManeuverIt()->GetBeginNodeIndex();
    int32_t enode_index = state.GetManeuverIt()->GetEndNodeIndex();

    float acc_dist = 0;
    for (int32_t index = snode_index + 1; index <= enode_index; ++index) {
        EnhanceNode *node = result->GetNode(index);
        if (node == nullptr) {
            GUIDE_ASSERT(node != nullptr);
            GUIDE_LOG_ERROR("node is nullptr .");
            return;
        }

        DirectedEdge *prev_edge = result->GetPrevEdge(index);
        if (prev_edge == nullptr) {
            GUIDE_ASSERT(prev_edge != nullptr);
            GUIDE_LOG_ERROR("prev_edge is nullptr .");
            return;
        }

        acc_dist += prev_edge->GetLengthMeter();

        int32_t sign_post_num = node->GetSignPostNum();
        for (int32_t index = 0; index < sign_post_num; ++index) {       
            SignPostState sign_post_state;
            sign_post_state.ptr = node->GetSignPost(index);
            sign_post_state.total_dist = state.GetDistanceToSNode() + acc_dist;
            sign_post_state.remain_dist = 0;

            sign_posts_.emplace_back(sign_post_state);
        }
    }
}

void ManeuverSignPostState::UpdateSignPosts() {
    for (auto &sign_post : sign_posts_) {
        sign_post.remain_dist = sign_post.total_dist - state_.GetDriveDistance();
    }
}

int32_t ManeuverSignPostState::GetSignPostNum() const {
    return sign_posts_.size();
}
    
const SignPostState* ManeuverSignPostState::GetFirstSignPost() const {
    if (sign_posts_.empty()) {
        return nullptr;
    }

    return &(sign_posts_.front());
}
    
const SignPostState* ManeuverSignPostState::GetLastSignPost() const {
    if (sign_posts_.empty()) {
        return nullptr;
    }

    return &(sign_posts_.back());
}
    
const SignPostState* ManeuverSignPostState::GetNextSignPost() const {
    auto status = state_.GetVehicleManeuverStatus();

    if (status == VehicleOnManeuverStatus::kVehicleOnManeuver) {
        for (int index = 0; index < sign_posts_.size(); ++index) {
            if (sign_posts_.at(index).remain_dist > 0) {
                return &(sign_posts_[index]);
            }
        }
    }

    if (status == VehicleOnManeuverStatus::kVehicleNotArriveManeuver) {
        return GetFirstSignPost();
    }

    return nullptr;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
