#include "guidance/src/guide/navigation/data/maneuver_state.h"
#include "Time.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

ManeuverState::ManeuverState(PathDataManagerPtr path_manager, ManeuverIt &it) 
: path_manager_(path_manager)
, maneuver_it_(it) {

    far_stamp_ = 0;
    mid_stamp_ = 0;
    near_stamp_ = 0;
    closeto_stamp_ = 0;
    post_stamp_ = 0;
    arrive_stamp_ = 0;

    remain_time_ = 0;
    total_distance_ = 0xFFFF;
    drive_distance_ = 0;
}

void ManeuverState::UpdateDriveDistance(float drive_distance) {
    drive_distance_ = drive_distance;

    if (GetVehicleManeuverStatus() == VehicleOnManeuverStatus::kVehicleOnManeuver) {
        maneuver_it_->SetLengthMeterToENode(GetDistanceToENode());
    }
}
 
float ManeuverState::GetDriveDistance() const {
    return drive_distance_;
}

void ManeuverState::InitTotalDistane(float distance) {
    total_distance_ = distance;
}

float ManeuverState::GetTotalDistance() const {
    return total_distance_;
}

float ManeuverState::GetDistanceToSNode() const {
    float result = (total_distance_ - drive_distance_);
    return (static_cast<int32_t>(result * 100)) * 0.01;  // precision: 1cm
}

float ManeuverState::GetDistanceToENode() const {
    float result = (total_distance_ + maneuver_it_->GetLengthMeter() - drive_distance_);
    return (static_cast<int32_t>(result * 100)) * 0.01;  // precision: 1cm
}

float ManeuverState::GetTimeToENode() const {
    if (GetVehicleManeuverStatus() == VehicleOnManeuverStatus::kVehicleOnManeuver) {
        float dis_to_enode = GetDistanceToENode();

        if (dis_to_enode <= 0) {
            return 0;
        }

        EnhancePathResultPtr path_ptr = path_manager_->GetEnhancePathResult();
        if (path_ptr == nullptr) {
            GUIDE_ASSERT(path_ptr != nullptr);
            GUIDE_LOG_ERROR("path is nullptr .");
            return 0;
        }

        int32_t begin_node_idx = maneuver_it_->GetBeginNodeIndex();
        int32_t end_node_idx = maneuver_it_->GetEndNodeIndex();
        double acc_dist = 0;
        double acc_time = 0;
        for (int32_t node_idx = end_node_idx; node_idx > begin_node_idx; --node_idx) {
            DirectedEdge *edge = path_ptr->GetPrevEdge(node_idx);
            if (acc_dist < dis_to_enode && (acc_dist + edge->GetLengthMeter()) >= dis_to_enode) {
                acc_time += (dis_to_enode - acc_dist)/edge->GetLengthMeter() * edge->GetBasicTime();
                break;
            }

            acc_dist += edge->GetLengthMeter();
            acc_time += edge->GetBasicTime();
        }
        return acc_time;
    }

    GUIDE_ASSERT(false);
    GUIDE_LOG_ERROR("ccp is not on this maneuver, GetTimeToENode is not supported .");
    return 0;
}

VehicleOnManeuverStatus ManeuverState::GetVehicleManeuverStatus() const {
    float dist_to_snode = GetDistanceToSNode();
    float dist_to_enode = GetDistanceToENode();

    if (dist_to_enode < 0) {
        return VehicleOnManeuverStatus::kVehiclePassedManeuver;
    }

    if (dist_to_snode > 0) {
        return VehicleOnManeuverStatus::kVehicleNotArriveManeuver;
    }

    return VehicleOnManeuverStatus::kVehicleOnManeuver;
}


bool ManeuverState::IsFarInstructionTag() const {
    return (far_stamp_ > 0);
}

std::string ManeuverState::GetFarInstruction() const {
    return maneuver_it_->GetVerbalPostInstruction();
}

std::string ManeuverState::GetFarInstructionWithTag() {
    far_stamp_ = Time::Now().ToMillisecond();
    return GetFarInstruction();
}

bool ManeuverState::IsMiddleInstructionTag() const {
    return (mid_stamp_ > 0);
}

std::string ManeuverState::GetMiddleInstruction() const {
    // return maneuver_it_->GetVerbalPostInstruction();
    if (!maneuver_it_->GetVerbalAlertInstruction().empty()) {
        return maneuver_it_->GetVerbalAlertInstruction();
    }
    return maneuver_it_->GetVerbalPreInstruction();
}

std::string ManeuverState::GetMiddleInstructionWithTag() {
    mid_stamp_ = Time::Now().ToMillisecond();

    if (far_stamp_ <= 0) {
        far_stamp_ = mid_stamp_;
    }

    return GetMiddleInstruction();
}

bool ManeuverState::IsNearInstructionTag() const {
    return (near_stamp_ > 0);
}

std::string ManeuverState::GetNearInstruction() const {
    if (!maneuver_it_->GetVerbalAlertInstruction().empty()) {
        return maneuver_it_->GetVerbalAlertInstruction();
    }
    return maneuver_it_->GetVerbalPreInstruction();
}

std::string ManeuverState::GetNearInstructionWithTag() {
    near_stamp_ = Time::Now().ToMillisecond();
    if (mid_stamp_ <= 0) {
        mid_stamp_ = near_stamp_;
    }

    if (far_stamp_ <= 0) {
        far_stamp_ = near_stamp_;
    }
    return GetNearInstruction();
}

bool ManeuverState::IsCloseToInstructionTag() const {
    return (closeto_stamp_ > 0);
}

std::string ManeuverState::GetCloseToInstruction() const {
    if (!maneuver_it_->GetVerbalSuccinctInstruction().empty()) {
        return maneuver_it_->GetVerbalSuccinctInstruction();
    }
    return maneuver_it_->GetVerbalPreInstruction();
}

std::string ManeuverState::GetCloseToInstructionWithTag() {
    closeto_stamp_ = Time::Now().ToMillisecond();

    if (near_stamp_ <= 0) {
        near_stamp_ = closeto_stamp_;
    }

    if (mid_stamp_ <= 0) {
        mid_stamp_ = closeto_stamp_;
    }

    if (far_stamp_ <= 0) {
        far_stamp_ = closeto_stamp_;
    }
    return GetCloseToInstruction();
}

bool ManeuverState::IsArriveInstructionTag() const {
    return (arrive_stamp_ > 0);
}

std::string ManeuverState::GetArriveInstruction() const {
    return maneuver_it_->GetVerbalPreInstruction();
}

std::string ManeuverState::GetArriveInstructionWithTag() {
    arrive_stamp_ = Time::Now().ToMillisecond();

    if (closeto_stamp_ <= 0) {
        closeto_stamp_ = arrive_stamp_;
    }

    if (near_stamp_ <= 0) {
        near_stamp_ = arrive_stamp_;
    }

    if (mid_stamp_ <= 0) {
        mid_stamp_ = arrive_stamp_;
    }

    if (far_stamp_ <= 0) {
        far_stamp_ = arrive_stamp_;
    }

    return GetArriveInstruction();
}

bool ManeuverState::IsPostInstructionTag() const {
    return (post_stamp_ > 0);
}

std::string ManeuverState::GetPostInstruction() const {
    return maneuver_it_->GetVerbalPostInstruction();
}
   
std::string ManeuverState::GetPostInstructionWithTag() {
    post_stamp_ = Time::Now().ToMillisecond();
    return maneuver_it_->GetVerbalPostInstruction();
}

int32_t ManeuverState::GetVehicleLinkIndex() const {
    int32_t dist_snode = GetDistanceToSNode();
    int32_t dist_enode = GetDistanceToENode();

    if ((dist_snode < 0) && (dist_enode > 0)) {
        int32_t snode_idx = maneuver_it_->GetBeginNodeIndex();
        int32_t enode_idx = maneuver_it_->GetEndNodeIndex();

        float maneuver_offset = std::fabs(dist_snode);
        float acc_offset = 0;
        for (int32_t index = snode_idx; index < enode_idx; ++index) {
            DirectedEdge *edge = path_manager_->GetEnhancePathResult()->GetEdge(index);
            GUIDE_ASSERT(edge != nullptr);

            if(edge == nullptr) {
                break;
            }

            if (acc_offset < maneuver_offset && maneuver_offset <= (acc_offset + edge->GetLengthMeter())) {
                return index;
            }

            acc_offset += edge->GetLengthMeter();
        } // for
    } // if

    GUIDE_ASSERT(false);
    return -1;
}

ManeuverIt ManeuverState::GetManeuverIt() {
    return maneuver_it_;
}

PathDataManagerPtr ManeuverState::GetPathManager() {
    return path_manager_;
}

}  // namespace guide
}  // namespace aurora
/* EOF */