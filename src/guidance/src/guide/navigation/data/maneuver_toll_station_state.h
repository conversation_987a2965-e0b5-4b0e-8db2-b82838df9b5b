#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_TOLL_STATION_STATE_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_TOLL_STATION_STATE_H

#include <cstdint>
#include "guidance/src/data/enhance_toll_station.h"
#include "guidance/src/guide/navigation/data/maneuver_state.h"

namespace aurora {
namespace guide {

struct TollStationState {
    EnhanceTollStationPtr ptr;
    std::string text;
    float  total_dist;
    float  remain_dist;
};

class ManeuverTollStationState {
public:
    ManeuverTollStationState(ManeuverState &state);
    ~ManeuverTollStationState() = default;

    void UpdateTollStations();

    int32_t GetTollStationNum() const;
    const TollStationState* GetFirsTollStation() const;
    const TollStationState* GetLastTollStation() const;
    const TollStationState* GetNextTollStation() const;

protected:
    ManeuverState &state_;
    std::vector<TollStationState> toll_station_states_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_TOLL_STATION_STATE_H
/* EOF */
