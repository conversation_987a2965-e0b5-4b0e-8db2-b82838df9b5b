#include "guidance/src/guide/navigation/data/maneuver_sapa_state.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

ManeuverSAPAState::ManeuverSAPAState(ManeuverState& state) 
: state_(state) {

    EnhancePathResultPtr result = state.GetPathManager()->GetEnhancePathResult();
    if (result == nullptr) {
        GUIDE_ASSERT(result != nullptr);
        GUIDE_LOG_ERROR("path result is nullptr .");
        return;
    }

    int32_t snode_index = state.GetManeuverIt()->GetBeginNodeIndex();
    int32_t enode_index = state.GetManeuverIt()->GetEndNodeIndex();

    state_.GetDistanceToSNode();
    float acc_dist = 0;
    for (int32_t node_index = snode_index; node_index < enode_index; ++node_index) {
        DirectedEdge *edge = result->GetEdge(node_index);
        if (edge == nullptr) {
            GUIDE_ASSERT(false);
            GUIDE_LOG_ERROR("GetEdge(index={}) is nullptr .", node_index);
            continue;
        }

        EnhanceSAPAPtr enhance_sapa = edge->GetSAPA();
        if (enhance_sapa != nullptr && enhance_sapa->IsValid()) {
            SAPAState sapa_state;
            sapa_state.ptr = enhance_sapa;
            sapa_state.total_dist = acc_dist + state_.GetDistanceToSNode();
            sapa_state.remain_dist = 0;
            sapa_states_.emplace_back(sapa_state);
        }

        acc_dist += edge->GetLengthMeter();
    }
}

void ManeuverSAPAState::UpdateSAPAs() {
    for (auto &sapa : sapa_states_) {
        sapa.remain_dist = sapa.total_dist - state_.GetDriveDistance();
    }
}

int32_t ManeuverSAPAState::GetSAPANum() const {
    return sapa_states_.size();
}
    
const SAPAState* ManeuverSAPAState::GetFirstSAPA() const {
     if (sapa_states_.empty()) {
        return nullptr;
    }

    return &(sapa_states_.front());
}
    
const SAPAState* ManeuverSAPAState::GetLastSAPA() const {
    if (sapa_states_.empty()) {
        return nullptr;
    }

    return &(sapa_states_.back());
}
    
const SAPAState* ManeuverSAPAState::GetNextSAPA() const {
    auto status = state_.GetVehicleManeuverStatus();

    if (status == VehicleOnManeuverStatus::kVehicleOnManeuver) {
        for (int index = 0; index < sapa_states_.size(); ++index) {
            if (sapa_states_.at(index).remain_dist > 0) {
                return &(sapa_states_[index]);
            }
        }
    }

    if (status == VehicleOnManeuverStatus::kVehicleNotArriveManeuver) {
        return GetFirstSAPA();
    }
    return nullptr;
}


}  // namespace guide
}  // namespace aurora
/* EOF */
