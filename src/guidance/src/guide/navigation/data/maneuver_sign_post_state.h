#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_SIGN_POST_STATE_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_SIGN_POST_STATE_H

#include <cstdint>
#include <vector>
#include "guidance/src/data/enhance_sign_post.h"
#include "guidance/src/guide/navigation/data/maneuver_state.h"

namespace aurora {
namespace guide {

struct SignPostState {
    EnhanceSignPostPtr ptr;
    float  total_dist;
    float  remain_dist;
};

class ManeuverSignPostState {
public:
    ManeuverSignPostState(ManeuverState &state);
    ~ManeuverSignPostState() = default;

    void UpdateSignPosts();

    int32_t GetSignPostNum() const;
    const SignPostState* GetFirstSignPost() const;
    const SignPostState* GetLastSignPost() const;
    const SignPostState* GetNextSignPost() const;

protected:
    ManeuverState &state_;
    std::vector<SignPostState> sign_posts_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_SIGN_POST_STATE_H
/* EOF */
