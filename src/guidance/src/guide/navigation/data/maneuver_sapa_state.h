#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_SAPA_STATE_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_SAPA_STATE_H

#include <cstdint>
#include <vector>
#include "guidance/src/data/enhance_sapa.h"
#include "guidance/src/guide/navigation/data/maneuver_state.h"


namespace aurora {
namespace guide {

struct SAPAState {
    EnhanceSAPAPtr ptr;
    float          total_dist;
    float          remain_dist;
};

class ManeuverSAPAState {
public:
    ManeuverSAPAState(ManeuverState& state);

    void UpdateSAPAs();

    int32_t GetSAPANum() const;
    const SAPAState* GetFirstSAPA() const;
    const SAPAState* GetLastSAPA() const;
    const SAPAState* GetNextSAPA() const;

protected:
    ManeuverState& state_;
    std::vector<SAPAState> sapa_states_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_SAPA_STATE_H
/* EOF */
