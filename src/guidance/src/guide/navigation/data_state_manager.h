#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_MANEUVERS_MANAGER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_MANEUVERS_MANAGER_H

#include <string>
#include <list>
#include <map>

#include "guidance/src/common/maneuver.h"
#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/data/path_data_manager.h"

#include "guidance/src/guide/navigation/data/maneuver_state.h"
#include "guidance/src/guide/navigation/data/maneuver_lane_info_state.h"
#include "guidance/src/guide/navigation/data/maneuver_junction_view_state.h"
#include "guidance/src/guide/navigation/data/maneuver_facility_state.h"
#include "guidance/src/guide/navigation/data/maneuver_toll_station_state.h"
#include "guidance/src/guide/navigation/data/maneuver_sapa_state.h"
#include "guidance/src/guide/navigation/data/maneuver_sign_post_state.h"

namespace aurora {
namespace guide {


struct ManeuverConstItCompare {
    bool operator() (const ManeuverConstIt &lhs, const ManeuverConstIt &rhs) const {
        return (lhs->GetBeginNodeIndex() < rhs->GetBeginNodeIndex());
    }
};

struct ManeuverStateItCompare {
    bool operator() (const ManeuverStateIt &lhs, const ManeuverStateIt &rhs) const {
        return (lhs->GetManeuverIt()->GetBeginNodeIndex() < rhs->GetManeuverIt()->GetBeginNodeIndex());
    }  
};


class DataStateManager {
public:
    DataStateManager(PathDataManagerPtr path_manager);
    ~DataStateManager();

    void UpdateStateByVehiclePos(const VehiclePosition &ccp);

    double         GetDriveDistance() const;

    ManeuverState* GetPrevManeuverState() const;
    ManeuverState* GetCurrManeuverState() const;
    ManeuverState* GetNextManeuverState() const;

    ManeuverLaneInfoState* GetPrevManeuverLaneInfoState() const;
    ManeuverLaneInfoState* GetCurrManeuverLaneInfoState() const;
    ManeuverLaneInfoState* GetNextManeuverLaneInfoState() const;

    ManeuverJunctionViewState* GetPrevManeuverJunctionViewState() const;
    ManeuverJunctionViewState* GetCurrManeuverJunctionViewState() const;
    ManeuverJunctionViewState* GetNextManeuverJunctionViewState() const;

    ManeuverFacilityState* GetPrevManeuverFacilityState() const;
    ManeuverFacilityState* GetCurrManeuverFacilityState() const;
    ManeuverFacilityState* GetNextManeuverFacilityState() const;

    ManeuverTollStationState* GetPrevManeuverStationState() const;
    ManeuverTollStationState* GetCurrManeuverStationState() const;
    ManeuverTollStationState* GetNextManeuverStationState() const;

    ManeuverSAPAState* GetPrevManeuverSAPAState() const;
    ManeuverSAPAState* GetCurrManeuverSAPAState() const;
    ManeuverSAPAState* GetNextManeuverSAPAState() const;

    ManeuverSignPostState* GetPrevManeuverSignPostState() const;
    ManeuverSignPostState* GetCurrManeuverSignPostState() const;
    ManeuverSignPostState* GetNextManeuverSignPostState() const;


protected:
    void BuildManeuverStates();
    void BuildManeuverLaneInfoStates();
    void BuildManeuverJunctionViewStates();
    void BuildManeuverFacilityStates();
    void BuildManeuverStationStates();
    void BuildManeuverSAPAStates();
    void BuildManeuverSignPostStates();

    bool UpdateCurrPos(const VehiclePosition &ccp);
    void UpdateManeuverStates(const VehiclePosition &ccp);
    void UpdateManeuverLaneInfoStates();
    void UpdateManeuverJunctionViesStates();
    void UpdateManeuverFacilityStates();
    void UpdateManeuverStationStates();
    void UpdateManeuverSAPAStates();
    void UpdateManeuverSignPostStates();

private:
    double drive_distance_;
    VehiclePosition prev_pos_;
    ManeuverStateIt cur_it_;
    PathDataManagerPtr path_manager_;
    std::list<ManeuverState> maneuver_states_;
    std::list<ManeuverLaneInfoState> maneuver_laneinfo_states_;
    std::list<ManeuverJunctionViewState> maneuver_junction_states_;
    std::list<ManeuverFacilityState> maneuver_facility_states_;
    std::list<ManeuverTollStationState> maneuver_station_states_;
    std::list<ManeuverSAPAState> maneuver_sapa_states_;
    std::list<ManeuverSignPostState> maneuver_sign_states_;

    std::map<ManeuverStateIt, ManeuverLaneInfoState*, ManeuverStateItCompare> lane_info_relations_;
    std::map<ManeuverStateIt, ManeuverJunctionViewState*, ManeuverStateItCompare> junction_view_relations_;
    std::map<ManeuverStateIt, ManeuverFacilityState*, ManeuverStateItCompare> facility_relations_;
    std::map<ManeuverStateIt, ManeuverTollStationState*, ManeuverStateItCompare> station_relations_;
    std::map<ManeuverStateIt, ManeuverSAPAState*, ManeuverStateItCompare> sapa_relations_;
    std::map<ManeuverStateIt, ManeuverSignPostState*, ManeuverStateItCompare> sign_post_relations_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_MANEUVERS_MANAGER_H
/* EOF */
