#include "guidance/src/guide/guide_runtime.h"
#include "guidance/src/guidance_control.h"
#include "guidance_def.h"
#include "guidance/src/common/guide_log.h"
#include "errorcode.h"

namespace aurora {
namespace guide {

GuideRuntime::GuideRuntime(GuidanceEvent *guide_event, DebugManager *debug_manager)
: guide_event_(guide_event) 
, debug_manager_(debug_manager)
, guidance_control_(nullptr) {
    nav_status_ = NavigationStatus::kNavigationStatusNone;
    mode_ = NavigationMode::kNavigationModeNone;
}

GuideRuntime::~GuideRuntime() {

}

void GuideRuntime::SetGuidanceControl(GuidanceControl *control) {
    guidance_control_ = control;
}

int32_t GuideRuntime::Start() {
    return 0;
}

int32_t GuideRuntime::Stop() {
    if (IsGuide()) {
        StopNavigation(NavigationStopCode::kStopCodeModuleUnInit);
    }
    return 0;
}

bool GuideRuntime::IsGuide() const {
    if ((mode_ == NavigationMode::kNavigationModeGPS || mode_ == NavigationMode::kNavigationModeSimulation ||
            mode_ == NavigationMode::kNavigationModeCruise) && 
                (nav_status_ == NavigationStatus::kNavigationStatusOn)) {
        return true;
    }
    return false;
}


int32_t GuideRuntime::StartNavigation(NavigationMode mode, PathDataManagerPtr path_manager) {
    if (mode == NavigationMode::kNavigationModeGPS ||
        mode == NavigationMode::kNavigationModeSimulation ||
        mode == NavigationMode::kNavigationModeCruise) {

        mode_ = mode;
        nav_status_ = NavigationStatus::kNavigationStatusOn;
        path_manager_ = path_manager;
        tracker_ = AbstractStateTracker::Create(mode, path_manager);
        guide_event_->StartNavigation(mode, path_manager->GetMainPathId());

    } else {
        // ignore
    }

    // TODO:
    return ErrorCode::kErrorCodeOk;
}

int32_t GuideRuntime::StopNavigation(NavigationStopCode code) {
    // #1. interrupt
    // #2. clear 
    // #3. callback

    tracker_ = nullptr;
    mode_ = NavigationMode::kNavigationModeNone;
    nav_status_ = NavigationStatus::KNavigationStatusOff;
    path_manager_ = nullptr;
    guide_event_->StopNavigation(mode_, code);

    prev_guide_data_ = GuideData();

    return 0;
}

int32_t GuideRuntime::PauseNavigation() {
    nav_status_ = NavigationStatus::kNavigationStatusPause;
    // #1. callback
    guide_event_->PauseNavigation(mode_);
    return 0;
}
    
int32_t GuideRuntime::ResumeNavigation() {
    nav_status_ = NavigationStatus::kNavigationStatusOn;
    guide_event_->ResumeNavigation(mode_);
    return 0;
}

int32_t GuideRuntime::DoGuide(const VehiclePosition &ccp) {
    if (tracker_ == nullptr) {
        GUIDE_ASSERT(tracker_ != nullptr);
        GUIDE_LOG_ERROR("tracker_ is nullptr .");
        return ErrorCode::kErrorCodeParamNullPointer;
    }

    if (path_manager_ == nullptr) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("path_manager_ is nullptr .");
        return ErrorCode::kErrorCodeParamNullPointer;
    }

    GuideData guide_data;
    tracker_->DoGuide(ccp, guide_data);
    ProcessDebug(ccp, guide_data);
    ProcessGuideData(guide_data);
    prev_guide_data_ = guide_data;

    return ErrorCode::kErrorCodeOk;
}

void GuideRuntime::ProcessGuideData(const GuideData &guide_data) {
    if (guide_event_ == nullptr) {
        return;
    }

    if (!guide_data.voice_text.empty()) {
        guide_event_->PlayVoice(guide_data.voice_text);
    }
    
    // TODO: 后续检测到LaneInfo变更在发送通知，而不是定频发送
    if (guide_data.lane_info_state != nullptr && guide_data.lane_info_state->ptr != nullptr) {
        NavigationLaneInfoPtr lane_info = std::make_shared<NavigationLaneInfo>();
        EnhanceLaneInfoPtr ptr = guide_data.lane_info_state->ptr;
        lane_info->id = ptr->GetId();
        lane_info->back_lanes = ptr->GetBackLaneActions();
        lane_info->front_lanes = ptr->GetFrontLaneActions();
        guide_event_->ShowLaneInfo(lane_info);
    } else {
        guide_event_->ShowLaneInfo(nullptr);
    }

    // TODO: 后续检测到JunctionView变更在发送通知，而不是定频发送
    if (guide_data.junction_view_state != nullptr && guide_data.junction_view_state->ptr != nullptr) {
        NavigationJunctionViewInfoPtr junc_info = std::make_shared<NavigationJunctionViewInfo>();
        EnhanceJunctionViewPtr ptr = guide_data.junction_view_state->ptr;
        junc_info->id = ptr->GetId();
        junc_info->type = ptr->GetType();
        junc_info->remain_dist = guide_data.junction_view_state->remain_dist;
        if (junc_info->remain_dist < 0) {
            junc_info->remain_dist = 0;
            GUIDE_ASSERT(false);
        }

        if (ptr->GetBackgroundImage() != nullptr) {
            junc_info->back_view_data = *(ptr->GetBackgroundImage());
        }

        if (ptr->GetForegroundImage() != nullptr) {
            junc_info->front_view_data = *(ptr->GetForegroundImage());
        }
        guide_event_->ShowJunctionView(junc_info);

    } else {
        guide_event_->ShowJunctionView(nullptr);
    }

    // process toll station
    if (guide_data.toll_station_state != nullptr && guide_data.toll_station_state->ptr != nullptr) {
        NavigationTollStationInfoPtr toll_station_info = std::make_shared<NavigationTollStationInfo>();
        EnhanceTollStationPtr ptr = guide_data.toll_station_state->ptr;

        toll_station_info->id = ptr->GetId();
        toll_station_info->remain_dist = guide_data.toll_station_state->remain_dist;
        if (toll_station_info->remain_dist < 0) {
            GUIDE_ASSERT(toll_station_info->remain_dist >= 0);
            toll_station_info->remain_dist = 0;
        }

        toll_station_info->station_name = ptr->GetName();
        if (ptr->GetImage() != nullptr) {
            toll_station_info->view_data = *(ptr->GetImage());
        }
        toll_station_info->gate_infos = ptr->GetTollPorts();
        guide_event_->UpdateTollStation(toll_station_info);
    } else {
        guide_event_->UpdateTollStation(nullptr);
    }

    // process nav guide board
    if (guide_data.path_id != PathID::kInvalidPathId) {
        NavigationInfoPtr nav_info = std::make_shared<NavigationInfo>();
        nav_info->mode = guide_data.mode;
        nav_info->path_id = guide_data.path_id;
        nav_info->curr_road_name = guide_data.road_name;
        nav_info->path_remain = guide_data.path_remain;
        nav_info->next_maneuver_points = guide_data.next_maneuver_points;

        // GUIDE_LOG_INFO("MapMatching: PathLindIdx={}, offset={}", guide_data.pos.GetPathLinkIndex(), guide_data.pos.GetLinkOffset());
        guide_event_->UpdateNavigationInfo(nav_info);
    }

    // process facility
    // TODO: 此处智能播发前方一个设施或Camera，后续需要调整，支持按照范围播发
    std::vector<NavigationFacilityInfoPtr> facility_infos;
    std::vector<NavigationCameraInfoPtr> camera_infos;
    if (guide_data.facility_state != nullptr) {
        EnhanceFacilityPtr enhance_facility = guide_data.facility_state->ptr;
        GUIDE_ASSERT(enhance_facility != nullptr);
        if (enhance_facility != nullptr) {
            if (enhance_facility->IsFacility()) {
                NavigationFacilityInfoPtr facility_info = std::make_shared<NavigationFacilityInfo>();
                facility_info->id = enhance_facility->GetId();
                facility_info->type = enhance_facility->GetFacilityType();
                facility_info->remain_dist = guide_data.facility_state->remain_dist;
                facility_info->lnglat = enhance_facility->GetPosition();
                facility_infos.emplace_back(facility_info);
            } else if (enhance_facility->IsCamera()) {
                NavigationCameraInfoPtr camera_info = std::make_shared<NavigationCameraInfo>();
                camera_info->id = enhance_facility->GetId();
                camera_info->type = enhance_facility->GetCameraType();
                camera_info->angle = enhance_facility->GetAngle();
                camera_info->remain_dist = guide_data.facility_state->remain_dist;
                camera_info->lnglat = enhance_facility->GetPosition();
                camera_info->speed_limit = enhance_facility->GetLimitSpeed();
                camera_infos.emplace_back(camera_info);
            } else {
                GUIDE_ASSERT(false);
            }
        }
    }
    guide_event_->UpdateFacilityInfos(facility_infos);
    guide_event_->UpdateCameraInfos(camera_infos);

    // process sapa
    std::vector<NavigationSAPAInfoPtr> sapa_infos;
    if (guide_data.sapa_state != nullptr) {
        EnhanceSAPAPtr enhance_sapa = guide_data.sapa_state->ptr;
        GUIDE_ASSERT(enhance_sapa != nullptr);

        if (enhance_sapa != nullptr) {
            NavigationSAPAInfoPtr sapa_info = std::make_shared<NavigationSAPAInfo>();
            sapa_info->id = enhance_sapa->GetId();
            sapa_info->lnglat = enhance_sapa->GetPosition();
            sapa_info->type = enhance_sapa->GetNavType();
            sapa_info->name = enhance_sapa->GetName();
            sapa_info->remain_dist = guide_data.sapa_state->remain_dist;
            sapa_info->detail = enhance_sapa->GetDetail();
            sapa_infos.emplace_back(sapa_info);
        }
    }
    guide_event_->UpdateSAPAInfos(sapa_infos);

    // process sign post
    std::vector<GuidanceSignPostInfoPtr> sign_posts;
    if (guide_data.sign_post_state != nullptr) {
        EnhanceSignPostPtr enhance_sign = guide_data.sign_post_state->ptr;
        GUIDE_ASSERT(enhance_sign != nullptr);

        if (enhance_sign != nullptr) {
            GuidanceSignPostInfoPtr sign_post = std::make_shared<GuidanceSignPostInfo>();
            sign_post->id = enhance_sign->GetId();
            sign_post->type = enhance_sign->GetType();
            sign_post->remain_dist = guide_data.sign_post_state->remain_dist;
            sign_post->lnglat = enhance_sign->GetPosition();
            sign_post->name_infos = enhance_sign->GetNames();
            sign_posts.emplace_back(sign_post);
        }
        guide_event_->UpdateSignPostInfos(sign_posts);
    }

    // process destination
    if (guide_data.is_arrive_dest) {
        guide_event_->ArriveDestiontion(mode_);

        if (guidance_control_ != nullptr) {
            guidance_control_->StopNavigation(NavigationStopCode::kStopCodeDestination);
        } else {
            GUIDE_ASSERT(false);
        }
    }
}

void GuideRuntime::ProcessDebug(const VehiclePosition &ccp, const GuideData &guide_data) {
    if (!debug_manager_->IsDebugOn()) {
        return;
    }

    debug_manager_->WriteMatching(path_manager_->GetMainPathId(), ccp);

    if (guide_data.is_arrive_dest || !guide_data.voice_text.empty()) {
        debug_manager_->WriteGuideData(path_manager_->GetMainPathId(), guide_data);
    }

    if (guide_data.lane_info_state != nullptr) {
        debug_manager_->WriteLaneInfo(path_manager_->GetMainPathId(), ccp, guide_data.lane_info_state);
    }

    if (guide_data.junction_view_state != nullptr && guide_data.junction_view_state->ptr != nullptr) {
        debug_manager_->WriteJunctionView(path_manager_->GetMainPathId(), ccp, guide_data.junction_view_state);
    }

    if (guide_data.toll_station_state != nullptr && guide_data.toll_station_state->ptr != nullptr) {
        debug_manager_->WriteTollStation(path_manager_->GetMainPathId(), ccp, guide_data.toll_station_state);
    }

    if (guide_data.sign_post_state != nullptr && guide_data.sign_post_state->ptr != nullptr) {
        debug_manager_->WriteSignPost(path_manager_->GetMainPathId(), ccp, guide_data.sign_post_state);
    }
}

}  // namespace guide
}  // namespace aurora
/* EOF */
