#ifndef MAP_SRC_GUIDANCE_SRC_COMMOM_TIME_TRACKER_H
#define MAP_SRC_GUIDANCE_SRC_COMMOM_TIME_TRACKER_H

#include <cstdint>
#include <string>

#include "Time.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

class TimeTracker {
public:
    TimeTracker(const std::string &tag, uint32_t limit_ms)
    : limit_(limit_ms) 
    , tag_(tag) {
        start_ = Time::Now().ToMicrosecond();
    }

    ~TimeTracker() {
        uint64_t end = Time::Now().ToMicrosecond();

        uint64_t elipse = end - start_;
        if (elipse >= (limit_ * 1000)) {
            GUIDE_LOG_ERROR("[TimeTracker] {} consume time too much: {} ms", tag_.c_str(), elipse/1000);
        }
    }

protected:
    TimeTracker(const TimeTracker &) = delete;
    TimeTracker& operator= (const TimeTracker &) = delete;
    
private:
    uint32_t limit_;
    uint64_t start_;
    std::string tag_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDANCE_SRC_COMMOM_TIME_TRACKER_H
/* EOF */
