#ifndef MAP_SRC_GUIDE_SRC_COMMOM_GUIDE_DATA_H
#define MAP_SRC_GUIDE_SRC_COMMOM_GUIDE_DATA_H

#include <string>
#include <vector>
#include "guidance_def.h"
#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/guide/navigation/data/maneuver_toll_station_state.h"
#include "guidance/src/guide/navigation/data/maneuver_lane_info_state.h"
#include "guidance/src/guide/navigation/data/maneuver_junction_view_state.h"
#include "guidance/src/guide/navigation/data/maneuver_facility_state.h"
#include "guidance/src/guide/navigation/data/maneuver_sapa_state.h"
#include "guidance/src/guide/navigation/data/maneuver_sign_post_state.h"
#include "guidance/src/common/maneuver.h"

namespace aurora {
namespace guide {

struct GuideData {
    VehiclePosition pos;
    std::string voice_text;
    bool        is_arrive_dest;

    const LaneInfoState *lane_info_state;
    const JunctionViewState *junction_view_state;
    const TollStationState *toll_station_state;
    const FacilityState *facility_state;
    const SAPAState *sapa_state;
    const SignPostState *sign_post_state;

    // navigation board
    uint64_t        path_id;
    NavigationMode  mode;
    std::string     road_name;
    TimeAndDist     path_remain;
    std::vector<ManeuverPoint> next_maneuver_points;

    GuideData() {
        is_arrive_dest = false;
        path_id = PathID::kInvalidPathId;

        lane_info_state = nullptr;
        junction_view_state = nullptr;
        toll_station_state = nullptr;
        facility_state = nullptr;
        sapa_state = nullptr;
        sign_post_state = nullptr;
    }
};

}  // namespace guide
}  // namespace aurora
#endif
/* EOF */