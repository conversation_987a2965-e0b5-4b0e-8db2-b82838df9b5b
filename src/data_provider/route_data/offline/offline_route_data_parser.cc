#include "route_data/offline/offline_route_data_parser.h"

#include <memory.h>

#include "common/common_def.h"
#include "logger.h"

namespace aurora {
namespace parser {

const std::string ADMIN_DATA_FOLDER = "/admin/";
const std::string ADCODE_FILE_NAME = "admin_code_region.dat";
const double MD5_TRANSFORMER = 100000.0;
const int DEGREE_2_SECOND = 3600;

OfflineRouteDataParser::OfflineRouteDataParser()
    : current_file_(nullptr), current_adcode_(-1), is_path_mode_(false), path_() {}

OfflineRouteDataParser::~OfflineRouteDataParser() {
  std::lock_guard<std::mutex> lock(file_mtx_);
  if (current_file_ != nullptr) {
    ::fclose(current_file_);
  }
}

bool OfflineRouteDataParser::Init(const char* path) {
  Clear();
  path_ = path;
  path_ += ADMIN_DATA_FOLDER;
  return ReadAdCodeFile();
}

std::vector<uint32_t> OfflineRouteDataParser::GetAdCodeByMbr(LngLatMbr mbr) {
  std::vector<uint32_t> result;
  for (auto itr = route_index_cache_.begin(); itr != route_index_cache_.end(); ++itr) {
    if (itr->second != nullptr && mbr.Intersects(itr->second->GetMbr())) {
      result.push_back(itr->first);
    }
  }
  return result;
}

void OfflineRouteDataParser::GetRouteTileIDs(uint32_t code, uint8_t level, LngLatMbr mbr,
                                             std::vector<RouteTileID>& tiles) {
  std::map<uint32_t, RouteFileIndexPtr>::iterator itr = route_index_cache_.find(code);
  if (itr != route_index_cache_.end()) {
    const RouteLevelIndex* level_index = itr->second->GetLevelIndex(0);
    MeshIndexArrayPtr meshes = itr->second->GetMeshIndexArray(level);
    if (level_index != nullptr && meshes != nullptr) {
      int mesh_width = level_index->mesh_lng_width;
      int mesh_height = level_index->mesh_lat_width;
      int mesh_width_offset = level_index->mesh_lng_offset;
      int mesh_height_offset = level_index->mesh_lat_offset;
      int min_mesh_col =
          ((mbr.minx() - MIN_LNG) * DEGREE_2_SECOND - mesh_width_offset) / mesh_width;
      int max_mesh_col =
          ((mbr.maxx() - MIN_LNG) * DEGREE_2_SECOND + mesh_width_offset) / mesh_width;
      int min_mesh_row =
          ((mbr.miny() - MIN_LAT) * DEGREE_2_SECOND - mesh_height_offset) / mesh_height;
      int max_mesh_row =
          ((mbr.maxy() - MIN_LAT) * DEGREE_2_SECOND + mesh_height_offset) / mesh_height;
      if (min_mesh_col < 0) {
        min_mesh_col = 0;
      }
      if (min_mesh_row < 0) {
        min_mesh_row = 0;
      }

      for (uint16_t x = min_mesh_col; x <= max_mesh_col; ++x) {
        for (uint16_t y = min_mesh_row; y <= max_mesh_row; ++y) {
          MeshCode mesh_code(x, y);
          const std::vector<RouteTileIndex>* array_ptr =
              itr->second->GetRouteTileIndexArray(level, mesh_code);
          if (array_ptr != nullptr) {
            for (size_t i = 0; i < array_ptr->size(); ++i) {
              const RouteTileIndex& tile_index = array_ptr->at(i);
              LngLatMbr tile_mbr(
                  tile_index.min_x / MD5_TRANSFORMER, tile_index.min_y / MD5_TRANSFORMER,
                  tile_index.max_x / MD5_TRANSFORMER, tile_index.max_y / MD5_TRANSFORMER);
              if (mbr.Intersects(tile_mbr)) {
                RouteTileID tile_id(code, level, x, y, i);
                tiles.push_back(tile_id);
              }
            }
          }
        }
      }
    }
  }
}

bool OfflineRouteDataParser::GetTileData(RouteTileID tile_id, TilePositionInMesh& pos_in_mesh,
                                         LngLatMbr& mbr, DataBuffer& buf) {
  std::map<uint32_t, RouteFileIndexPtr>::iterator itr = route_index_cache_.find(tile_id.adcode);
  if (itr == route_index_cache_.end()) {
    return false;
  }
  RouteFileIndexPtr& ptr = itr->second;
  if (ptr == nullptr) {
    return false;
  }
  uint32_t file_buf_addr = 0;
  MeshCode mesh_code(tile_id.mesh_col, tile_id.mesh_row);
  const RouteTileIndex* tile_index_ptr =
      ptr->FindTileIndex(tile_id.level, mesh_code, tile_id.tile_id, file_buf_addr);
  if (tile_index_ptr == 0) {
    return false;
  }
  std::lock_guard<std::mutex> lock(file_mtx_);
  FILE* file_ptr = OpenFile(ptr->GetAdcode());
  if (file_ptr == nullptr) {
    return false;
  }
  if (::fseek(file_ptr, file_buf_addr, SEEK_SET) >= 0) {
    buf.resize(tile_index_ptr->tile_data_size, 0);
    ::fread(&(buf[0]), tile_index_ptr->tile_data_size, 1, file_ptr);
    pos_in_mesh.code = tile_index_ptr->pos_in_mesh.code;
    LngLatMbr tile_mbr(
        func::MD52LngLat(tile_index_ptr->min_x), func::MD52LngLat(tile_index_ptr->min_y),
        func::MD52LngLat(tile_index_ptr->max_x), func::MD52LngLat(tile_index_ptr->max_y));
    mbr = tile_mbr;
    return true;
  }
  int col = tile_id.mesh_col;
  int row = tile_id.mesh_row;
  LOG_INFO_TAG("OfflineRouteDataParser", "GetTileData failed,col:{}, row:{}, buf size:{}", col, row,
               tile_index_ptr->tile_data_size);

  return false;
}

void OfflineRouteDataParser::CacheAllMeshIndex() {
  for (auto itr = route_index_cache_.begin(); itr != route_index_cache_.end(); ++itr) {
    FILE* file_ptr = OpenFile(itr->first);
    if (file_ptr != nullptr) {
      RouteFileHeader file_header;
      if (ParseFileHead(file_ptr, file_header)) {
        ParseLevelIndex(itr->first, file_ptr, file_header);
      }
    }
  }
}
uint32_t OfflineRouteDataParser::GetDataVersion(uint32_t adcode) {
  uint32_t result = 0;
  std::map<uint32_t, RouteFileIndexPtr>::iterator itr = route_index_cache_.find(adcode);
  if (itr != route_index_cache_.end()) {
    RouteFileIndexPtr ptr = itr->second;
    if (ptr != nullptr) {
      result = ptr->GetFileHeader().data_version;
    }
  }
  return result;
}

void OfflineRouteDataParser::SetPathMode(bool is_path_mode) {
  is_path_mode_ = is_path_mode;
  if (!is_path_mode_) {
    std::lock_guard<std::mutex> lock(file_mtx_);
    for (auto itr = file_ptr_cache_.begin(); itr != file_ptr_cache_.end(); ++itr) {
      ::fclose(itr->second);
    }
    file_ptr_cache_.clear();
  }
}

void OfflineRouteDataParser::GetAdminInfo(AdministrativeInfoSet& infos) {
  for (auto itr = route_index_cache_.begin(); itr != route_index_cache_.end(); ++itr) {
    FILE* file_ptr = OpenFile(itr->first);
    if (file_ptr != nullptr) {
      RouteFileHeader file_header;
      if (ParseFileHead(file_ptr, file_header)) {
        uint16_t buf_size = file_header.admin_table_size;
        std::vector<uint8_t> buf(buf_size, 0);
        if (::fseek(file_ptr, file_header.admin_table_address, SEEK_SET) >= 0) {
          if (fread(&buf[0], buf_size, 1, file_ptr) > 0) {
            ParseAdminInfo(itr->first, buf, infos);
          }
        }
      }
    }
  }
  if (!infos.empty()) {
    std::sort(infos.begin(), infos.end());
  }
}

void OfflineRouteDataParser::Clear() {
  if (current_file_) {
    ::fclose(current_file_);
    current_file_ = nullptr;
  }
  SetPathMode(false);
  route_index_cache_.clear();
  region_index_.clear();
  current_adcode_ = -1;
}

bool OfflineRouteDataParser::ReadAdCodeFile() {
  std::string file_path = path_ + ADCODE_FILE_NAME;
  FILE* file_ptr = ::fopen(file_path.c_str(), "rb");
  if (file_ptr == nullptr) {
    return false;
  }
  if (::fseek(file_ptr, 0, SEEK_SET) >= 0) {
    AdCodeRegionFileHeader header;
    if (::fread(&(header.file_header_size), sizeof(uint16_t), 1, file_ptr) <= 0) {
      ::fclose(file_ptr);
      return false;
    }
    size_t read_size = sizeof(RouteFileHeader);
    if (read_size > header.file_header_size) {
      read_size = header.file_header_size;
    }
    read_size -= sizeof(uint16_t);
    if (::fread(&(header.region_index_size), read_size, 1, file_ptr) <= 0) {
      ::fclose(file_ptr);
      return false;
    }
    std::vector<RegionIndex> index;
    index.resize(header.region_index_count);
    if (::fread(&(index[0]), header.region_index_count * header.region_index_size, 1, file_ptr)) {
      for (size_t i = 0; i < index.size(); ++i) {
        MeshCode code(index[i].mesh_x, index[i].mesh_y);
        auto itr = region_index_.find(code.code);
        if (itr == region_index_.end()) {
          std::vector<uint32_t> adcodes = {index[i].adcode};
          std::pair<uint32_t, std::vector<uint32_t>> item = std::make_pair(code.code, adcodes);
          region_index_.insert(item);
        } else {
          itr->second.push_back(index[i].adcode);
        }
        auto it = route_index_cache_.find(index[i].adcode);
        if (it == route_index_cache_.end()) {
          route_index_cache_.insert(std::make_pair(index[i].adcode, nullptr));
        }
      }
      ::fclose(file_ptr);
      return true;
    }
  }
  ::fclose(file_ptr);
  return false;
}

bool OfflineRouteDataParser::ParseFileHead(FILE* file_ptr, RouteFileHeader& file_header) {
  if (file_ptr == nullptr) {
    return false;
  }
  if (::fseek(file_ptr, 0, SEEK_SET) >= 0) {
    if (::fread(&(file_header.file_header_size), sizeof(uint16_t), 1, file_ptr) <= 0) {
      return false;
    }
    size_t read_size = sizeof(RouteFileHeader);
    if (read_size > file_header.file_header_size) {
      read_size = file_header.file_header_size;
    }
    read_size -= sizeof(uint16_t);
    if (::fread(&(file_header.level_index_size), read_size, 1, file_ptr) > 0) {
      return true;
    }
  }
  return false;
}

bool OfflineRouteDataParser::ParseLevelIndex(uint32_t code, FILE* file_ptr,
                                             const RouteFileHeader& file_header) {
  if (file_ptr == nullptr) {
    return false;
  }
  if (file_header.real_leavel_count <= 0) {
    return false;
  }
  std::vector<RouteLevelIndex> level_index;
  {
    RouteLevelIndex default_index;
    level_index.resize(file_header.real_leavel_count, default_index);
  }
  size_t copy_size = sizeof(RouteLevelIndex);
  if (::fseek(file_ptr, file_header.file_header_size, SEEK_SET) >= 0) {
    if (file_header.level_index_size == copy_size) {
      int8_t* dst = (int8_t*)(&level_index[0]);
      ::fread(dst, copy_size * file_header.real_leavel_count, 1, file_ptr);
    } else {
      if (copy_size > file_header.level_index_size) {
        copy_size = file_header.level_index_size;
      }
      for (uint8_t i = 0; i < file_header.real_leavel_count; ++i) {
        if (::fseek(file_ptr, file_header.file_header_size + i * file_header.level_index_size,
                    SEEK_SET) >= 0) {
          int8_t* dst = (int8_t*)(&level_index[i]);
          ::fread(dst, copy_size, 1, file_ptr);
        }
      }
    }
  }
  RouteFileIndexPtr cache_ptr = std::make_shared<RouteFileIndex>(code, file_ptr, file_header);
  if (cache_ptr->SetLevelIndex(&(level_index[0]), file_header.real_leavel_count)) {
    std::map<uint32_t, RouteFileIndexPtr>::iterator itr = route_index_cache_.find(code);
    if (itr != route_index_cache_.end()) {
      itr->second = cache_ptr;
    } else {
      return false;
    }
    DataBuffer level_data_buf;
    for (uint8_t i = 0; i < file_header.real_leavel_count; ++i) {
      RouteLevelIndex& index = level_index[i];
      if (index.level_data_size > 0) {
        level_data_buf.resize(index.level_data_size, 0);
        if (::fseek(file_ptr, index.level_data_address, SEEK_SET) >= 0) {
          ::fread(&level_data_buf[0], index.level_data_size, 1, file_ptr);
          ParseMeshIndex(i, index, &level_data_buf[0], cache_ptr);
        }
      }
    }
    return true;
  }
  return false;
}

bool OfflineRouteDataParser::ParseMeshIndex(uint8_t level, const RouteLevelIndex& level_index,
                                            const int8_t* buf, RouteFileIndexPtr cache) {
  MeshIndexArrayPtr mesh_index = std::make_shared<MeshIndexArray>();
  mesh_index->resize(level_index.mesh_count);
  if (level_index.mesh_index_size == sizeof(RouteMeshIndex)) {
    int8_t* dst = (int8_t*)&(mesh_index->at(0));
    ::memcpy(dst, buf, level_index.mesh_index_size * level_index.mesh_count);
  } else {
    size_t copy_size = sizeof(RouteMeshIndex);
    ::memset(&(mesh_index->at(0)), 0, copy_size * level_index.mesh_count);
    if (copy_size > level_index.mesh_index_size) {
      copy_size = level_index.mesh_index_size;
    }

    for (uint8_t j = 0; j < level_index.mesh_count; ++j) {
      int8_t* dst = (int8_t*)&(mesh_index->at(j));
      const int8_t* src = buf + j * level_index.mesh_index_size;
      ::memcpy(dst, src, copy_size);
    }
  }

  bool result = false;
  for (uint32_t i = 0; i < level_index.mesh_count; ++i) {
    MeshCacheItemPtr ptr = std::make_shared<MeshCacheItem>();
    ptr->level = level;
    ptr->mesh_index_id = i;
    RouteMeshHeader* mesh_header_ptr = (RouteMeshHeader*)(buf + mesh_index->at(i).mesh_data_adress);
    ptr->tile_index_array.resize(mesh_header_ptr->tile_count);
    if (mesh_header_ptr->tile_index_size == sizeof(RouteTileIndex)) {
      int8_t* dst = (int8_t*)&(ptr->tile_index_array[0]);
      const int8_t* src =
          buf + mesh_index->at(i).mesh_data_adress + mesh_header_ptr->mesh_header_size;
      ::memcpy(dst, src, mesh_header_ptr->tile_count * mesh_header_ptr->tile_index_size);
    } else {
      size_t copy_size = sizeof(RouteTileIndex);
      ::memset(&(ptr->tile_index_array[0]), 0, copy_size * mesh_header_ptr->tile_count);
      if (copy_size > mesh_header_ptr->tile_index_size) {
        copy_size = mesh_header_ptr->tile_index_size;
      }
      for (uint8_t j = 0; j < mesh_header_ptr->tile_count; ++j) {
        int8_t* dst = (int8_t*)&(ptr->tile_index_array[j]);
        const int8_t* src = buf + mesh_index->at(i).mesh_data_adress +
                            mesh_header_ptr->mesh_header_size +
                            j * mesh_header_ptr->tile_index_size;
        ::memcpy(dst, src, copy_size);
      }
    }

    MeshCode mesh_code(mesh_index->at(i).mesh_col, mesh_index->at(i).mesh_row);
    cache->SetMeshIndex(level, mesh_index);
    result |= cache->AddMeshCache(level, mesh_code, ptr);
  }
  return result;
}

FILE* OfflineRouteDataParser::OpenFile(uint32_t adcode) {
  if (current_adcode_ != adcode) {
    if (is_path_mode_) {
      if (current_file_ != nullptr) {
        if (file_ptr_cache_.find(current_adcode_) == file_ptr_cache_.end()) {
          file_ptr_cache_.emplace(current_adcode_, current_file_);
        }
      }
      auto itr = file_ptr_cache_.find(adcode);
      if (itr != file_ptr_cache_.end()) {
        current_file_ = itr->second;
        file_ptr_cache_.erase(itr);
        if (current_file_ != nullptr) {
          current_adcode_ = adcode;
          return current_file_;
        }
      }
    } else {
      if (current_file_ != nullptr) {
        ::fclose(current_file_);
        current_file_ = nullptr;
        current_adcode_ = 0;
      }
    }
    std::string file_name = std::to_string(adcode);
    std::string file_path = path_ + file_name.c_str() + ".dat";
    current_file_ = ::fopen(file_path.c_str(), "rb");
    if (current_file_ != nullptr) {
      current_adcode_ = adcode;
    }
  }
  return current_file_;
}

void OfflineRouteDataParser::ParseAdminInfo(int32_t adcode, std::vector<uint8_t>& buf,
                                            AdministrativeInfoSet& infos) {
  uint32_t buf_index = 0;
  while (buf_index < buf.size()) {
    AdministrativeInfo info;
    info.adcode = adcode;

    int32_t* lng = (int32_t*)(&buf[buf_index]);
    buf_index += sizeof(int32_t);
    int32_t* lat = (int32_t*)(&buf[buf_index]);
    buf_index += sizeof(int32_t);
    info.center.Set((*lng) / 100000.0, (*lat) / 100000.0);

    uint8_t native_city_name_length = buf[buf_index];
    buf_index += 1;
    uint8_t foreign_city_name_length = buf[buf_index];
    buf_index += 1;
    uint8_t native_province_name_length = buf[buf_index];
    buf_index += 1;
    uint8_t foreign_province_name_length = buf[buf_index];
    buf_index += 1;
    if (foreign_city_name_length > 0) {
      buf_index += native_city_name_length;
      std::string name((char*)&(buf[buf_index]), foreign_city_name_length);
      buf_index += foreign_city_name_length;
      info.city_name = name;
    } else if (native_city_name_length > 0) {
      std::string name((char*)&(buf[buf_index]), native_city_name_length);
      buf_index += native_city_name_length;
      buf_index += foreign_city_name_length;
      info.city_name = name;
    } else {
      LOG_ERROR("OfflineRouteDataParser::ParseAdminInfo: {} no city name", adcode);
    }

    if (foreign_province_name_length > 0) {
      buf_index += native_province_name_length;
      std::string name((char*)&(buf[buf_index]), foreign_province_name_length);
      buf_index += foreign_province_name_length;
      info.province_name = name;

    } else if (native_province_name_length > 0) {
      std::string name((char*)&(buf[buf_index]), native_province_name_length);
      buf_index += native_province_name_length;
      buf_index += foreign_province_name_length;
      info.province_name = name;
    } else {
      LOG_ERROR("OfflineRouteDataParser::ParseAdminInfo: {} no province name", adcode);
    }

    assert(buf_index <= buf.size());
    infos.push_back(info);
  }
}
}  // namespace parser
}  // namespace aurora
