#include "route_data/feature/signpost_info.h"

namespace aurora {
namespace parser {
SignpostInfo::SignpostInfo() : base_(nullptr) {}
SignpostInfo::SignpostInfo(const SignpostBase* base) : base_(base) {}

uint8_t SignpostInfo::GetSignInfoNameCount() { return infos_.size(); }
const char* SignpostInfo::GetSignInfoName(uint8_t index) {
  if (index >= infos_.size()) {
    return nullptr;
  }
  return &infos_[index][0];
}
}  // namespace parser
}  // namespace aurora
