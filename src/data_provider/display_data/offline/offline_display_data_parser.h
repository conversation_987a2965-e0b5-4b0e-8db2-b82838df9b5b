#ifndef MAP_SRC_DATA_PROVIDER_DISPLAY_DATA_OFFLINE_OFFLINE_DISPLAY_DATA_PARSER_H
#define MAP_SRC_DATA_PROVIDER_DISPLAY_DATA_OFFLINE_OFFLINE_DISPLAY_DATA_PARSER_H

#include <mutex>
#include <string>

#include "display_data/display_data_def.h"
#include "display_data/display_data_tile_def.h"
#include "display_data/offline/display_data_file_def.h"
#include "display_data/offline/tile_index_cache.h"

namespace aurora {
namespace parser {
class OfflineDisplayDataParser {
public:
  OfflineDisplayDataParser();
  ~OfflineDisplayDataParser();
  bool Init(const char* path);
  bool GetTileData(DisplayTileID tile_id, uint16_t& pixel_num, DataBuffer& buf);
//  bool ReadAllTileBufFromFile(const char* file_path, uint16_t& pixel_num);

private:
  bool ReadAdCodeFile();
  void FindAdCodesByTileID(DisplayTileID tile_id, std::vector<uint32_t>& adcodes);
  bool FindTile(DisplayTileID tile, TileIndexCacheValue tile_index, uint16_t& pixel_num,
                DataBuffer& buf);
  void ReadTileIndexFromFile(uint32_t adcode, const char* file_path);
  bool ParseFileHead(FILE* file_ptr, DisplayFileHeader& file_header);
  TileIndexBufCachePtr ParseIndex(FILE* file_ptr, DisplayFileHeader& header, uint16_t& pixel_num);
  bool ReadTileBuf(FILE* file_ptr, std::vector<DisplayTileIndex>& cache, DisplayTileID tile_id,
                   DataBuffer& buf);
  void UseTileIndex(uint32_t adcode, TileIndexCacheValue index_cache);

  std::string data_path_;
  TileIndexCache cache_;
  int32_t adcode_;
  TileIndexCacheValue current_tile_index_;

  typedef std::map<uint8_t, std::vector<FileBound>> AdCodeCache;
  typedef std::pair<uint8_t, std::vector<FileBound>> AdCodeCacheItem;
  AdCodeCache adcode_cache_;
  std::mutex file_mtx_;
};
}  // namespace parser
}  // namespace aurora

#endif  // MAP_SRC_DATA_PROVIDER_DISPLAY_DATA_OFFLINE_OFFLINE_DISPLAY_DATA_PARSER_H
