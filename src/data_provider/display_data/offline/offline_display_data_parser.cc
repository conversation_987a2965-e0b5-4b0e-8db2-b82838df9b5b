#include "display_data/offline/offline_display_data_parser.h"

#include <fstream>
#include <vector>

#include "common/common_def.h"
#include "display_data/display_data_tile_def.h"
#include "logger.h"

namespace aurora {
namespace parser {

const std::string ADMIN_DATA_FOLDER = "/admin/";
const std::string ADCODE_FILE_NAME = "admin_code_region.dat";
const uint32_t FILE_CACHE_LIMIT = 9;
enum AdcodeFileItem {
  kAdCode = 0,
  kPhysicalLevel,
  kTileCount,
  kMinTileCol,
  kMinTileRow,
  kMaxTileCol,
  kMaxTileRow,
  kPolygonFullNum,
  kAdcodeFileItemCount,
};

OfflineDisplayDataParser::OfflineDisplayDataParser()
    : data_path_(), cache_(FILE_CACHE_LIMIT), adcode_(-1), current_tile_index_(), adcode_cache_() {}

OfflineDisplayDataParser::~OfflineDisplayDataParser() { current_tile_index_.Clear(); }

bool OfflineDisplayDataParser::Init(const char* path) {
  std::lock_guard<std::mutex> lock(file_mtx_);
  data_path_ = path;
  data_path_ += ADMIN_DATA_FOLDER.c_str();
  adcode_ = -1;
  current_tile_index_.Clear();
  cache_.Clear();
  return ReadAdCodeFile();
}

bool OfflineDisplayDataParser::GetTileData(DisplayTileID tile_id, uint16_t& pixel_num,
                                           DataBuffer& buf) {
  std::lock_guard<std::mutex> lock(file_mtx_);
  if (FindTile(tile_id, current_tile_index_, pixel_num, buf)) {
    return true;
  }

  TileIndexCacheValue tile_index;
  std::vector<uint32_t> adcodes;
  FindAdCodesByTileID(tile_id, adcodes);
  for (size_t i = 0; i < adcodes.size(); ++i) {
    if (cache_.TakeOut(adcodes[i], tile_index)) {
      UseTileIndex(adcodes[i], tile_index);
      if (FindTile(tile_id, current_tile_index_, pixel_num, buf)) {
        return true;
      }
    }
  }
  for (size_t i = 0; i < adcodes.size(); ++i) {
    if (adcodes[i] == adcode_ || cache_.Contains(adcodes[i])) {
      continue;
    }
    std::string file_name = std::to_string(adcodes[i]) + ".dat";
    if (adcodes[i] == 0) {
      file_name = "000000.dat";
    }
    std::string file_path = data_path_ + file_name;
    ReadTileIndexFromFile(adcodes[i], file_path.c_str());
    if (FindTile(tile_id, current_tile_index_, pixel_num, buf)) {
      return true;
    }
  }
  return false;
}
#if 0
bool OfflineDisplayDataParser::ReadAllTileBufFromFile(const char* file_path, uint16_t& pixel_num) {
  FILE* file_ptr = ::fopen(file_path, "rb");
  if (file_ptr == nullptr) {
    return false;
  }
  bool result = false;
  DisplayFileHeader file_header;
  if (ParseFileHead(file_ptr, file_header)) {
    if (file_header.level_index_size > 0 && file_header.real_leavel_count > 0) {
      TileIndexBufCachePtr ptr = ParseIndex(file_ptr, file_header, pixel_num);
      if (ptr) {
        for (auto itr = ptr->begin(); itr != ptr->end(); ++itr) {
          DisplayTileID tile;
          tile.level = itr->first;
          for (size_t i = 0; i < itr->second.size(); ++i) {
            DataBuffer buf;
            DisplayTileIndex& index = itr->second[i];
            tile.col = index.tile_x;
            tile.row = index.tile_y;
            if (index.tile_size == 0) {
              buf.resize(4);
              uint32_t* feature_type_ptr = (uint32_t*)(&buf[0]);
              *feature_type_ptr = index.tile_address;
            } else {
              if (::fseek(file_ptr, index.tile_address, SEEK_SET) >= 0) {
                buf.resize(index.tile_size);
                ::fread(&(buf[0]), index.tile_size, 1, file_ptr);
              }
            }
            result = true;
          }
        }
      }
    }
  }
  ::fclose(file_ptr);
  return result;
}
#endif
bool OfflineDisplayDataParser::ReadAdCodeFile() {
  std::string file_path = data_path_ + ADCODE_FILE_NAME;
  FILE* file_ptr = ::fopen(file_path.c_str(), "r");
  if (file_ptr != nullptr) {
    char* line_string = nullptr;
    size_t size = 0;
    if (::getline(&line_string, &size, file_ptr) < 0) {
      ::fclose(file_ptr);
      return false;
    }
    while (::getline(&line_string, &size, file_ptr) >= 0) {
      std::vector<uint32_t> num(8, 0);
      std::string file_name = "";
      char* token = nullptr;
      std::string delim = ",";
      size_t count = 0;
      token = ::strtok(line_string, delim.c_str());
      size_t i = 0;
      while (token != nullptr) {
        if (i == 0) {
          file_name = token;
        }
        num[i] = ::atoi(token);
        ++i;
        token = ::strtok(nullptr, delim.c_str());
      }
      FileBound file_mbr(num[kMinTileCol], num[kMinTileRow], num[kMaxTileCol], num[kMaxTileRow],
                         num[kAdCode]);
      AdCodeCache::iterator itr = adcode_cache_.find(num[kPhysicalLevel]);
      if (itr == adcode_cache_.end()) {
        std::vector<FileBound> mbr_array;
        AdCodeCacheItem item = std::make_pair(num[kPhysicalLevel], mbr_array);
        adcode_cache_.insert(item);
        itr = adcode_cache_.find(num[kPhysicalLevel]);
      }

      if (itr != adcode_cache_.end()) {
        itr->second.push_back(file_mbr);
      }
    }
    ::fclose(file_ptr);
    return !adcode_cache_.empty();
  }
  LOG_INFO_TAG("OfflineDisplayDataParser", "open file failed! no admin_code_region.dat");
  return false;
}

void OfflineDisplayDataParser::FindAdCodesByTileID(DisplayTileID tile_id,
                                                   std::vector<uint32_t>& adcodes) {
  uint8_t level = LOGIC_PHYSICAL_LEVEL_MAP[tile_id.level];
  AdCodeCache::iterator itr = adcode_cache_.find(level);
  if (itr != adcode_cache_.end()) {
    std::vector<FileBound>& file_mbrs = itr->second;
    for (size_t i = 0; i < file_mbrs.size(); ++i) {
      if (tile_id.col >= file_mbrs[i].start_x && tile_id.col < file_mbrs[i].end_x &&
          tile_id.row >= file_mbrs[i].start_y && tile_id.row < file_mbrs[i].end_y) {
        adcodes.push_back(file_mbrs[i].adcode);
      }
    }
  }
}

bool OfflineDisplayDataParser::FindTile(DisplayTileID tile, TileIndexCacheValue tile_index,
                                        uint16_t& pixel_num, DataBuffer& buf) {
  if (tile_index.buf_ptr != nullptr && tile_index.file_ptr != nullptr) {
    for (std::vector<TileIndexBufCacheItem>::iterator itr = tile_index.buf_ptr->begin();
         itr != tile_index.buf_ptr->end(); ++itr) {
      if (itr->first == tile.level) {
        if (ReadTileBuf(tile_index.file_ptr, itr->second, tile, buf)) {
          pixel_num = tile_index.pixel_num;
          return true;
        }
      }
    }
  }
  return false;
}

void OfflineDisplayDataParser::ReadTileIndexFromFile(uint32_t adcode, const char* file_path) {
  FILE* file_ptr = ::fopen(file_path, "rb");
  if (file_ptr == nullptr) {
    return;
  }
  DisplayFileHeader file_header;
  if (ParseFileHead(file_ptr, file_header)) {
    if (file_header.level_index_size > 0 && file_header.real_leavel_count > 0) {
      uint16_t pixel_num = 0;
      TileIndexBufCachePtr ptr = ParseIndex(file_ptr, file_header, pixel_num);
      if (ptr) {
        TileIndexCacheValue tile_index = {pixel_num, file_ptr, ptr};
        UseTileIndex(adcode, tile_index);
        return;
      }
    }
  }
  ::fclose(file_ptr);
}

bool OfflineDisplayDataParser::ParseFileHead(FILE* file_ptr, DisplayFileHeader& file_header) {
  if (::fseek(file_ptr, 0, SEEK_SET) >= 0) {
    if (::fread(&(file_header.file_header_size), sizeof(uint16_t), 1, file_ptr) <= 0) {
      return false;
    }
    size_t read_size = sizeof(DisplayFileHeader);
    if (read_size > file_header.file_header_size) {
      read_size = file_header.file_header_size;
    }
    if (read_size < sizeof(uint16_t)) {
      return false;
    }
    read_size -= sizeof(uint16_t);
    if (::fread(&(file_header.level_index_size), read_size, 1, file_ptr) > 0) {
      return true;
    }
  }
  return false;
}

TileIndexBufCachePtr OfflineDisplayDataParser::ParseIndex(FILE* file_ptr, DisplayFileHeader& header,
                                                          uint16_t& pixel_num) {
  if (::fseek(file_ptr, header.file_header_size, SEEK_SET) < 0) {
    return nullptr;
  }
  if (sizeof(DisplayLevelIndex) > header.level_index_size) {
    return nullptr;
  }
  DataBuffer index_header_buf;
  size_t read_size = header.level_index_size;
  read_size *= header.real_leavel_count;
  if (read_size <= 0) {
    return nullptr;
  }
  index_header_buf.resize(read_size);
  if (::fread(&(index_header_buf[0]), read_size, 1, file_ptr) > 0) {
    TileIndexBufCachePtr ptr = std::make_shared<TileIndexBufCache>();
    ptr->resize(header.real_leavel_count);

    for (int i = 0; i < header.real_leavel_count; ++i) {
      DisplayLevelIndex* index_header_ptr =
          (DisplayLevelIndex*)(index_header_buf.data() + i * header.level_index_size);
      pixel_num = index_header_ptr->pixel_num;
      if (::fseek(file_ptr, index_header_ptr->level_data_address, SEEK_SET) >= 0) {
        if (sizeof(DisplayTileIndex) <= index_header_ptr->tile_index_size) {
          TileIndexBufCacheItem& cache_item = (*ptr)[i];
          cache_item.first = index_header_ptr->logic_level;
          size_t read_size = index_header_ptr->tile_index_size * index_header_ptr->tile_count;
          DataBuffer tile_index_buf;
          tile_index_buf.resize(read_size);
          int8_t* buf_ptr = &(tile_index_buf[0]);
          if (::fread(buf_ptr, read_size, 1, file_ptr) > 0) {
            cache_item.second.resize(index_header_ptr->tile_count);
            if (sizeof(DisplayTileIndex) < index_header_ptr->tile_index_size) {
              for (size_t i = 0; i < index_header_ptr->tile_count; ++i) {
                DisplayTileIndex* tile_index_ptr =
                    (DisplayTileIndex*)(buf_ptr + i * index_header_ptr->tile_index_size);
                ::memcpy(&(cache_item.second[i]), tile_index_ptr, sizeof(DisplayTileIndex));
              }
            } else {
              ::memcpy(&(cache_item.second[0]), buf_ptr, read_size);
            }
          }
        }
      }
    }
    return ptr;
  }
  return nullptr;
}

bool OfflineDisplayDataParser::ReadTileBuf(FILE* file_ptr, std::vector<DisplayTileIndex>& cache,
                                           DisplayTileID tile_id, DataBuffer& buf) {
  for (size_t i = 0; i < cache.size(); ++i) {
    DisplayTileIndex& index = cache[i];
    if (tile_id.col == index.tile_x && tile_id.row == index.tile_y) {
      if (index.tile_size == 0) {
        buf.resize(4);
        uint32_t* feature_type_ptr = (uint32_t*)(&buf[0]);
        *feature_type_ptr = index.tile_address;
        return true;
      } else {
        if (::fseek(file_ptr, index.tile_address, SEEK_SET) >= 0) {
          buf.resize(index.tile_size);
          if (::fread(&(buf[0]), index.tile_size, 1, file_ptr) > 0) {
            return true;
          }
        }
      }
    }
  }
  return false;
}

void OfflineDisplayDataParser::UseTileIndex(uint32_t adcode, TileIndexCacheValue index_cache) {
  if (index_cache.file_ptr == current_tile_index_.file_ptr) {
    return;
  }
  if (current_tile_index_.buf_ptr != nullptr && !current_tile_index_.buf_ptr->empty()) {
    if (current_tile_index_.file_ptr != nullptr) {
      cache_.Add(adcode_, current_tile_index_);
    }
  } else {
    if (current_tile_index_.file_ptr != nullptr) {
      ::fclose(current_tile_index_.file_ptr);
    }
  }
  adcode_ = adcode;
  current_tile_index_ = index_cache;
}
}  // namespace parser
}  // namespace aurora
