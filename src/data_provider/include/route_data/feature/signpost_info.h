#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_SIGNPOST_INFO_H
#define MAP_SRC_DATA_PROVIDER_INCLUDE_SIGNPOST_INFO_H

#include "export_type_def.h"
#include "route_data/route_data_def.h"

namespace aurora {
namespace parser {

#pragma pack(push, 1)
struct SignpostBase {
  uint32_t in_edge_id : 23;
  uint32_t in_edge_dir : 1;
  uint32_t is_same_mesh : 1;
  uint32_t is_same_tile : 1;
  uint32_t type : 1;
  uint32_t not_used : 5;

  uint16_t out_mesh_col;
  uint16_t out_mesh_row;
  uint32_t out_tile_id : 8;
  uint32_t out_edge_dir : 1;
  uint32_t out_edge_id : 23;

  uint16_t info_offset[3];
  SignpostBase() { ::memset(this, 0, sizeof(*this)); }
};
#pragma pack(pop)
typedef std::vector<char> SignInfoName;
class SignpostInfo {
public:
  SignpostInfo();
  SignpostInfo(const SignpostBase* base);
  const SignpostBase* GetBaseInfo() { return base_; }
  uint8_t GetSignInfoNameCount();
  const char* GetSignInfoName(uint8_t index);

  bool operator<(const SignpostInfo& other) const {
    if (base_ != nullptr && other.base_ != nullptr) {
      if (base_->in_edge_id != other.base_->in_edge_id) {
        return base_->in_edge_id < other.base_->in_edge_id;
      } else if (base_->in_edge_dir != other.base_->in_edge_dir) {
        return base_->in_edge_dir < other.base_->in_edge_dir;
      } else if (base_->out_mesh_col != other.base_->out_mesh_col) {
        return base_->out_mesh_col < other.base_->out_mesh_col;
      } else if (base_->out_mesh_row != other.base_->out_mesh_row) {
        return base_->out_mesh_row < other.base_->out_mesh_row;
      } else if (base_->out_tile_id != other.base_->out_tile_id) {
        return base_->out_tile_id < other.base_->out_tile_id;
      } else if (base_->out_edge_id != other.base_->out_edge_id) {
        return base_->out_edge_id < other.base_->out_edge_id;
      } else if (base_->out_edge_dir != other.base_->out_edge_dir) {
        return base_->out_edge_dir < other.base_->out_edge_dir;
      } else {
      }
    }
    return this < &other;
  }
  bool operator==(const SignpostInfo& other) const {
    if (base_ != nullptr && other.base_ != nullptr) {
      return base_->in_edge_id == other.base_->in_edge_id &&
             base_->in_edge_dir == other.base_->in_edge_dir &&
             base_->out_mesh_col == other.base_->out_mesh_col &&
             base_->out_mesh_row == other.base_->out_mesh_row &&
             base_->out_tile_id == other.base_->out_tile_id &&
             base_->out_edge_id == other.base_->out_edge_id &&
             base_->out_edge_dir == other.base_->out_edge_dir;
    }
    return this == &other;
  }

private:
  uint32_t id_;
  const SignpostBase* base_;
  std::vector<SignInfoName> infos_;
  std::vector<SignInfoName> foreign_infos_;
  // Friend declaration for access to private members
  friend class RouteTileParser;
};
typedef std::vector<SignpostInfo> SignpostInfoSet;
}  // namespace parser
}  // namespace aurora

#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_SIGNPOST_INFO_H
