#ifndef MAP_SRC_DATA_PROVIDER_ROUTE_DATA_COMMON_DEF_H
#define MAP_SRC_DATA_PROVIDER_ROUTE_DATA_COMMON_DEF_H

#include <math.h>

#include <cstdint>
#include <vector>

#include "point2.h"

namespace aurora {
namespace parser {

const double MD5_FACTOR = 100000.0;
const double MIN_LNG = -180.0;
const double MIN_LAT = -90.0;
const double MAX_LNG = 180.0;
const double MAX_LAT = 90.0;
const double TOP_LEVEL_WIDTH = 180;
const double TOP_LEVEL_HEIGHT = 180;
const double PI = 3.1415926535897932;
static uint8_t LEVEL_COUNT = 20;
static uint8_t LEVEL_MAP[] = {2,  2,  2,  4,  4,  6,  6,  8,  8,  10,
                              10, 12, 12, 12, 15, 15, 15, 15, 15, 15};
static uint8_t LOGIC_PHYSICAL_LEVEL_MAP[] = {0, 0, 0, 1, 1, 2, 2, 3, 3, 4,
                                             4, 5, 5, 5, 6, 6, 6, 6, 6, 6};
static uint8_t GLOBAL_LEVEL = 0;
static uint8_t WIDE_AREA_START_LEVEL = 1;
static uint8_t ADMIN_DATA_MAX_LEVEL = 17;
static uint8_t MAX_LEVEL_OF_DATA_LEVEL[] = {2,  2,  2,  4,  4,  6,  6,  8,  8,  10,
                                            10, 13, 13, 13, 17, 17, 17, 17, 19, 19};

typedef std::vector<int8_t> DataBuffer;
typedef std::vector<int16_t> GeoBuffer;

namespace func {
template <typename InType, typename OutType>
void AlignGeoBuf(const InType* ori_buf, uint32_t count, std::vector<OutType>& buf) {
  buf.resize(count, 0);
  uint32_t geo_offset = count & 0x07 ? ((count >> 3) + 1) : (count >> 3);
  uint8_t mask = 0x01;
  int8_t* cur_point = (int8_t*)&(ori_buf[geo_offset]);
  for (uint32_t i = 0; i < count; ++i) {
    if (ori_buf[i / 8] & mask) {
      buf[i] = *((int16_t*)cur_point);
      cur_point += 2;
    } else {
      buf[i] = *cur_point;
      cur_point += 1;
    }
    mask = (mask == 0x80) ? 0x01 : mask << 1;
  }
}
static int32_t LngLat2MD5(double lnglat) { return lnglat * MD5_FACTOR; }
static double MD52LngLat(int32_t md5) { return md5 / MD5_FACTOR; }

static PointXY<double> TileColRow2LngLat(uint8_t logic_level, int32_t col, int32_t row) {
  PointXY<double> lnglat(col * TOP_LEVEL_WIDTH / ::pow(2.0, logic_level - 1) + MIN_LNG,
                         row * TOP_LEVEL_WIDTH / ::pow(2.0, logic_level - 1) + MIN_LAT);
  return lnglat;
}

static void LngLat2TileColRow(uint8_t logic_level, double lng, double lat, int32_t& col,
                              int32_t& row) {
  col = (lng - MIN_LNG) * ::pow(2.0, logic_level - 1) / TOP_LEVEL_WIDTH;
  row = (lat - MIN_LAT) * ::pow(2.0, logic_level - 1) / TOP_LEVEL_HEIGHT;
}

static PointXY<double> LngLat2Mercator(double lng, double lat) {
  double x = lng * 20037508.342789 / 180;
  double y = (((90 + lat) * PI / 360)) / (PI / 180);
  y *= 20037508.34789 / 180;
  PointXY<double> mercator(x, y);
  return mercator;
}

static PointXY<double> Mercator2LngLat(double x, double y) {
  double lng = x / 20037508.34 * 180;
  double lat = y / 20037508.34 * 180;
  lat = 180 / PI * (2 * ((lat * PI / 180)) - PI / 2);
  PointXY<double> lnglat(lng, lat);
  return lnglat;
}
}  // namespace func
}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_ROUTE_DATA_COMMON_DEF_H
