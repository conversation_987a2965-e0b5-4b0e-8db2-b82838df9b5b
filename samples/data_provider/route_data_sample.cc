#include "route_data_sample.h"

#include <fstream>
#include <iomanip>
#include <iostream>

#include "route_data/route_data_def.h"

namespace aurora {
namespace parser {
RouteDataSample::RouteDataSample() {}

void RouteDataSample::RunSample(const char* prefix) {
  prefix_ = prefix;
  DataProvider provider;
  std::string path = prefix_ + "map_engine/distribution/data/route";
  provider.InitRouteParser(path.c_str());
  {
    //    double lon = 121.31604686379432;
    //    double lat = 31.189158148023324;
    //    double lon = 121.40006;
    //    double lat = 31.1717;
    double lon = 121.279628;
    double lat = 31.178582;
    aurora::parser::GeoMbr mbr(lon - 0.001, lat - 0.001, lon + 0.001, lat + 0.001);
    std::vector<RouteTileID> level0_tile_ids;
    provider.GetRouteTileIDsByMBR(0, mbr, level0_tile_ids);
    PrintTiles(provider, level0_tile_ids);
    std::vector<RouteTileID> level1_tile_ids;
    provider.GetRouteTileIDsByMBR(1, mbr, level1_tile_ids);
    PrintTiles(provider, level1_tile_ids);
    std::vector<RouteTileID> level2_tile_ids;
    provider.GetRouteTileIDsByMBR(2, mbr, level2_tile_ids);
    PrintTiles(provider, level2_tile_ids);
  }
}

void RouteDataSample::PrintTiles(DataProvider& provider, std::vector<RouteTileID>& tile_ids) {
  std::cout << "  tile_ids size: " << tile_ids.size() << std::endl;
  RouteTileReader reader;
  for (size_t i = 0; i < tile_ids.size(); ++i) {
    RouteTilePackagePtr tile_ptr = provider.GetRouteTileByID(tile_ids.at(i));
    reader.SetTarget(tile_ptr);

    AugmentEdgeSet& edges = reader.GetAugmentEdges();
    PrintEdges(reader, edges);

    RouteNodeSet& nodes = reader.GetNodes();
    PrintNodes(reader, nodes);

    LimitPassSet& limit_passes = reader.GetLimitPass();
    PrintLimitPass(reader, limit_passes);

    LaneInfoSet& lanes = reader.GetLaneInfo();
    PrintLanes(reader, lanes);

    FacilityInfoSet& facilities = reader.GetFacilityInfo();
    PrintFacilities(reader, facilities);

    JuncviewInfoSet& juncviews = reader.GetJuncviewInfo();
    PrintJuncview(reader, juncviews);

    SignpostInfoSet& signposts = reader.GetSignpostInfo();
    PrintSignposts(reader, signposts);

    TollgateInfoSet& tollgates = reader.GetTollgateInfo();
    PrintTollgates(reader, tollgates);

    VoiceInfoSet& voices = reader.GetVoiceInfo();
    PrintVoices(reader, voices);
  }
}

void RouteDataSample::PrintLimitPass(RouteTileReader& reader, LimitPassSet& limit_passes) {
  std::string file_name_prefix = prefix_ + "output/" + std::to_string(reader.GetTileID().adcode) +
                                 "_" + std::to_string(reader.GetTileID().level) + "_" +
                                 std::to_string(reader.GetTileID().mesh_col) + "_" +
                                 std::to_string(reader.GetTileID().mesh_row) + "_" +
                                 std::to_string(reader.GetTileID().tile_id);
  std::string file_name = file_name_prefix + "_limit.txt";
  std::ofstream out_file(file_name.c_str());
  if (out_file.is_open()) {
    out_file << std::setprecision(8);
    out_file << "Limit" << std::endl;
    for (size_t i = 0; i < limit_passes.size(); ++i) {
      LimitPass& limit = limit_passes[i];
      const LimitPassBase* base = limit.GetBaseInfo();
      if (base != nullptr) {
        LngLat pos = GetPoint(reader, base->in_edge_id, base->in_edge_dir);
        uint32_t type = base->access_ctrl_type;
        uint32_t relation = base->access_ctrl_relation;
        const char* domain = limit.GetTimeDomain();
        out_file << "POINT(" << pos.x() << " " << pos.y() << ")|id:" << i << "|type:" << type
                 << "|relation:" << relation << "|time:";
        if (domain != nullptr) {
          out_file << domain;
        }
        uint32_t in_link = base->in_edge_id;
        uint32_t in_dir = base->in_edge_dir;
        uint32_t out_tile = base->out_tile_id;
        uint32_t out_link = base->out_edge_id;
        uint32_t out_dir = base->out_edge_dir;
        out_file << "|inLink:" << in_link << "&in_dir:" << in_dir << "&out_tile:" << out_tile
                 << "&out_link:" << out_link << "&out_dir:" << out_dir;
        out_file << std::endl;
      }
    }
  }
}

LngLat RouteDataSample::GetPoint(RouteTileReader& reader, uint32_t link, uint32_t dir) {
  AugmentEdge* edge = reader.GetAugmentEdgeByID(link);
  LngLat pos = edge->GetGeoPoints()[0];
  if (dir == 0) {
    pos = edge->GetGeoPoints().back();
  }
  return pos;
}

void RouteDataSample::PrintLanes(RouteTileReader& reader, LaneInfoSet& lanes) {
  std::string file_name_prefix = prefix_ + "output/" + std::to_string(reader.GetTileID().adcode) +
                                 "_" + std::to_string(reader.GetTileID().level) + "_" +
                                 std::to_string(reader.GetTileID().mesh_col) + "_" +
                                 std::to_string(reader.GetTileID().mesh_row) + "_" +
                                 std::to_string(reader.GetTileID().tile_id);
  std::string file_name = file_name_prefix + "_lane.txt";
  std::ofstream out_file(file_name.c_str());
  if (out_file.is_open()) {
    out_file << std::setprecision(8);
    out_file << "Lane" << std::endl;
    for (size_t i = 0; i < lanes.size(); ++i) {
      LaneInfo& lane = lanes[i];
      const LaneBase* base = lane.GetBaseInfo();
      if (base != nullptr) {
        LngLat pos = GetPoint(reader, base->in_edge_id, base->in_edge_dir);
        uint32_t count = base->lane_count;
        out_file << "POINT(" << pos.x() << " " << pos.y() << ")|id:" << i << "|laneCount:" << count
                 << "|relationCount:" << (int)base->lane_relation_count << "|";
        for (size_t j = 0; j < 16; ++j) {
          LaneType shape = base->lane_type[j];
          if (shape.value != 0) {
            uint32_t straight = shape.straight;
            uint32_t slight_right = shape.slight_right;
            uint32_t right = shape.right;
            uint32_t sharp_right = shape.sharp_right;
            uint32_t u_turn = shape.u_turn;
            uint32_t sharp_left = shape.sharp_left;
            uint32_t left = shape.left;
            uint32_t slight_left = shape.slight_left;

            uint32_t bus = shape.bus;
            uint32_t variable = shape.variable;
            uint32_t left_extend = shape.left_extend;
            uint32_t right_extend = shape.right_extend;
            uint32_t null = shape.null;
            uint32_t hov = shape.hov;
            uint32_t tide = shape.tide;
            out_file << "{shape" << j << ":" << straight << slight_right << right << sharp_right
                     << u_turn << sharp_left << left << slight_left << bus << variable
                     << left_extend << right_extend << null << hov << tide << "}";
          } else {
            break;
          }
        }

        uint32_t in_link = base->in_edge_id;
        uint32_t in_dir = base->in_edge_dir;
        out_file << "|inLink:" << in_link << "&in_dir:" << in_dir;
        out_file << std::endl;
      }
    }
  }
}

void RouteDataSample::PrintFacilities(RouteTileReader& reader, FacilityInfoSet& facilities) {
  std::string file_name_prefix = prefix_ + "output/" + std::to_string(reader.GetTileID().adcode) +
                                 "_" + std::to_string(reader.GetTileID().level) + "_" +
                                 std::to_string(reader.GetTileID().mesh_col) + "_" +
                                 std::to_string(reader.GetTileID().mesh_row) + "_" +
                                 std::to_string(reader.GetTileID().tile_id);
  std::string file_name = file_name_prefix + "_facility.txt";
  std::ofstream out_file(file_name.c_str());
  if (out_file.is_open()) {
    out_file << std::setprecision(8);
    out_file << "Facility" << std::endl;
    for (size_t i = 0; i < facilities.size(); ++i) {
      FacilityInfo& facility = facilities[i];
      const FacilityBase* base = facility.GetBaseInfo();
      if (base != nullptr) {
        LngLat pos = facility.GetPosition();
        uint32_t type = base->type;
        uint32_t speed = base->speed_limit;
        uint32_t len = base->distance_to_start;
        uint32_t angle = base->angle;
        out_file << "POINT(" << pos.x() << " " << pos.y() << ")|id:" << i << "|type:" << type
                 << "&speed:" << speed << "&length:" << len << "&angle:" << angle;

        uint32_t in_link = base->in_edge_id;
        uint32_t in_dir = base->in_edge_dir;
        out_file << "|inLink:" << in_link << "&in_dir:" << in_dir;
        out_file << std::endl;
      }
    }
  }
}

void RouteDataSample::PrintSignposts(RouteTileReader& reader, SignpostInfoSet& signposts) {
  std::string file_name_prefix = prefix_ + "output/" + std::to_string(reader.GetTileID().adcode) +
                                 "_" + std::to_string(reader.GetTileID().level) + "_" +
                                 std::to_string(reader.GetTileID().mesh_col) + "_" +
                                 std::to_string(reader.GetTileID().mesh_row) + "_" +
                                 std::to_string(reader.GetTileID().tile_id);
  std::string file_name = file_name_prefix + "_signpost.txt";
  std::ofstream out_file(file_name.c_str());
  if (out_file.is_open()) {
    out_file << std::setprecision(8);
    out_file << "Signpost" << std::endl;
    for (size_t i = 0; i < signposts.size(); ++i) {
      SignpostInfo& signpost = signposts[i];
      const SignpostBase* base = signpost.GetBaseInfo();
      if (base != nullptr) {
        LngLat pos = GetPoint(reader, base->in_edge_id, base->in_edge_dir);
        out_file << "POINT(" << pos.x() << " " << pos.y() << ")|id:" << i << "|name:";
        for (size_t j = 0; j < signpost.GetSignInfoNameCount(); ++j) {
          const char* info = signpost.GetSignInfoName(j);
          if (info != nullptr) {
            out_file << info << " &";
          }
        }

        uint32_t in_link = base->in_edge_id;
        uint32_t in_dir = base->in_edge_dir;
        uint32_t same_mesh = base->is_same_mesh;
        uint32_t same_tile = base->is_same_tile;
        uint32_t out_mesh_col = base->out_mesh_col;
        uint32_t out_mesh_row = base->out_mesh_row;
        uint32_t out_tile = base->out_tile_id;
        uint32_t out_link = base->out_edge_id;
        uint32_t out_dir = base->out_edge_dir;
        out_file << "|inLink:" << in_link << "&in_dir:" << in_dir << "&same_mesh:" << same_mesh
                 << "&same_tile:" << same_tile << "&out_mesh_col:" << out_mesh_col
                 << "&out_mesh_row:" << out_mesh_row << "&out_tile:" << out_tile
                 << "&out_link:" << out_link << "&out_dir:" << out_dir;
        out_file << std::endl;
      }
    }
  }
}

void RouteDataSample::PrintTollgates(RouteTileReader& reader, TollgateInfoSet& tollgates) {
  std::string file_name_prefix = prefix_ + "output/" + std::to_string(reader.GetTileID().adcode) +
                                 "_" + std::to_string(reader.GetTileID().level) + "_" +
                                 std::to_string(reader.GetTileID().mesh_col) + "_" +
                                 std::to_string(reader.GetTileID().mesh_row) + "_" +
                                 std::to_string(reader.GetTileID().tile_id);
  std::string file_name = file_name_prefix + "_tollgate.txt";
  std::ofstream out_file(file_name.c_str());
  if (out_file.is_open()) {
    out_file << std::setprecision(8);
    out_file << "Tollgate" << std::endl;
    for (size_t i = 0; i < tollgates.size(); ++i) {
      TollgateInfo& tollgate = tollgates[i];
      const TollgateBase* base = tollgate.GetBaseInfo();
      if (base != nullptr) {
        LngLat pos = GetPoint(reader, base->in_edge_id, base->in_edge_dir);
        out_file << "POINT(" << pos.x() << " " << pos.y() << ")|id:" << i;

        if (tollgate.GetName() != nullptr) {
          out_file << "|name:" << tollgate.GetName();
        } else {
          out_file << "|name:null";
        }
        std::vector<char> bg_img_no(17, 0);
        ::memcpy(&bg_img_no[0], base->background_img_no, 16);
        out_file << "|bg:" << &bg_img_no[0];

        out_file << "|";
        for (size_t j = 0; j < tollgate.GetTollgateGateCount(); ++j) {
          const TollgateGateInfo* gate = tollgate.GetTollgateGateInfo(j);
          if (gate != nullptr) {
            uint32_t cash = gate->cash;
            uint32_t etc = gate->etc;
            uint32_t auto_card = gate->auto_card;
            uint32_t alipay = gate->alipay;
            uint32_t wechat = gate->wechat;
            uint32_t itc = gate->itc;
            uint32_t normal = gate->normal;
            uint32_t hk_macao = gate->hk_macao;
            uint32_t general = gate->general;
            uint32_t wide_lane = gate->wide_lane;
            out_file << "{gate" << j << ":" << cash << etc << auto_card << alipay << wechat << itc
                     << normal << hk_macao << general << wide_lane << "}";
          }
        }

        uint32_t in_link = base->in_edge_id;
        uint32_t in_dir = base->in_edge_dir;
        uint32_t same_mesh = base->is_same_mesh;
        uint32_t same_tile = base->is_same_tile;
        uint32_t out_mesh_col = base->out_mesh_col;
        uint32_t out_mesh_row = base->out_mesh_row;
        uint32_t out_tile = base->out_tile_id;
        uint32_t out_link = base->out_edge_id;
        uint32_t out_dir = base->out_edge_dir;
        out_file << "|inLink:" << in_link << "&in_dir:" << in_dir << "&same_mesh:" << same_mesh
                 << "&same_tile:" << same_tile << "&out_mesh_col:" << out_mesh_col
                 << "&out_mesh_row:" << out_mesh_row << "&out_tile:" << out_tile
                 << "&out_link:" << out_link << "&out_dir:" << out_dir;
        out_file << std::endl;
      }
    }
  }
}

void RouteDataSample::PrintVoices(RouteTileReader& reader, VoiceInfoSet& voices) {
  std::string file_name_prefix = prefix_ + "output/" + std::to_string(reader.GetTileID().adcode) +
                                 "_" + std::to_string(reader.GetTileID().level) + "_" +
                                 std::to_string(reader.GetTileID().mesh_col) + "_" +
                                 std::to_string(reader.GetTileID().mesh_row) + "_" +
                                 std::to_string(reader.GetTileID().tile_id);
  std::string file_name = file_name_prefix + "_voice.txt";
  std::ofstream out_file(file_name.c_str());
  if (out_file.is_open()) {
    out_file << std::setprecision(8);
    out_file << "Voice" << std::endl;
    for (size_t i = 0; i < voices.size(); ++i) {
      VoiceInfo& voice = voices[i];
      const VoiceBase* base = voice.GetBaseInfo();
      if (base != nullptr) {
        LngLat pos = GetPoint(reader, base->in_edge_id, base->in_edge_dir);
        uint32_t type = base->voice_type;
        out_file << "POINT(" << pos.x() << " " << pos.y() << ")|id:" << i << "|type:" << type;

        uint32_t in_link = base->in_edge_id;
        uint32_t in_dir = base->in_edge_dir;
        uint32_t same_mesh = base->is_same_mesh;
        uint32_t same_tile = base->is_same_tile;
        uint32_t out_mesh_x = base->out_mesh_x;
        uint32_t out_mesh_y = base->out_mesh_y;
        uint32_t out_tile = base->out_tile_id;
        uint32_t out_link = base->out_edge_id;
        uint32_t out_dir = base->out_edge_dir;
        out_file << "|inLink:" << in_link << "&in_dir:" << in_dir << "&same_mesh:" << same_mesh
                 << "&same_tile:" << same_tile << "&out_mesh_x:" << out_mesh_x
                 << "&out_mesh_y:" << out_mesh_y << "&out_tile:" << out_tile
                 << "&out_link:" << out_link << "&out_dir:" << out_dir;

        out_file << std::endl;
      }
    }
  }
}

void RouteDataSample::PrintJuncview(RouteTileReader& reader, JuncviewInfoSet& juncviews) {
  std::string file_name_prefix = prefix_ + "output/" + std::to_string(reader.GetTileID().adcode) +
                                 "_" + std::to_string(reader.GetTileID().level) + "_" +
                                 std::to_string(reader.GetTileID().mesh_col) + "_" +
                                 std::to_string(reader.GetTileID().mesh_row) + "_" +
                                 std::to_string(reader.GetTileID().tile_id);
  std::string file_name = file_name_prefix + "_juncview.txt";
  std::ofstream out_file(file_name.c_str());
  if (out_file.is_open()) {
    out_file << std::setprecision(8);
    out_file << "Juncview" << std::endl;
    for (size_t i = 0; i < juncviews.size(); ++i) {
      JuncviewInfo& juncview = juncviews[i];
      const JuncviewBase* base = juncview.GetBaseInfo();
      if (base != nullptr) {
        LngLat pos = GetPoint(reader, base->in_edge_id, base->in_edge_dir);
        out_file << "POINT(" << pos.x() << " " << pos.y() << ")|id:" << i;
        std::vector<char> bg_img_no(17, 0);
        std::vector<char> fg_img_no(17, 0);
        ::memcpy(&bg_img_no[0], base->background_img_no, 16);
        ::memcpy(&fg_img_no[0], base->foreground_img_no, 16);
        out_file << "|bg:" << &bg_img_no[0] << "|fg:" << &fg_img_no[0];

        uint32_t in_link = base->in_edge_id;
        uint32_t in_dir = base->in_edge_dir;
        uint32_t same_mesh = base->is_same_mesh;
        uint32_t same_tile = base->is_same_tile;
        uint32_t out_mesh_col = base->out_mesh_col;
        uint32_t out_mesh_row = base->out_mesh_row;
        uint32_t out_tile = base->out_tile_id;
        uint32_t out_link = base->out_edge_id;
        uint32_t out_dir = base->out_edge_dir;
        out_file << "|inLink:" << in_link << "&in_dir:" << in_dir << "&same_mesh:" << same_mesh
                 << "&same_tile:" << same_tile << "&out_mesh_col:" << out_mesh_col
                 << "&out_mesh_row:" << out_mesh_row << "&out_tile:" << out_tile
                 << "&out_link:" << out_link << "&out_dir:" << out_dir;
        out_file << std::endl;
      }
    }
  }
}

void RouteDataSample::PrintEdges(RouteTileReader& reader, AugmentEdgeSet& edges) {
  std::string file_name_prefix = prefix_ + "output/" + std::to_string(reader.GetTileID().adcode) +
                                 "_" + std::to_string(reader.GetTileID().level) + "_" +
                                 std::to_string(reader.GetTileID().mesh_col) + "_" +
                                 std::to_string(reader.GetTileID().mesh_row) + "_" +
                                 std::to_string(reader.GetTileID().tile_id);
  std::string file_name = file_name_prefix + "_edge.txt";
  std::ofstream out_file(file_name.c_str());
  if (out_file.is_open()) {
    out_file << std::setprecision(8);
    out_file << "Edge" << std::endl;
    for (size_t i = 0; i < edges.size(); ++i) {
      const std::vector<aurora::parser::LngLat>& points = edges[i].GetGeoPoints();
      //      if (i > 30) {
      //        return;
      //      }
      out_file << "LINESTRING(";
      for (size_t j = 0; j < points.size(); ++j) {
        const aurora::parser::LngLat& pos = points[j];
        out_file << "" << pos.x() << " " << pos.y();
        if (j < points.size() - 1) {
          out_file << ",";
        }
      }
      out_file << ")|id:" << i;

      if (edges[i].GetBaseInfo()->has_road_name) {
        if (edges[i].GetLocalName() != nullptr) {
          out_file << "|name:" << edges[i].GetLocalName();
        } else {
          out_file << "|name:null";
        }
        if (edges[i].GetForeignName() != nullptr) {
          out_file << "|froreign:" << edges[i].GetForeignName();
        } else {
          out_file << "|froreign:null";
        }
      } else {
        out_file << "|name:null"
                 << "|froreign:null";
      }
      if (edges[i].GetBaseInfo()->has_road_no && edges[i].GetRoadNo() != nullptr) {
        out_file << "|roadNo:" << edges[i].GetRoadNo();
      } else {
        out_file << "|roadNo:null";
      }
      TopolEdge* edge = reader.GetTopolEdgeByID(i);
      if (edge != nullptr) {
        const TopolEdgeBase* info = edge->GetBaseInfo();
        if (info != nullptr) {
          out_file << "|dir:" << (int)info->direction;
          if (info->direction == EdgeDirection::kEdgeDirectionReversePass) {
            out_file << "|snode:" << info->end_node_id;
            out_file << "&enode:" << info->start_node_id;
          } else {
            out_file << "|snode:" << info->start_node_id;
            out_file << "&enode:" << info->end_node_id;
          }
          uint32_t road_class = info->road_class;
          out_file << "|class:" << road_class;
        }
      }

      out_file << std::endl;
    }
  }
}

void RouteDataSample::PrintNodes(RouteTileReader& reader, RouteNodeSet& nodes) {
  std::string file_name_prefix = prefix_ + "output/" + std::to_string(reader.GetTileID().adcode) +
                                 "_" + std::to_string(reader.GetTileID().level) + "_" +
                                 std::to_string(reader.GetTileID().mesh_col) + "_" +
                                 std::to_string(reader.GetTileID().mesh_row) + "_" +
                                 std::to_string(reader.GetTileID().tile_id);
  std::string file_name = file_name_prefix + "_node.txt";
  std::ofstream out_file(file_name.c_str());
  if (out_file.is_open()) {
    out_file << std::setprecision(8);
    out_file << "Node" << std::endl;
    for (size_t i = 0; i < nodes.size(); ++i) {
      RouteNode& node = nodes[i];
      const NodeBase* base = node.GetBaseInfo();
      if (base != nullptr) {
        out_file << "POINT(" << node.GetPosition().x() << " " << node.GetPosition().y()
                 << ")|id:" << i << "|bNodeNum:" << base->boundary_node_count;
        const std::vector<BoundaryNode*>& b_nodes = node.GetBoundaryNode();
        for (size_t i = 0; i < b_nodes.size(); ++i) {
          out_file << "&" << b_nodes[i]->adj_node_id;
        }
        const std::vector<ConnectedEdge*>& cEdges = node.GetConnectedEdge();
        out_file << "|connLinkNum:" << cEdges.size();
        for (size_t i = 0; i < cEdges.size(); ++i) {
          out_file << "&" << cEdges[i]->edge_id;
        }
        const AdminBoundaryNode* adnode = node.GetAdBoundaryNode();
        if (adnode != nullptr) {
          out_file << "|adNode:" << adnode->adcode << "&" << adnode->adj_mesh_col << "&"
                   << adnode->adj_mesh_row << "&" << adnode->adj_node_id;
        } else {
          out_file << "|adNode:null";
        }
        const TransNodeInfo* trUNode = node.GetTransUpNodeInfo();
        if (trUNode != nullptr) {
          out_file << "|trUNode:" << trUNode->opp_node_id;
        } else {
          out_file << "|trUNode:null";
        }
        const TransNodeInfo* trDNode = node.GetTransDownNodeInfo();
        if (trDNode != nullptr) {
          out_file << "&trDNode:" << trDNode->opp_node_id;
        } else {
          out_file << "&trDNode:null";
        }
        const std::vector<ConnectedEdge*>& edges = node.GetConnectedEdge();
        out_file << "|edgeNum:" << edges.size() << "&";
        for (size_t i = 0; i < edges.size(); ++i) {
          out_file << edges[i]->edge_id << "&";
        }
        out_file << "|others:";
        RouteNodeID node_id(reader.GetTileID(), node.GetID());
        RouteEdgeIDSet in_edges;
        std::vector<bool> forwards;
        if (reader.GetInEdgeID(node_id, in_edges, forwards)) {
          out_file << "&inEdge:";
          for (size_t i = 0; i < in_edges.size(); ++i) {
            out_file << "&" << in_edges[i].feature_id;
          }
        }

        std::vector<bool> forwards1;
        RouteEdgeIDSet out_edges;
        if (reader.GetOutEdgeID(node_id, out_edges, forwards1)) {
          out_file << "&outEdge:";
          for (size_t i = 0; i < out_edges.size(); ++i) {
            out_file << "&" << out_edges[i].feature_id;
          }
        }

        std::vector<bool> forwards2;
        RouteEdgeIDSet up_in_edges;
        if (reader.GetTransUpInEdgeID(node_id, up_in_edges, forwards2)) {
          out_file << "&upInEdge:";
          for (size_t i = 0; i < up_in_edges.size(); ++i) {
            out_file << "&" << up_in_edges[i].feature_id;
          }
        }

        std::vector<bool> forwards3;
        RouteEdgeIDSet up_out_edges;
        if (reader.GetTransUpOutEdgeID(node_id, up_out_edges, forwards3)) {
          out_file << "&upOutEdge:";
          for (size_t i = 0; i < up_out_edges.size(); ++i) {
            out_file << "&" << up_out_edges[i].feature_id;
          }
        }

        std::vector<bool> forwards4;
        RouteEdgeIDSet down_in_edges;
        if (reader.GetTransDownInEdgeID(node_id, down_in_edges, forwards4)) {
          out_file << "&downInEdge:";
          for (size_t i = 0; i < down_in_edges.size(); ++i) {
            out_file << "&" << down_in_edges[i].feature_id;
          }
        }

        std::vector<bool> forwards5;
        RouteEdgeIDSet down_out_edges;
        if (reader.GetTransDownOutEdgeID(node_id, down_out_edges, forwards5)) {
          out_file << "&downOutEdge:";
          for (size_t i = 0; i < down_out_edges.size(); ++i) {
            out_file << "&" << down_out_edges[i].feature_id;
          }
        }

        out_file << std::endl;
      }
    }
  }
}
}  // namespace parser
}  // namespace aurora
