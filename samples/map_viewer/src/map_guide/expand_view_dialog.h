﻿
#ifndef EXPAND_VIEW_DIALOG_H
#define EXPAND_VIEW_DIALOG_H

#include <QDialog>
#include "ui_expand_view_dialog.h"
#include "util_type_define.h"
#include "guidance_def.h"

struct tagRgeGpb;
class ExpandViewDialog : public QDialog
{
	Q_OBJECT

public:
	ExpandViewDialog(QWidget *parent = 0);
	~ExpandViewDialog();
	void updatesignboard();
	void updategpbpic();

signals:
	void winclose();

public slots:
	void closepopmenu();
	void do_rge_gp_msg(long event_type, long param);
	void do_showImage();
	void do_hideImage();
    void onUpdateExpandView(aurora::guide::NavigationJunctionViewInfoPtr jv_ptr);

private:
	void UpdateProgressbar(ULONG dist_to_gp, bool is_freeway);
	void UpdateDistToGp(ULONG dist_to_gp);
	void DoMsgUpdateGp();
	void DoMsgNewGp();
    //void UpdatePicture(tagRgeGpn* rge_gpn);

    bool saveImage(const std::vector<int8_t>& imageData, const QString& filePath);

private:
	Ui::expand_view_dlg ui;
    std::string  m_id;
};

#endif // EXPAND_VIEW_DIALOG_H
