

#ifndef GUIDANCE_CONTROL_H
#define GUIDANCE_CONTROL_H

#include <QObject>
#include "guidance/include/guidance_def.h"
#include "guidance_module.h"

class GuidanceControl : public QObject
{
    Q_OBJECT
public:

    GuidanceControl(QObject *parent);
    ~GuidanceControl();

signals:
    void update_guide_msg(QString text);
public slots:
    void onGetTBTDetailsInfo();
    void onUpdatePlayTTS(aurora::guide::GuidanceSoundInfoPtr sound_tts);

public:
    std::vector<aurora::guide::NavigationManeuverInfoPtr> m_tbt_details;

private:



};


#endif // GUIDANCE_CONTROL_H
