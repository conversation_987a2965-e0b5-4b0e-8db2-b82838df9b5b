
#include "location_control.h"
#include "geo_point.h"
#include "location_layer.h"

using namespace aurora::loc;

LocationControl::LocationControl(QObject *parent)
{
   m_loction_layer_ptr = nullptr;
   m_select_path_id = 0;
}

LocationControl::~LocationControl()
{

}

void LocationControl::setMapEngineLoctionInterFace(LocationPtr interface_ptr)
{
    m_loction_interface_ptr = interface_ptr;
}

void LocationControl::onUpdateSelectPathId(uint64_t id)
{
    m_select_path_id = id;
}

bool LocationControl::startSimulation( aurora::path::PathResultPtr path_result_ptr)
{

    if (m_select_path_id == 0)
        return false;

    if (path_result_ptr) {
        for (auto &item : path_result_ptr->paths)
        {
            if (item.path_id == m_select_path_id) {
                m_select_path_ptr = std::make_shared<aurora::path::PathInfo>(item);
                m_loction_layer_ptr->onStartDrive(m_select_path_ptr);
                m_loction_interface_ptr->SetRoutePath(m_select_path_ptr);
                std::shared_ptr<SimulatorController> Simulation_ptr =  m_loction_interface_ptr->GetSimulatorController();
                if (Simulation_ptr) {
                    Simulation_ptr->Start();
                    return true;
                }
            }
        }
    }

    return false;
}

void LocationControl::onClearDriveView()
{

    if (m_loction_layer_ptr)
        m_loction_layer_ptr->onClearDriveView();
}

void LocationControl::setLoctionLayer(LocationLayer * loction_layer_ptr)
{
    m_loction_layer_ptr = loction_layer_ptr;
}

void LocationControl::setSimulatorConfig(SimulConfig config)
{
    if (m_loction_interface_ptr )
    {
        std::shared_ptr<SimulatorController> Simulation_ptr =  m_loction_interface_ptr->GetSimulatorController();
        if (Simulation_ptr) {
             Simulation_ptr->SetConfig(config);
             m_simulator_config = config;
        }
    }


}

void LocationControl::resume()
{
    if (m_loction_interface_ptr) {
        std::shared_ptr<SimulatorController> Simulation_ptr =  m_loction_interface_ptr->GetSimulatorController();
        Simulation_ptr->Resume();
    }
}

void LocationControl::pause()
{
    if (m_loction_interface_ptr) {
        std::shared_ptr<SimulatorController> Simulation_ptr =  m_loction_interface_ptr->GetSimulatorController();
        Simulation_ptr->Pause();
    }
}

void LocationControl::play()
{
    if (m_loction_interface_ptr) {
        std::shared_ptr<SimulatorController> Simulation_ptr =  m_loction_interface_ptr->GetSimulatorController();
        Simulation_ptr->Start();

        m_loction_layer_ptr->showCarFlag();
    }
}

void LocationControl::stop()
{
    if (m_loction_interface_ptr) {
        std::shared_ptr<SimulatorController> Simulation_ptr =  m_loction_interface_ptr->GetSimulatorController();
        Simulation_ptr->Stop();

        m_loction_layer_ptr->onClearDriveView();
    }
}

void LocationControl::setTraceLogData(QString file_path)
{
    if (m_loction_interface_ptr) {
        std::shared_ptr<SimulatorController> Simulation_ptr =  m_loction_interface_ptr->GetSimulatorController();
        Simulation_ptr->LoadTrackData(file_path.toStdString());
    }
}






