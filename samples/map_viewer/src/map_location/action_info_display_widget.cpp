﻿
#include "action_info_display_widget.h"
#include "main_window.h"
#include "map_widget_manage.h"
#include "guidance/include/guidance_def.h"
#include "map_engine_manage.h"
#include "map_widget_manage.h"
#include "route_plan_layer.h"
#include "gps_encry.h"
#include "guidance_common.h"


using namespace aurora::guide;


ActionInfoDisplayWidget::ActionInfoDisplayWidget(QWidget *parent)
	: QDockWidget(parent)
    , m_tbtDetails_ptr(nullptr)
    , m_map_type(MAP_INVAID)
{
    ui = new Ui::ActionInfoDisplayWidget();
	ui->setupUi(this);

    m_map_widget_ptr = g_app->getMainWindow()->getMapWidget();
}

ActionInfoDisplayWidget::~ActionInfoDisplayWidget()
{
	delete ui;
}

void ActionInfoDisplayWidget::showGpInfo()
{

}

void ActionInfoDisplayWidget::showIpInfo()
{

}

void ActionInfoDisplayWidget::showLaneInfos()
{

}

void ActionInfoDisplayWidget::showHighwayBoardPoints()
{

}

void ActionInfoDisplayWidget::clearSelectionItem()
{
	ui->treeWidget->setCurrentItem(nullptr);
}

QIcon ActionInfoDisplayWidget::getTbtInfoIconByType(int &type)
{
    return guidanceCommon::getTbtInfoIconByType(type);

}


void ActionInfoDisplayWidget::showTBTInfo(std::vector<NavigationManeuverInfoPtr> &tbtDetails)
{
    if (!ui->treeWidget)
        return;

    m_map_type = MAP_ENGINE;

    ui->treeWidget->clear();
    ui->treeWidget->setIconSize(QSize(30, 30));
    m_tbtDetails_ptr = &tbtDetails;
    QString text;
    QTreeWidgetItem* item = nullptr;
    for (size_t i = 0; i < tbtDetails.size(); i++) {

        auto &tbt = tbtDetails.at(i);
        item = new QTreeWidgetItem(ui->treeWidget);
        item->setIcon(0, getTbtInfoIconByType(tbt->type));

        if ((i+1) == tbtDetails.size() ) {
            text = QString(QString::fromLocal8Bit("到达目的地"));
        } else {
            text = QString("%1->%2 %3%4 m")
                          .arg(QString::fromLocal8Bit(tbt->action.c_str()))
                          .arg(QString::fromLocal8Bit(tbt->begin_name.c_str()))
                          .arg(QString::fromLocal8Bit("行驶"))
                          .arg(tbt->length);
       }

        item->setText(1, text);

    }
    ui->treeWidget->resizeColumnToContents(0);
    ui->treeWidget->expandToDepth(0);
}


void ActionInfoDisplayWidget::onItemSelectionChanged()
{

    QTreeWidgetItem* item = ui->treeWidget->currentItem(); // 获取当前节点
    if (item == nullptr)
        return;

    auto rc = MapEngineManage::GetInstance()->m_routingCenter_ptr;

    if (m_map_widget_ptr) {

        switch (m_map_type)
        {
        case ENUM_MAPTYPE::MAP_ENGINE:
        {
            if (!m_tbtDetails_ptr)
                return;

           uint32 idx = ui->treeWidget->indexOfTopLevelItem(item);
           if (idx < m_tbtDetails_ptr->size()) {
               m_map_widget_ptr->getRoutePlanLayer()->setSelectTbtLink(m_tbtDetails_ptr->at(idx)->geos);
           }

        }
        break;
        case ENUM_MAPTYPE::BAIDU_MAP:
        {
            if (item->parent() && !item->parent()->parent())
            {
                QTreeWidgetItem *parent = item->parent();
                size_t index = parent->indexOfChild(item); // 获取子项在父项中的索引

                vector<NaviAction>* bmap_actions = rc->getOnlineRoute()->getBMapActions();
                if (index < bmap_actions->size())
                {
                    m_map_widget_ptr->getRoutePlanLayer()->setOnlineSelectActionCoords(&bmap_actions->at(index).coords, BAIDU_MAP);
                }

            }
        }
         break;
        case ENUM_MAPTYPE::AMAP:
        {
            if (item->parent() && !item->parent()->parent())
            {
                QTreeWidgetItem *parent = item->parent();
                size_t index = parent->indexOfChild(item); // 获取子项在父项中的索引

                vector<NaviActionAmap>* amap_actions = rc->getOnlineRoute()->getAMapActions();
                if (index < amap_actions->size())
                {
                    m_map_widget_ptr->getRoutePlanLayer()->setOnlineSelectActionCoords(&amap_actions->at(index).coords, AMAP);
                }
            }

        }
         break;
        case ENUM_MAPTYPE::MAP_INVAID:
        {
            return;
        }
        }

    }


}

void ActionInfoDisplayWidget::updateDisplay(ENUM_MAPTYPE type)
{
    m_map_type = type;
    ui->treeWidget->clear();

    switch (type)
    {
    case BAIDU_MAP:
        showBmapActions();
        break;
    case AMAP:
        showAmapActions();
        break;
    default:
        break;
    }

    //ui->treeWidget->expandToDepth(2);
    ui->treeWidget->expandToDepth(0);
    ui->treeWidget->resizeColumnToContents(0);
}

void ActionInfoDisplayWidget::showBmapActions()
{
    QTreeWidgetItem* item;
    QTreeWidgetItem* sub_item;

    vector<NaviAction>* actions = MapEngineManage::GetInstance()->m_routingCenter_ptr->getOnlineRoute()->getBMapActions();

    item = new QTreeWidgetItem(ui->treeWidget);
    item->setText(0, tr("Bmap"));
    item->setText(1, QString::number(actions->size()));

    for (size_t i = 0; i < actions->size(); i++)
    {
        sub_item = new QTreeWidgetItem(item);
        sub_item->setText(0, QString("action %1").arg(i));
        showBmapOneAction(actions->at(i), sub_item);
    }
}

void ActionInfoDisplayWidget::showAmapActions()
{
    QTreeWidgetItem* item;
    QTreeWidgetItem* sub_item;

    vector<NaviActionAmap>* actions = MapEngineManage::GetInstance()->m_routingCenter_ptr->getOnlineRoute()->getAMapActions();

    item = new QTreeWidgetItem(ui->treeWidget);
    item->setText(0, tr("Amap"));
    item->setText(1, QString::number(actions->size()));

    for (size_t i = 0; i < actions->size(); i++)
    {
        sub_item = new QTreeWidgetItem(item);
        sub_item->setText(0, QString("action %1").arg(i));
        showAmapOneAction(actions->at(i), sub_item);
    }
}

void ActionInfoDisplayWidget::showAmapOneAction(NaviActionAmap action, QTreeWidgetItem* item)
{
    QTreeWidgetItem* sub_item;
#if defined(WIN32) || defined(_WIN32_WCE)
    QTextCodec *tc = QTextCodec::codecForName("GBK");
#else
    QTextCodec *tc = QTextCodec::codecForName("UTF-8");
#endif

    sub_item = new QTreeWidgetItem(item);
    sub_item->setText(0, "distance");
    sub_item->setText(1, QString("%1").arg(util::NumToKmM(action.distance)));

    if (action.toll_distance)
    {
        sub_item = new QTreeWidgetItem(item);
        sub_item->setText(0, "toll");
        sub_item->setText(1, QString("%1 (%2%3)").arg(util::NumToKmM(action.toll_distance)).arg(action.tolls).arg(tc->toUnicode(QByteArray("元"))));
    }

    //sub_item = new QTreeWidgetItem(item);
    //sub_item->setText(0, "toll_road");
    //sub_item->setText(1, QString("%1").arg(action.toll_road));

    if (!action.action.isEmpty())
    {
        sub_item = new QTreeWidgetItem(item);
        sub_item->setText(0, "action_assistant");
        if (!action.assistant_action.isEmpty())
        {
            sub_item->setText(1, QString("%1 (%2)").arg(action.action).arg(action.assistant_action));
        }
        else
        {
            sub_item->setText(1, QString("%1").arg(action.action));
        }
    }

    if (!action.instruction.isEmpty())
    {
        sub_item = new QTreeWidgetItem(item);
        sub_item->setText(0, "instruction");
        sub_item->setText(1, QString("%1").arg(action.instruction));
    }

    if (!action.road_name.isEmpty())
    {
        sub_item = new QTreeWidgetItem(item);
        sub_item->setText(0, "road");
        if (!action.orientation.isEmpty())
        {
            sub_item->setText(1, QString("%1 (%2)").arg(action.road_name).arg(action.orientation));
        }
        else
        {
            sub_item->setText(1, QString("%1").arg(action.road_name));
        }
    }
}

void ActionInfoDisplayWidget::showBmapOneAction(NaviAction& action, QTreeWidgetItem* item)
{
    QTreeWidgetItem* sub_item;
#if defined(WIN32) || defined(_WIN32_WCE)
    QTextCodec *tc = QTextCodec::codecForName("GBK");
#else
    QTextCodec *tc = QTextCodec::codecForName("UTF-8");
#endif

    sub_item = new QTreeWidgetItem(item);
    sub_item->setText(0, "turn");
    sub_item->setText(1, QString("%1").arg(tc->toUnicode(QByteArray(Bmap_turn_toString(action.turn)))));

    //sub_item = new QTreeWidgetItem(item);
    //sub_item->setText(0, "type");
    //sub_item->setText(1, QString("%1").arg(action.type));

    //sub_item = new QTreeWidgetItem(item);
    //sub_item->setText(0, "traffic_condition");
    //sub_item->setText(1, QString("%1").arg(action.traffic_condition));

    //sub_item = new QTreeWidgetItem(item);
    //sub_item->setText(0, "area");
    //sub_item->setText(1, QString("%1").arg(action.area));

    sub_item = new QTreeWidgetItem(item);
    sub_item->setText(0, "direction");
    sub_item->setText(1, QString("%1%2").arg(Bmap_direction_toString(action.direction)).arg(tc->toUnicode(QByteArray("度"))));

    sub_item = new QTreeWidgetItem(item);
    sub_item->setText(0, "distance_duration");
    sub_item->setText(1, QString("%1 (%2)").arg(util::NumToKmM(action.distance)).arg(util::NumToHousSec(action.duration)));

    if (!action.instruction.isEmpty())
    {
        sub_item = new QTreeWidgetItem(item);
        sub_item->setText(0, "instruction");
        sub_item->setText(1, QString("%1").arg(action.instruction));
    }

    if (!action.stepOriginInstruction.isEmpty())
    {
        sub_item = new QTreeWidgetItem(item);
        sub_item->setText(0, "stepOriginInstruction");
        sub_item->setText(1, QString("%1").arg(action.stepOriginInstruction));
    }

    if (!action.stepDestinationInstruction.isEmpty())
    {
        sub_item = new QTreeWidgetItem(item);
        sub_item->setText(0, "stepDestinationInstruction");
        sub_item->setText(1, QString("%1").arg(action.stepDestinationInstruction));
    }
}


