﻿
#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include <QtWidgets/QMainWindow>
#include <QtWidgets>
#include <QXmlStreamReader>
#include <QXmlStreamAttribute>
#include <QRegExp>
#include <QNetworkReply>

#include "util_funtion_tools.h"
#include "geo_point.h"
#include "routing_control.h"
#include "search/include/search_service_def.h"



using namespace map_engine;


namespace Ui { class MainWindowClass; }

class ToolBoxController;
class MapLayer;
class MapCamera;
class MapWidget;
class ExpandViewDialog;
class TurnMarkInfoDialog;
class ParallelDialog;
class BetterRouteDialog;
class MapDataAdapter;
class ViewEngineLayer;


class MainWindow : public QMainWindow
{
	Q_OBJECT

public:
	MainWindow(QWidget *parent = 0);
	~MainWindow();

    MapWidget* getMapWidget();

    void updatePoiResultList(std::shared_ptr<aurora::search::SearchByTextResponse> &response);
    ToolBoxController* getToolBoxController() { return m_toolBoxController; }

    void setToolboxWidth(int width);
    void onUpdatePoiSearchStatusInfo(QString info);
    void startCaclPath();
    void onNavigationArrive();

private:
    // Fixed map layers
    void loadPreferencesConfig();
    void setupFixedMapLayers();

    void updateInfoTree();
    void updateOpenRecnetMenu();
    void openFile(const QString &fileName);
    void setMapLayerTreeDisplayMenu( bool isDisplay);

    void setupSearchUi();
    void setupToolBoxUi();
    void setupRouteUi();
    void setupSimulationUi();
    void setupLoctionUi();

    void loadCityList();
    void resetPoiResult();
    void setSimulatorConfig();

    void createTurnMarkView();
    void showTurnMarkView();
    void hideTurnMarkView();

    void createExpandViewDialog();
    void showExpandView();
    void hideExpandView();

    void traverseDirectories(const QString& path, QStringList &fullPathList, QStringList &dirNameList, int depth = 0);

    void onReloadMapViewer(const QString *dataPath);
    void loadFilesFromDirectory(const QString &directoryPath, QStringList &file_name_list, QStringList &file_path_list);
    void setTraceLogData();

    int checkLastRegionCode(QStringList &data_path_list);
    void setSpeedComboBoxLocationConfig(int idx);

    void clearPoiResult();


public slots:
      void onUpdatePoiSearchPos(double lng, double lat);
      void onRoutePlanChanged();
      void onRouteResultChanged();

      void onUpdateRouteFileTestResultInfo(QString info);
      void onUpdateRouteFileTestLableInfo(QString info);

      void onSplitterMoved(int pos, int index);

private slots:
    // Menubar action slots
    void onActionOpenTriggered();
    void onShowToolBoxTriggered(bool checked);
    void onActionSetStartTriggered();
    void onActionSetEndTriggered();
    void onActionAddWayPointTriggered();
    void onActionDeleteRouteTriggered();
    void onActionFileTestTriggered();
    void onActionPreferencesTriggered();
    void onActionCompareWithBaiduTriggered(bool checked);
    void onActionCompareWithAmapTriggered(bool checked);
    void OnTurnMarkDialogDestroy();


    //Toolbar action slots
    void onActionSelectFeatureTriggered();
    void StartDrive();
    void startSimulation();
    void endSimulation();
    void onActionPathCalculationTriggered();
    void onActionPositionTriggered();
    void onActionHomeTrigger();
    void onActionCompanyTrigger();


    // toolbox slot
    void onLayerListContextMenu(const QPoint &pos);
    void onLayerListChanged(const QList<MapLayer*> *layerList);
    void onLayerListItemChanged(QListWidgetItem *item);
    void onRoutingRuleComboBoxCurrentIndexChanged(int index);

    void on_mapLayerList_itemClicked(QListWidgetItem *item);
    void onPoiSearch();
    void onSelectFileTestButtonClicked();
    void onRouteTestStartClickSlot();
    void onRouteTestStopClickSlot();
    void onRoutePlanListContextMenu(const QPoint &pos);
    void onRoutePlanListItemDoubleClicked(QListWidgetItem* item);
    void onRoutePlanListItemClicked(QListWidgetItem* item);
    void onRouteResultListChanged();
    void onGoPushButtonClicked();
    void onPlaybackIntervalComboBoxCurrentIndexChangeslot(int index);
    void onSimulCancelClickButtonslot();

    void onUpdataTurnMarkInfoDialogPos();
    void onUpdataExpandViewDialogPos();

    void onDataBaseItemDoubleClicked(QTreeWidgetItem* item, int id);

    void onLoadLogPushButtonClicked();
    void onPlayPushButtonClicked();
    void onStopPushButtonClicked();
    void onNextPushButtonClicked();

    void onSpeedComboBoxCurrentIndexChanged(int idx);
    void onShowDrCheckBoxClicked(bool checked);
    void onShowGpsCheckBoxClicked(bool checked);

private:

    Ui::MainWindowClass* ui;

    //for guide
    std::shared_ptr<ExpandViewDialog> m_expand_view_dlg_ptr;   // 放大图显示窗口
    std::shared_ptr<TurnMarkInfoDialog>  m_turn_mark_dlg_ptr;	// 引导转向图标显示
	ParallelDialog* m_parallel_dlg; // 平行路切换按钮窗口
	BetterRouteDialog* m_better_route_dlg; // 最优路线切换按钮窗口

    QLabel* m_statusMapLabel;

	void* m_viewLayer;
	void* m_routeLayer;
	void* m_locationLayer;
	void* m_searchLayer;
    ViewEngineLayer* m_viewEngineLayer;
	QComboBox*	m_guidanceModeComboBox;
	QComboBox*	m_languageComboBox;
	QComboBox*	m_simulationSpeedComboBox;
	QComboBox*	m_onlineOfflineCombox;

	QComboBox*  m_onlineTypeCombox;
	QComboBox*  m_routeModeCombox;

	QCheckBox*	m_muteCheckBox;

	QCheckBox*	m_cameraVoiceCheckBox;
	QCheckBox*	m_overspeedVoiceCheckBox;
	QCheckBox*	m_gpbGuideCheckBox;
	QCheckBox*	m_destPrecisionCheckBox;
	std::vector<QString> m_case_test_name_vec;  // case测试页中的名称列表
	QString m_case_test_file_path; // case测试文件路径

	MapDataAdapter*  m_mapDataCache;

    //LogPlayerController* m_logPlayerControl;
    //LogSim* m_logSim;
	QString m_lastLogFilePath;
	QString m_compareLogFilePath;
	QString m_simulationFilePath;
	QString m_simulationFolderPath;
	QStringList m_transferFileNames;
	QString m_lastTransferFileName;
	QString m_lastSearchTestFilePath;
	QStringList m_lastLocTestSimFile;
    QStringList m_loc_log_file_list;
	
	//引导点类型和id
	std::vector<QString> m_guider_type;
	std::vector<int32> m_guide_point_id;

    //SugResponse m_sug_response;
    //SearchResponse m_search_response;
    uint32 m_start_time;
    //std::set<QString> m_key_vec;
    //std::set<QString>::const_iterator m_it;

    QString m_last_open_folder_Path;
    std::string m_pre_province_name;

    ToolBoxController* m_toolBoxController;
    QStringList m_data_path_list;

    int m_last_select_region;


	struct LocTextExpectResult
	{
		QString key;
		QString	key_value;
		int		start_index;
		int		end_index;

		LocTextExpectResult(QString key, QString key_value, int start_index, int end_index)
			: key(key)
			, key_value(key_value)
			, start_index(start_index)
			, end_index(end_index)
		{
		}
	};
	std::vector<LocTextExpectResult> m_loc_test_expect_result;


	/* render engine view, s */
	bool        m_northUp;
	int         m_schema_model;
	QTimer*     m_centerPositionRefreshTimer; //地图中心点刷新计时器
	QTimer*		m_autoMoveTimer;

	/* render engine view, e */


};

#endif // MAIN_WINDOW_H
