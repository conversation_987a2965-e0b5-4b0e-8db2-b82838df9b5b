﻿
#ifndef MAP_WIDGET_H
#define MAP_WIDGET_H

#include <QOpenGLWidget>
#include <QOpenGLFunctions>
#include <QListWidgetItem>
#include <mutex>
#include "util_type_define.h"
#include "geo_point.h"

class MapLayer;
class MapCamera;
class MapDataAdapter;
class RoutePlanLayer;
class SearchPoiLayer;
class RenderEngineLayer;
class LocationLayer;

/**
    @brief 地图窗体
    @details
        在地图窗体中添加显示内容时，请通过增加或丰富地图图层的方法实现。
*/

enum KeyRotationStatus {
    KEY_ROTATION_LEFT = 0,
    KEY_ROTATION_RIGHT ,
    KEY_ROTATION_INVALID

};

class MapWidget : public QOpenGLWidget, protected QOpenGLFunctions
{
    Q_OBJECT

public:
    MapWidget(QWidget *parent = 0);
    ~MapWidget();

    // 游标相关
    void setCursorVisible(bool visible);
    bool isCursorVisible() { return m_cursorVisible; }

    // 图层(MapLayer)相关
    const QList<MapLayer*>& getLayerList() { return m_layers; }
    void addMapLayer(MapLayer *layer);
    bool removeMapLayerByIndex(int index);
    void setMapLayerVisibilityByIndex(int index, bool visibility);
    void SetMapDataCache(MapDataAdapter* cache);
    void initRouteDataMapLayer();
    void setSelectMapLayerType(enMapLayerType type) {m_layer_type_select =  type;}
    enMapLayerType getSelectMapLayerType() {return m_layer_type_select;}

    RoutePlanLayer *getRoutePlanLayer() {return m_route_plane_Layer_ptr.get();}
    SearchPoiLayer *getSearchPoiLayer() {return m_search_poi_Layer_ptr.get();}
    LocationLayer *getLocationLayer() {return m_location_Layer_ptr.get();}
    RenderEngineLayer *getRenderEngineLayer() {return m_render_map_layer_ptr.get();}

    // 摄像机(Camera)相关
    bool setProjection(const QString &projectionName);
	MapCamera * getMapCamera();
    void setWorldCenter(const map_engine::RGeoPoint* pos, bool set_Scale= true, int animation = 500);
    void getWorldCenter(map_engine::RGeoPoint * center) const;
    void updateScale(double &scale);
    void onFlyTo(const map_engine::RGeoPoint* pos);

    float getLastRotationValue();
    void setLastRotationValue(float value);

    void startNavi();
    void reloadRouteDataLayer();

private:
    void addBackgroundMapLayer(MapLayer *layer);
    void drawCursor(QPainter& painter);
    void onClearMaplayer();
    void updataTurnMarkDialogPos(MapCamera& camera);
    void checkKeys();
    KeyRotationStatus checkKeyCombination();
    void handleUpDownCombination(KeyRotationStatus key_status);



signals:
	void cameraChanged(const MapCamera* c);
    void layerListChanged(const QList<MapLayer*>* layerList);
    void updataTurnMarkInfoDialogPos();
    void updataExpandViewDialogPos();

public slots:
    void ForceUpdateWidget();

protected:
    virtual void initializeGL();
    virtual void resizeGL(int w, int h);
    virtual void paintGL();

	// 鼠标拖放与滚轮动作响应
	virtual void wheelEvent(QWheelEvent *e);

	virtual void mousePressEvent(QMouseEvent *e);
	virtual void mouseMoveEvent(QMouseEvent *e);
	virtual void mouseReleaseEvent(QMouseEvent *e);
	virtual void mouseDoubleClickEvent(QMouseEvent *e);
    
    virtual void keyPressEvent(QKeyEvent *keyevent);
    virtual void keyReleaseEvent(QKeyEvent *event);


    void checkCombination();

private:
    QString m_projectionName;
    bool m_cursorVisible;
    MapCamera* m_camera;
	bool m_mouseDown;
    map_engine::RGeoPoint m_mousePressWorldPos;

    int x_;
    int y_;
    int h_;

    bool is_follow_car_;
    bool is_head_up_;
    MapDataAdapter* m_mapCacheData_;
    enMapLayerType m_layer_type_select;

    QList<MapLayer*> m_layers;    
    QList<MapLayer*> m_background_layers;

    std::shared_ptr<RenderEngineLayer> m_render_map_layer_ptr;
    std::shared_ptr<RoutePlanLayer> m_route_plane_Layer_ptr;
    std::shared_ptr<SearchPoiLayer> m_search_poi_Layer_ptr;
    std::shared_ptr<LocationLayer> m_location_Layer_ptr;

    QSet<int> keysPressed;
    QTimer *m_rotationTimer;
    KeyRotationStatus m_currentRotationStatus;
    float m_last_rotation_value;

    std::mutex m_mutex;


};

#endif // MAP_WIDGET_H
