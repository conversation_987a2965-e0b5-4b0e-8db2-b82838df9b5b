﻿
#include "main_window.h"
#include <iostream>
#include "ui_main_window.h"

#include "feature_input_info_dialog.h"
#include "geo_point.h"
#include "turn_mark_info_dialog.h"
#include "map_engine_manage.h"
#include "map_layer.h"
#include "route_plan_layer.h"
#include "search_poi_layer.h"
#include "map_render_widget.h"
#include "map_widget_manage.h"
#include "preferences_dialog.h"
#include "tool_box_controller.h"
#include "turn_mark_info_dialog.h"
#include "expand_view_dialog.h"
#include "position_dialog.h"
#include "render_engine_layer.h"
#include "location_layer.h"

using namespace aurora::search;

#define DataIndexRole	(Qt::UserRole + 1)

MainWindow::MainWindow(QWidget *parent)
	: QMainWindow(parent)
    , ui(new Ui::MainWindowClass())
    , m_parallel_dlg(nullptr)
    , m_better_route_dlg(nullptr)
    , m_viewLayer(nullptr)
    , m_routeLayer(nullptr)
    , m_viewE<PERSON>ineLayer(nullptr)
    , m_northUp(true)

{
    m_last_select_region = 0;
    ui->setupUi(this);

    m_toolBoxController = new ToolBoxController(ui->toolBox);

    setupToolBoxUi();
    setupRouteUi();
    setupSearchUi();
    setupSimulationUi();
    setupLoctionUi();

    setMapLayerTreeDisplayMenu(false);


//	m_centerPositionRefreshTimer = new QTimer(this);
//	connect(m_centerPositionRefreshTimer, SIGNAL(timeout()), this, SLOT(onRefreshCenterPosition()));
//	m_centerPositionRefreshTimer->start(1000);

//	m_autoMoveTimer = new QTimer(this);
//	connect(m_autoMoveTimer, SIGNAL(timeout()), this, SLOT(onAutoMove()));
//	m_autoMoveTimer->start(10000);



    connect(ui->mapWidget, SIGNAL(updataTurnMarkInfoDialogPos()), this, SLOT(onUpdataTurnMarkInfoDialogPos()));
    connect(ui->mapWidget, SIGNAL(updataExpandViewDialogPos()), this, SLOT(onUpdataExpandViewDialogPos()));

    //for map widget manage
    connect(ui->mapWidget, SIGNAL(layerListChanged(const QList<MapLayer*>*)), this, SLOT(onLayerListChanged(const QList<MapLayer*>*)));
    connect(ui->mapWidget->getRoutePlanLayer(), &RoutePlanLayer::updatePoiSearchPos, this, &MainWindow::onUpdatePoiSearchPos, Qt::QueuedConnection);
    //connect(ui->mapWidget, &MapWidget::cameraChanged, this, &MainWindow::onMapCameraChanged);


    MapEngineManage * mm = MapEngineManage::GetInstance();
    mm->m_loction_control_ptr->setLoctionLayer(ui->mapWidget->getLocationLayer());
    mm->m_routingCenter_ptr->setMapLayer(ui->mapWidget);
    connect(mm, SIGNAL(poiQueryResultChanged()), this, SLOT(onPoiResultChanged()));

    connect(mm->m_routingCenter_ptr.get(), &RoutingControl::routePlanChanged, this, &MainWindow::onRoutePlanChanged, Qt::QueuedConnection);
    connect(mm->m_routingCenter_ptr.get(), &RoutingControl::routeResultChanged, this, &MainWindow::onRouteResultChanged, Qt::QueuedConnection);

    connect(ui->splitter, &QSplitter::splitterMoved, this, &MainWindow::onSplitterMoved);

    RGeoPoint map_center(mm->m_def_longitude, mm->m_def_latitude);
    ui->mapWidget->setWorldCenter(&map_center);

    //loadCityList();
    createTurnMarkView();
    createExpandViewDialog();
    updateInfoTree();

}

MainWindow::~MainWindow()
{

//    if (m_centerPositionRefreshTimer) {
//        delete m_centerPositionRefreshTimer;
//        m_centerPositionRefreshTimer = nullptr;
//    }

//    if (m_autoMoveTimer) {
//        delete m_autoMoveTimer;
//        m_autoMoveTimer = nullptr;
//    }

    if (ui) {
       delete ui;
       ui = nullptr;
    }

    if (m_toolBoxController) {
        delete m_toolBoxController;
        m_toolBoxController = nullptr;
    }

    MapEngineManage::Close();
}

void MainWindow::loadPreferencesConfig()
{
}

void MainWindow::setToolboxWidth(int width)
{
    int totalWidth = this->width();
    int mapWidgetWidth = totalWidth - width;

    ui->splitter->setSizes({width, mapWidgetWidth});
    std::cout << "before : toolBoxWidth = " << width << "mapWidgetWidth = " << mapWidgetWidth << std::endl;

}

void MainWindow::onSplitterMoved(int pos, int index)
{
    qDebug() << "onSplitterMoved: pos = " << pos <<" , index = " << index;
    // 处理分割器移动
    QList<int> sizes = ui->splitter->sizes();
    int toolbox_width = sizes.at(0);
    //int MapWidget_width = sizes.at(1);

    QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);
    s.beginGroup("ui");
    s.setValue("toobox_width", toolbox_width);
    s.endGroup();
}


void MainWindow::setupToolBoxUi()
{
    ui->viewPage->setVisible(false);
    ui->page->setVisible(false);
    ui->tmcPage->setVisible(false);
    ui->locationTestPage->setVisible(false);

    QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);
    s.beginGroup("ui");
    QStringList pages = s.value("toolbox_hidden_pages").toStringList();
    int toobox_width = s.value("toobox_width").toInt();
    s.endGroup();

    m_toolBoxController->applyHiddenItemTextList(pages);
    if (toobox_width > 0) {
        setToolboxWidth(toobox_width);
    } else {
       setToolboxWidth(TOOLBOX_WIDTH);
    }

    ui->toolBox->setCurrentIndex(0);
}

void MainWindow::setupSearchUi()
{
    ui->cityComboBox->setVisible(false);
    ui->typeComboBox->setVisible(false);
    ui->onlyRightComboBox->setVisible(false);
    ui->showTypeComboBox->setVisible(false);

    ui->cityLabel->setVisible(false);
    ui->typeLabel->setVisible(false);
    ui->onlyRightLabel->setVisible(false);
    ui->showTypeLabel->setVisible(false);

    ui->stopSearchTestPushButton->setVisible(false);
    ui->fileSearchTestPushButton->setVisible(false);
    ui->fileSugTestButton->setVisible(false);
    ui->cancelPushButton->setVisible(false);

    ui->loadMorePushButton->setVisible(false);
    ui->loadPreviousPushButton->setVisible(false);

    ui->radiusLineEdit->setText("8000");
    ui->widthLineEdit->setText("10");
}

void MainWindow::setupRouteUi()
{

    //remove other tab
    ui->tabWidget->removeTab(3);
    ui->tabWidget->removeTab(3);

    ui->actionCopyRouteCase->setVisible(false);
    ui->actionPaste->setVisible(false);
    ui->actionReverseLink->setVisible(false);
    ui->actionMultiRoute->setVisible(false);
    ui->actionAvoidYunmapRoute->setVisible(false);
    ui->actionCityTest->setVisible(false);
    ui->actionRandomTest->setVisible(false);
    ui->actionCaseTest->setVisible(false);
    ui->actionFileTest->setVisible(false);
    ui->actionStopTest->setVisible(false);

    QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);
    s.beginGroup("map");
    bool compareWithBaidu = s.value("compare_with_baidu", false).toBool();
    bool compareWithAmap = s.value("compare_with_amap", false).toBool();
    ui->actionCompareWithBaidu->setChecked(compareWithBaidu);
    ui->actionCompareWithAmap->setChecked(compareWithAmap);
    s.endGroup();
}

void MainWindow::setupSimulationUi()
{
    ui->loadFilePushButton->setVisible(false);
    ui->loadFolderPushButton->setVisible(false);
    ui->logFileComboBox->setVisible(false);
    ui->idLineEdit->setVisible(false);
    ui->dateTimeEdit->setVisible(false);
    ui->prevPtPushButton->setVisible(false);
    ui->nextPtPushButton->setVisible(false);
    ui->sendPtPushButton->setVisible(false);
    ui->showPointsCheckBox->setVisible(false);
    ui->showSelectedFileCheckBox->setVisible(false);
    ui->showSlopeCheckBox->setVisible(false);
    ui->filterParkCheckBox->setVisible(false);
    ui->enableLocalSimCheckBox->setVisible(false);
    ui->loopCheckBox->setVisible(false);
    ui->quickMatchPushButton->setVisible(false);
    ui->ipLineEdit->setVisible(false);
    ui->ipLabel->setVisible(false);
    ui->verticalScrollBar->setVisible(false);
    ui->autoCheckBox->setVisible(false);
    ui->rateComboBox->setVisible(false);
    ui->clipPushButton->setVisible(false);
    //ui->infoListWidget->setVisible(false);

    QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);

    s.beginGroup("simulation");
    ui->PosNoiseMeanlineEdit->setText(s.value("position_noise_mean", 0.0).toString());
    ui->PosNoiseStdlineEdit->setText(s.value("position_noise_stddev", 0.0).toString());
    ui->HeadingNoiseMeanlineEdit->setText(s.value("heading_noise_mean", 0.0).toString());
    ui->HeadingNoiseStdlineEdit->setText(s.value("heading_noise_stddev", 0.0).toString());
    ui->SampleIntervallineEdit->setText(s.value("sample_interval", 1.0).toString());
    int index = s.value("play_interval", 4).toInt();
    s.endGroup();

    ui->PlaybackIntervalcomboBox->addItem("1X", QVariant(1));
    ui->PlaybackIntervalcomboBox->addItem("2X", QVariant(2));
    ui->PlaybackIntervalcomboBox->addItem("4X", QVariant(4));
    ui->PlaybackIntervalcomboBox->addItem("6X", QVariant(6));
    ui->PlaybackIntervalcomboBox->addItem("10X", QVariant(10));
    ui->PlaybackIntervalcomboBox->addItem("16X", QVariant(18));
    ui->PlaybackIntervalcomboBox->addItem("24X", QVariant(24));
    ui->PlaybackIntervalcomboBox->addItem("32X", QVariant(32));
    ui->PlaybackIntervalcomboBox->addItem("64X", QVariant(64));

    ui->PlaybackIntervalcomboBox->setCurrentIndex(index);

}

void MainWindow::setupLoctionUi()
{
    ui->logTypeComboBox->setVisible(false);
    ui->label_11->setVisible(false);
    ui->carAngleLineEdit->setVisible(false);
    ui->setCarPosPushButton->setVisible(false);
    ui->loadCompareLogPushButton->setVisible(false);
    ui->doOtherRoadPushButton->setVisible(false);
    ui->showCcpCheckBox->setVisible(false);
    ui->showCompareCcpCheckBox->setVisible(false);
    ui->locationTimeConsumptionLabel->setVisible(false);
    ui->srcFileLineEdit->setVisible(false);
    ui->selectFilePushButton->setVisible(false);
    ui->transferFilePushButton->setVisible(false);
    ui->transferTimeConsumptionLabel->setVisible(false);
    ui->TransferTimeLabel->setVisible(false);
    ui->locationTimeLabel->setVisible(false);
    ui->nextPushButton->setVisible(false);

    ui->showDrCheckBox->setCheckState(Qt::Checked);
    ui->showGpsCheckBox->setCheckState(Qt::Checked);

}

static void _setDir(const QString& str, QTreeWidgetItem* item)
{
    QDir dir;
    dir.setPath(str);
    dir.setFilter(QDir::Files | QDir::Dirs | QDir::NoSymLinks | QDir::NoDotAndDotDot);
    QDirIterator iter(dir, QDirIterator::Subdirectories);
    while (iter.hasNext())
    {
        iter.next();
        QFileInfo info = iter.fileInfo();
        if (info.isDir() && str == info.path())
        {
            QTreeWidgetItem* dirItem = new QTreeWidgetItem(item);
            dirItem->setText(0, info.fileName());
            _setDir(info.filePath(), dirItem);
        }
        QString path = info.path();
        if (info.isFile() && str == info.path())
        {
            QTreeWidgetItem* fileItem = new QTreeWidgetItem(item);
            fileItem->setText(0, info.fileName());
        }
    }
}

void MainWindow::traverseDirectories(const QString& path, QStringList &fullPathList, QStringList &dirNameList, int depth)
{
    QDir dir(path);
    if (!dir.exists()) {
        qDebug() << "Error: Directory" << path << "does not exist!";
        return;
    }

    // 设置过滤条件：只显示文件夹，不显示.和..
    QDir::Filters filters = QDir::Dirs | QDir::NoDotAndDotDot;

    // 获取文件夹列表
    QStringList currentDirs = dir.entryList(filters);

    // 遍历当前层级的所有文件夹
    for (const QString& dirName : currentDirs) {
        QString fullPath = dir.filePath(dirName);

        // 添加到完整路径列表
        fullPathList.append(fullPath);

        // 添加到文件夹名称列表
        dirNameList.append(dirName);

        // 打印带缩进的完整路径
        qDebug() << QString(depth * 4, ' ').toUtf8().constData()
                 << "[" << dirName << "]"
                 << fullPath;

//        // 递归遍历子文件夹
//        traverseDirectories(fullPath, fullPathList, dirNameList, depth + 1);
    }
}


void MainWindow::onReloadMapViewer(const QString *dataPath )
{
    if (!dataPath)
        return;

    MapEngineManage* dc = MapEngineManage::GetInstance();

    //for clear
    onActionDeleteRouteTriggered();  //clear route & Simulation
    clearPoiResult(); //clear poi search
    dc->onDestroyMapEngine(); //destroy map engine

    //for init
    dc->InitMapEngine(dataPath);

    RGeoPoint pos(dc->m_def_longitude, dc->m_def_latitude);
    ui->mapWidget->getMapCamera()->setWorldCenter(&pos);
    //for reload
    loadCityList(); //load city list

    //for widget
    g_app->hideAllWidget();
}

int MainWindow::checkLastRegionCode(QStringList &data_path_list)
{

    QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);
    s.beginGroup("map_data");
    QString region_code = s.value("region_code", "").toString();
    s.endGroup();

    if (region_code.isEmpty())
        return 0;

    for (int i = 0; i < data_path_list.size(); i++)
    {
        QString config_file_Path = data_path_list[i] + "/config.ini";
        if (QFile(config_file_Path).exists())
        {
            QSettings settings(config_file_Path, QSettings::IniFormat);
            QString region_key = settings.value("region_code", "").toString();
            if (region_code == region_key)
                return i;
        }
    }

    return 0;
}

void MainWindow::updateInfoTree()
{

    MapEngineManage* dc = MapEngineManage::GetInstance();

    ui->dataInfoTree->clear();

    QString data_path = dc->m_base_data_path;
    if (!data_path.isEmpty())
    {
        QStringList dirNameList;
        traverseDirectories(data_path, m_data_path_list, dirNameList);

        if (m_data_path_list.size() == 0 || dirNameList.size() == 0)
            return;

        m_last_select_region = checkLastRegionCode(m_data_path_list); //get last save region code

        QTreeWidgetItem* rootItem = nullptr;
        for (int i = 0; i < m_data_path_list.size(); ++i) {
            rootItem = new QTreeWidgetItem(ui->dataInfoTree);
            rootItem->setText(0, dirNameList.at(i));
            _setDir(m_data_path_list.at(i), rootItem);

            if (i == m_last_select_region) {
                rootItem->setSelected(true);
                onReloadMapViewer(&m_data_path_list.at(i));
                ui->mapWidget->initRouteDataMapLayer();
            }
        }

    }
}


void MainWindow::onDataBaseItemDoubleClicked(QTreeWidgetItem* item, int id)
{
    bool isTopLevelItem = (item->parent() == nullptr);
    if (!isTopLevelItem)
        return;

    int index = ui->dataInfoTree->indexOfTopLevelItem(item);
    if (m_last_select_region == index)
        return;

    m_last_select_region = index;

    QString data_path = m_data_path_list.at(index);
    qDebug() << "onDataBaseItemClicked index = " << index << ", path = " << data_path;

    onReloadMapViewer(&data_path);

    //for render map
    if (ui->mapWidget->getRenderEngineLayer())
        ui->mapWidget->getRenderEngineLayer()->onReloadDisplayData(data_path.toStdString());

}

void MainWindow::createTurnMarkView()
{
    if (m_turn_mark_dlg_ptr == nullptr)
    {
        m_turn_mark_dlg_ptr = std::make_shared<TurnMarkInfoDialog>(this);
        assert(m_turn_mark_dlg_ptr != nullptr);
    }
    Qt::WindowFlags flags = m_turn_mark_dlg_ptr->windowFlags() | Qt::Popup;
    m_turn_mark_dlg_ptr->setWindowFlags(flags);
    m_turn_mark_dlg_ptr->setVisible(false);
}

void MainWindow::OnTurnMarkDialogDestroy()
{
    m_turn_mark_dlg_ptr = nullptr;
}

void MainWindow::showTurnMarkView()
{

    if (m_turn_mark_dlg_ptr->isHidden()) {
        //m_turn_mark_dlg_ptr->move(270, 120);
        m_turn_mark_dlg_ptr->setVisible(true);
    }
    else {
        m_turn_mark_dlg_ptr->update();
    }
}

void MainWindow::hideTurnMarkView()
{
    if (m_turn_mark_dlg_ptr != nullptr) {
        m_turn_mark_dlg_ptr->setVisible(false);
    }
}

void MainWindow::onUpdataTurnMarkInfoDialogPos()
{
    if (m_turn_mark_dlg_ptr) {
        int x = this->width() - 360;
        int y = 200;
        m_turn_mark_dlg_ptr->move(x, y);
    }

}

void MainWindow::createExpandViewDialog()
{
    if (m_expand_view_dlg_ptr == nullptr)
    {
        m_expand_view_dlg_ptr = std::make_shared<ExpandViewDialog>(ui->mapWidget);
        assert(m_expand_view_dlg_ptr != NULL);
        m_expand_view_dlg_ptr->setVisible(false);
    }
}

// 显示路口扩大图
void MainWindow::showExpandView()
{
    if (m_expand_view_dlg_ptr->isHidden()) {
         //m_expand_view_dlg->resize(400, 480);
        m_expand_view_dlg_ptr->setVisible(true);
    }
    else
        m_expand_view_dlg_ptr->update();
}

// 隐藏路口扩大图
void MainWindow::hideExpandView()
{
    if (m_expand_view_dlg_ptr != NULL) {
        m_expand_view_dlg_ptr->close();
    }
}

void MainWindow::onUpdataExpandViewDialogPos()
{
    if (m_expand_view_dlg_ptr) {
//       int x = this->width() - 760;
//       int y = 200;
       m_expand_view_dlg_ptr->move(0, 0);
    }


}

void MainWindow::loadCityList()
{
    enum {
        LongitudeRole = Qt::UserRole,
        LatitudeRole
    };

    aurora::parser::DataProvider data_provider = MapEngineManage::GetInstance()->GetDataProvider();
    ui->cityTree->clear();
    ui->cityTree->expandToDepth(1);

//    QFont font = ui->cityTree->font();
//    font.setPointSize(15);
//    ui->cityTree->setFont(font);

    QTreeWidgetItem* item = nullptr;
    QTreeWidgetItem* childItem = nullptr;
    QTextCodec *tc = QTextCodec::codecForName("UTF-8");
    for (auto &city : data_provider.GetAdminInfo())
    {
        if (m_pre_province_name != city.province_name) {

            item = new QTreeWidgetItem(ui->cityTree);
            QString str = QString("%1").arg(tc->toUnicode(city.province_name.c_str()));
            item->setText(0, str);

            item->setData(0, LongitudeRole, city.center.x());
            item->setData(0, LatitudeRole, city.center.y());

            m_pre_province_name = city.province_name;

            std::cout << "province_name" << city.province_name << "log : " << city.center.x() << "lat : " << city.center.y() << std::endl;
            continue;
        }

        if (item) {
            childItem = new QTreeWidgetItem(item);
            QString str = QString("%1").arg(tc->toUnicode(city.city_name.c_str()));
            childItem->setText(0, str);

            childItem->setData(0, LongitudeRole, city.center.x());
            childItem->setData(0, LatitudeRole, city.center.y());
            std::cout << "city_name" << city.city_name << "log : " << city.center.x() << "lat : " << city.center.y() << std::endl;
        }
    }

    // 连接点击信号到lambda函数
    connect(ui->cityTree, &QTreeWidget::itemClicked, this, [this](QTreeWidgetItem* item, int column) {
        // 获取经纬度数据
        double longitude = item->data(0, LongitudeRole).toDouble();
        double latitude = item->data(0, LatitudeRole).toDouble();

        RGeoPoint pos(longitude, latitude);
        ui->mapWidget->onFlyTo(&pos);
        std::cout << "Clicked "  << "log : " << longitude << "lat : " << latitude << std::endl;
    });
}

void MainWindow::onActionOpenTriggered()
{
    return;

    QString default_open_path;
    if (!m_last_open_folder_Path.isEmpty()) {
        default_open_path = m_last_open_folder_Path;
    } else {
        default_open_path = QDir::homePath(); // 默认打开的路径（此处为用户主目录）
    }


    QString fileName = QFileDialog::getOpenFileName(
        this,                    // 父窗口指针
        "open file",            // 对话框标题
        default_open_path,
        "配置文件 (*.ini)"  // 文件过滤器
    );

    if (fileName.isEmpty())
        return;

    QString appPath = QCoreApplication::applicationDirPath();
    if (MapEngineManage::GetInstance()->InitMapEngine(&appPath)) {
        ui->mapWidget->initRouteDataMapLayer();
    }

    m_last_open_folder_Path = QFileInfo(fileName).absolutePath();

}

void MainWindow::onShowToolBoxTriggered(bool checked)
{
   ui->toolBox->setVisible(checked);
}

void MainWindow::onActionSelectFeatureTriggered()
{
    ui->toolBox->setCurrentIndex(4);
}

void MainWindow::onActionPositionTriggered()
{

//    if (ui->mapWidget) {
//        MapEngineManage * dc = MapEngineManage::GetInstance();
//        RGeoPoint pos(dc->m_def_longitude, dc->m_def_latitude);
//        ui->mapWidget->setWorldCenter(&pos);
//    }

    map_engine::RGeoPoint pt;

    if (PositionDialog::get(this, &pt))
        ui->mapWidget->setWorldCenter(&pt, true);

}

void MainWindow::onActionSetStartTriggered()
{
    RGeoPoint pos;
    ui->mapWidget->getWorldCenter(&pos);
    ui->mapWidget->getRoutePlanLayer()->onSetStartPoint(&pos);
}

void MainWindow::onActionSetEndTriggered()
{
    RGeoPoint pos;
    ui->mapWidget->getWorldCenter(&pos);
    ui->mapWidget->getRoutePlanLayer()->onSetEndPoint(&pos);
}

void MainWindow::onActionAddWayPointTriggered()
{
    RGeoPoint pos;
    ui->mapWidget->getWorldCenter(&pos);
    ui->mapWidget->getRoutePlanLayer()->onSetWayPoint(&pos);
}

void MainWindow::onActionDeleteRouteTriggered()
{
    MapEngineManage::GetInstance()->m_routingCenter_ptr->clearRoutePathAndPoints();
    ui->routePlanList->clear();
    ui->routeResultList->clear();
    endSimulation();
}

void MainWindow::onActionFileTestTriggered()
{
    QString fileName = QFileDialog::getOpenFileName(this, tr("Open File"), QString(),
        tr("route_test_input.txt;(*.txt)"));

    if (fileName.isEmpty())
        return;

    std::shared_ptr<RoutingControl> rc = MapEngineManage::GetInstance()->m_routingCenter_ptr;
    if (rc)
        rc->setFileTestData(fileName);
}

void MainWindow::StartDrive()
{

}

void MainWindow::startCaclPath()
{
    onRoutePlanChanged();
    onActionPathCalculationTriggered();
}

void MainWindow::onActionPathCalculationTriggered()
{
   endSimulation();
   MapEngineManage *mm = MapEngineManage::GetInstance();
   mm->m_routingCenter_ptr->clearRoutePath();
   mm->m_routingCenter_ptr->StartPathCalculation();

   int index = m_toolBoxController->indexByName(QString("routingPage"));
   if (index != -1)
      m_toolBoxController->setCurrentIndex(index);
}


MapWidget* MainWindow::getMapWidget()
{
    return ui->mapWidget;
}

void MainWindow::onLayerListContextMenu(const QPoint &pos)
{
    QListWidgetItem* item = ui->mapLayerList->itemAt(pos);
    if (item == NULL)
        return;

    bool ok;
    int index = item->data(Qt::UserRole).toInt(&ok);
    if (!ok)
        return;

    auto layerList = ui->mapWidget->getLayerList();
    if (index < 0 || layerList.size() <= index)
        return;

    layerList.at(index)->onContextMenuRequested(ui->mapLayerList->mapToGlobal(pos));
}

void MainWindow::onLayerListChanged(const QList<MapLayer*> *layerList)
{
    ui->mapLayerList->clear();

    for (int index = 0; index < layerList->size(); ++index)
    {
        MapLayer* layer = layerList->at(index);
        if (layer->getTitle().isEmpty())
            continue;

        QListWidgetItem* item = new QListWidgetItem(ui->mapLayerList);
        item->setText(layer->getTitle());
        if (layer->getMapLayerType() == kMapLayer_Render_map) {
            item->setCheckState( Qt::Checked );
        } else {
            item->setCheckState( Qt::Unchecked);
        }
        item->setData(Qt::UserRole, QVariant::fromValue(index));
    }

}

void MainWindow::onLayerListItemChanged(QListWidgetItem *item)
{
    bool ok;
    int index = item->data(Qt::UserRole).toInt(&ok);
    if (!ok)
        return;

    switch (item->checkState())
    {
    case Qt::Unchecked:
        ui->mapWidget->setMapLayerVisibilityByIndex(index, false);
        break;
    case Qt::Checked:
    {
        ui->mapWidget->setMapLayerVisibilityByIndex(index, true);
//        ui->mapWidget->setSelectMapLayerType(static_cast<enMapLayerType>(index));
//        item->setSelected(true);

        if (static_cast<enMapLayerType>(index) == kMapLayer_Render_map) {
            for (int i = 0; i < ui->mapLayerList->count(); ++i) {
                QListWidgetItem* itr = ui->mapLayerList->item(i);
                if (item != itr) {
                    itr->setCheckState(Qt::Unchecked);
                    ui->mapWidget->setMapLayerVisibilityByIndex(i, false);
                }
            }

        } else {
            int id = static_cast<int>(kMapLayer_Render_map);
            ui->mapLayerList->item(id)->setCheckState(Qt::Unchecked);
            ui->mapWidget->setMapLayerVisibilityByIndex(id, false);
        }
    }
        break;
    default:
        break;
    }


}

void MainWindow::setMapLayerTreeDisplayMenu( bool isDisplay)
{
    if (isDisplay) {
        ui->mapLayerTree->show();
    }
    else {
        ui->mapLayerTree->hide();
    }
}

void MainWindow::on_mapLayerList_itemClicked(QListWidgetItem *item)
{
    bool ok;
    int index = item->data(Qt::UserRole).toInt(&ok);
    if (!ok)
        return;

    if (item->isSelected())
        ui->mapWidget->setSelectMapLayerType(static_cast<enMapLayerType>(index));
}

void MainWindow::onUpdatePoiSearchPos(double lng, double lat)
{
    ui->LnglineEdit->setText(QString::number(lng, 'f', 6));
    ui->LatlineEdit->setText(QString::number(lat, 'f', 6));
}

void MainWindow::onPoiSearch()
{
    resetPoiResult();

    if (ui->keywordLineEdit->text().isEmpty() && ui->CategoryLineEdit->text().isEmpty())
        return;

    const auto request = std::make_shared<aurora::search::SearchByTextRequest>();

    request->query = ui->keywordLineEdit->text().toStdString();
    double lng,lat;
    if (!ui->LnglineEdit->text().isEmpty() && !ui->LatlineEdit->text().isEmpty()) {
        lng = ui->LnglineEdit->text().toDouble();
        lat = ui->LatlineEdit->text().toDouble();
    } else {
        RGeoPoint worldCenter;
        ui->mapWidget->getMapCamera()->getWorldCenter(&worldCenter);
        lng = worldCenter.CoordX_;
        lat = worldCenter.CoordY_;
    }
    request->location_restriction = CreateCircularBounds({lng, lat}, ui->radiusLineEdit->text().toDouble());
    request->search_mode = ui->modeComboBox->currentIndex() == 0 ? SearchMode::kOnline : SearchMode::kOffline;
    request->page_size = ui->widthLineEdit->text().isEmpty() ? 10 : ui->widthLineEdit->text().toInt();

    if (!ui->CategoryLineEdit->text().isEmpty()) {
        request->included_categories.push_back(ui->CategoryLineEdit->text().toStdString());
    }

    MapEngineManage::GetInstance()->onRequestPoiSearch(request);

    onUpdatePoiSearchStatusInfo(tr("searching ..."));

}

void MainWindow::onUpdatePoiSearchStatusInfo(QString info)
{
    ui->timeLabel->setText(info);
}

void MainWindow::updatePoiResultList(std::shared_ptr<SearchByTextResponse> &response)
{
    if (!response)
        return;

    ui->poiResultListWidget->clear();

    int len = response->place_briefs.size();
    for (int i = 0; i < len; i++)
    {
        PlaceBrief &poi = response->place_briefs.at(i);
        QListWidgetItem* item = new QListWidgetItem(ui->poiResultListWidget);
        QString text = tr("%1. ID:%2\nName: %3\n")
            .arg(i + 1)
            .arg(QString::fromStdString(poi.id))
            .arg(QString::fromStdString(poi.name));

        item->setText(text);
        item->setData(DataIndexRole, QVariant::fromValue(i));
    }
    //Search completed, returning 10 results.
    ui->poiResultListWidget->scrollToTop();
    ui->mapWidget->getSearchPoiLayer()->setSearchResult(response);

}

void MainWindow::resetPoiResult()
{
   MapEngineManage::GetInstance()->clearPoiResult();
   onUpdatePoiSearchStatusInfo(tr("N/A"));
   ui->poiResultListWidget->clear();
   ui->mapWidget->getSearchPoiLayer()->setSearchResult(nullptr);
}


void MainWindow::clearPoiResult()
{

    resetPoiResult();
    ui->keywordLineEdit->setText("");

}


void MainWindow::onSelectFileTestButtonClicked()
{
    QString fileName = QFileDialog::getOpenFileName(this, tr("Open File"), QString(),
        tr("route_test_input.txt;(*.txt)"));

    if (fileName.isEmpty())
        return;

    ui->FIleTestlineEdit->setText(fileName);
    ui->RouteFileTestStatuslabel->setText("");
}

void MainWindow::onUpdateRouteFileTestResultInfo(QString info)
{
    ui->RouteFileTesttextEdit->append(info);
    ui->RouteFileTesttextEdit->moveCursor(QTextCursor::End);
}

void MainWindow::onUpdateRouteFileTestLableInfo(QString info)
{
    ui->RouteFileTestStatuslabel->setText(info);

}

void MainWindow::onRouteTestStartClickSlot()
{
    if (ui->FIleTestlineEdit->text().isEmpty()) {
        ui->RouteFileTestStatuslabel->setText("The file path is empty!");
        return;
    }

    ui->RouteFileTesttextEdit->clear();
    ui->RouteFileTestStatuslabel->setText("");

    std::shared_ptr<RoutingControl> rc = MapEngineManage::GetInstance()->m_routingCenter_ptr;
    if (rc) {
       rc->m_is_file_test_stop = false;
       QString file_path = ui->FIleTestlineEdit->text();
       if (rc->setFileTestData(file_path))
           ui->RouteTestStartpushButton->setEnabled(false);
    }
}

void MainWindow::onRouteTestStopClickSlot()
{
    ui->RouteTestStartpushButton->setEnabled(true);

    std::shared_ptr<RoutingControl> rc = MapEngineManage::GetInstance()->m_routingCenter_ptr;
    if (rc) {
       rc->m_is_file_test_stop = true;
    }
}

void MainWindow::startSimulation()
{
    endSimulation();
    setSimulatorConfig();

    MapEngineManage *mm = MapEngineManage::GetInstance();
    if (mm->m_routingCenter_ptr && mm->m_loction_control_ptr) {

        if (mm->m_loction_control_ptr->startSimulation(mm->m_routingCenter_ptr->getRoutePathResult()))
        {
            mm->setNavigationMode(aurora::guide::kNavigationModeSimulation);
            g_app->hideAllWidget();
            showTurnMarkView();
            mm->setCurMapMode(MM_SIMUL_NAVI);
            mm->m_routingCenter_ptr->clearRoutePath(); //first get select_id, after clear path

            int index = m_toolBoxController->indexByName(QString("simulationPage"));
            if (index != -1)
               m_toolBoxController->setCurrentIndex(index);

            ui->mapWidget->startNavi();
        }

    }
}

void MainWindow::endSimulation()
{
    MapEngineManage *mm = MapEngineManage::GetInstance();
    mm->setCurMapMode(MM_MAP_VIEW);
    mm->stopNavigation();
    mm->stopSimulation();
    mm->m_loction_control_ptr->onClearDriveView();
    hideTurnMarkView();
}

void MainWindow::onRoutingRuleComboBoxCurrentIndexChanged(int index)
{
//    bool ok;
//    uint8 ruleIdx = ui->routingRuleComboBox->itemData(index).toInt(&ok);
//    if (!ok)
//        return;

    MapEngineManage::GetInstance()->m_routingCenter_ptr->setRouteRule(index);

}

 void MainWindow::onActionPreferencesTriggered()
{
     PreferencesDialog dlg(this);
     dlg.exec();
}

 void MainWindow::onActionCompareWithBaiduTriggered(bool checked)
 {
     QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);
     s.beginGroup("map");
     s.setValue("compare_with_baidu", checked);
     s.endGroup();

     std::shared_ptr<RoutingControl>  rc = MapEngineManage::GetInstance()->m_routingCenter_ptr;
     if (rc)
        rc->setCompareWithBaidu(checked);
 }

 void MainWindow::onActionCompareWithAmapTriggered(bool checked)
 {
     QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);
     s.beginGroup("map");
     s.setValue("compare_with_amap", checked);
     s.endGroup();

     std::shared_ptr<RoutingControl>  rc = MapEngineManage::GetInstance()->m_routingCenter_ptr;
     if (rc)
        rc->setCompareWithAmap(checked);
 }

 void MainWindow::onRoutePlanChanged()
 {
     std::shared_ptr<RoutingControl> rc_ptr = MapEngineManage::GetInstance()->m_routingCenter_ptr;
     if (!rc_ptr)
         return;

     ui->routePlanList->clear();

     auto ptr = ui->mapWidget->getRoutePlanLayer();
     RoutePointSet *start_point = ptr->getRoutePoint(RPT_START_POINT);
     RoutePointSet *end_point = ptr->getRoutePoint(RPT_END_POINT);
     RoutePointSet *way_point = ptr->getRoutePoint(RPT_WAY_POINT);

     QListWidgetItem* item = nullptr;

     //start point
     if (start_point && start_point->size() > 0) {
        auto &pos = start_point->at(0).latlon_pos;
        item = new QListWidgetItem(QIcon(":/MainWindow/resources/s.png"), "", ui->routePlanList);
        item->setText(tr("Start (%1, %2)").arg(QString::number(pos.CoordX_, 'f', 6)).arg(QString::number(pos.CoordY_, 'f', 6)));
        QVariant coordData = QVariant::fromValue(QPointF(pos.CoordX_, pos.CoordY_));
        item->setData(Qt::UserRole, coordData);
        item->setData(Qt::UserRole + 1, QVariant::fromValue(int(RPT_START_POINT)));

     }

     // Way points
     if (way_point && way_point->size() > 0) {
         for (size_t i = 0; i < way_point->size(); ++i) {
             auto &pos = way_point->at(i).latlon_pos;
             item = new QListWidgetItem(QIcon(":/MainWindow/resources/w.png"), "", ui->routePlanList);
             item->setText(tr("waypoint (%1, %2)").arg(QString::number(pos.CoordX_, 'f', 6)).arg(QString::number(pos.CoordY_, 'f', 6)));
             QVariant coordData = QVariant::fromValue(QPointF(pos.CoordX_, pos.CoordY_));
             item->setData(Qt::UserRole, coordData);
             item->setData(Qt::UserRole + 1, QVariant::fromValue(int(RPT_WAY_POINT) + i));
         }

     }

     //end point
     if (end_point && end_point->size() > 0) {
         auto &pos = end_point->at(0).latlon_pos;
         item = new QListWidgetItem(QIcon(":/MainWindow/resources/e.png"), "", ui->routePlanList);
         item->setText(tr("End (%1, %2)").arg(QString::number(pos.CoordX_, 'f', 6)).arg(QString::number(pos.CoordY_, 'f', 6)));
         QVariant coordData = QVariant::fromValue(QPointF(pos.CoordX_, pos.CoordY_));
         item->setData(Qt::UserRole, coordData);
         item->setData(Qt::UserRole + 1, QVariant::fromValue(int(RPT_END_POINT)));
     }

 }

 void MainWindow::onRoutePlanListItemDoubleClicked(QListWidgetItem* item)
 {


 }

 void MainWindow::onRoutePlanListItemClicked(QListWidgetItem* item)
 {
     if (item) {
         QPointF coords = item->data(Qt::UserRole).value<QPointF>();
         qDebug() <<"RoutePlan " << "Lng: " << coords.x() << ", Lat: " << coords.y();
         RGeoPoint map_center_pos(coords.x(), coords.y());
         ui->mapWidget->setWorldCenter(&map_center_pos);
     }

 }


 void MainWindow::onRoutePlanListContextMenu(const QPoint &pos)
 {
     QListWidget* lw = ui->routePlanList;
     QListWidgetItem* item = lw->itemAt(pos);
     bool ok;

     if (item == nullptr)
         return;

     int index = item->data(Qt::UserRole + 1).toInt(&ok);
     if (!ok || index < 0)
         return;

     QMenu menu(this);
     QAction* action = menu.addAction(QIcon(":/MainWindow/resources/delete_16.png"), "&Remove");
     connect(action, &QAction::triggered, this, [this, index]() {
         auto itr = ui->mapWidget->getRoutePlanLayer();
          if (index == 0) {
              itr->removeRoutePoint(RPT_START_POINT);
          } else if (index == 1) {
              itr->removeRoutePoint(RPT_END_POINT);
          } else if (index >= 2) {
              itr->removeRoutePoint(RPT_WAY_POINT, index - 2);
          }

          startCaclPath();
//         DataCenter::instance()->m_routingCenter->removeDest(index);
//         DataCenter::instance()->m_routingCenter->removeAvoidArea(index);
//         DataCenter::instance()->m_routingCenter->removeAvoidLink(index);
     });
     menu.exec(lw->mapToGlobal(pos));
 }

 void MainWindow::onRouteResultChanged()
 {
     ui->routeResultList->clear();

     std::shared_ptr<RoutingControl> rc_ptr = MapEngineManage::GetInstance()->m_routingCenter_ptr;
     if (!rc_ptr)
         return;

     auto Route_path_ptr = rc_ptr->getRoutePathResult();
     if (!Route_path_ptr)
         return;

     QTextCodec *tc = QTextCodec::codecForName("UTF-8");
     QString routeInfo;
     for (size_t i = 0; i < Route_path_ptr->paths.size(); i++) {

         QListWidgetItem* item = new QListWidgetItem(ui->routeResultList);
         auto &path = Route_path_ptr->paths.at(i);
         routeInfo.clear();
         routeInfo.append(tc->toUnicode(QByteArray("路线 ")));
         routeInfo.append(QString::number(i+1));
         routeInfo.append(" : ");
         routeInfo.append(util::NumToKmM(path.length));
         routeInfo.append("; ");
         routeInfo.append(util::NumToHousSec(path.travel_time));
         routeInfo.append("; ");
         //routeInfo.append(QString::number(0));
         routeInfo.append(QString::number(path.traffic_light_num));
         routeInfo.append(tc->toUnicode(QByteArray("个红绿灯")));

         item->setText(routeInfo);
     }

     //for baidu route

     if (rc_ptr->getCompareWithBaidu())
     {
         QString b_routeInfo = rc_ptr->getRouteInfoBmap();

         if (!routeInfo.isEmpty())
         {
             QListWidgetItem* item = new QListWidgetItem(ui->routeResultList);

             routeInfo.clear();
             routeInfo.append(tc->toUnicode(QByteArray("百度： ")));
             routeInfo.append(b_routeInfo);
             item->setText(routeInfo);
         }
     }


     //for amap route
     if (rc_ptr->getCompareWithAmap())
     {
         QString a_routeInfo = rc_ptr->getRouteInfoAmap();
         //QString ruleStr;
         if (!routeInfo.isEmpty())
         {
             QListWidgetItem* item = new QListWidgetItem(ui->routeResultList);

             routeInfo.clear();
             routeInfo.append(tc->toUnicode(QByteArray("高德： ")));
             routeInfo.append(a_routeInfo);
             routeInfo += tc->toUnicode(QByteArray("个红绿灯"));
             item->setText(routeInfo);
         }
     }

 }

 void MainWindow::onRouteResultListChanged()
 {
     int8 row = ui->routeResultList->currentRow();
     QListWidgetItem *item = ui->routeResultList->item(row);
     if (!item->isSelected())
         return;

    int i = 0;

 }

 void MainWindow::onGoPushButtonClicked()
 {
     startSimulation();
 }

 void MainWindow::onSimulCancelClickButtonslot()
 {
     endSimulation();
 }

 void MainWindow::onPlaybackIntervalComboBoxCurrentIndexChangeslot(int index)
 {
    setSimulatorConfig();
 }


 void MainWindow::setSimulatorConfig()
 {
     SimulConfig config;
     config.position_noise_mean = ui->PosNoiseMeanlineEdit->text().isEmpty() ? 0.0 : ui->PosNoiseMeanlineEdit->text().toDouble();
     config.position_noise_stddev = ui->PosNoiseStdlineEdit->text().isEmpty() ? 0.0 : ui->PosNoiseStdlineEdit->text().toDouble();
     config.heading_noise_mean = ui->HeadingNoiseMeanlineEdit->text().isEmpty() ? 0.0 : ui->HeadingNoiseMeanlineEdit->text().toDouble();
     config.heading_noise_stddev = ui->HeadingNoiseStdlineEdit->text().isEmpty() ? 0.0 : ui->HeadingNoiseStdlineEdit->text().toDouble();
     config.sample_interval = ui->SampleIntervallineEdit->text().isEmpty() ? 1.0 : ui->SampleIntervallineEdit->text().toDouble();

     double play_inter = ui->PlaybackIntervalcomboBox->currentData().toDouble();
     if (play_inter > 0.0) {
         config.play_interval = 1.0/play_inter;
     } else {
         config.play_interval = 0.1;
     }

     QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);
     s.beginGroup("simulation");
     s.setValue("position_noise_mean", config.position_noise_mean);
     s.setValue("position_noise_stddev", config.position_noise_stddev);
     s.setValue("heading_noise_mean", config.heading_noise_mean);
     s.setValue("heading_noise_stddev", config.heading_noise_stddev);
     s.setValue("sample_interval", config.sample_interval);
     s.setValue("play_interval", ui->PlaybackIntervalcomboBox->currentIndex());
     s.endGroup();

     MapEngineManage *mm = MapEngineManage::GetInstance();
     if (mm->m_loction_control_ptr) {
         mm->m_loction_control_ptr->setSimulatorConfig(config);
     }
 }

 void MainWindow::onNavigationArrive()
 {
     QMessageBox::information(this, tr("Info"), tr("Arrive at the destination!"));

     MapEngineManage *mm = MapEngineManage::GetInstance();
     mm->setCurMapMode(MM_MAP_VIEW);
     mm->stopNavigation();
     mm->stopSimulation();
     hideTurnMarkView();
 }

 void MainWindow::onActionHomeTrigger()
 {
     qDebug() << "onActionHomeTrigger" ;
     RGeoPoint pos;
     MapEngineManage::GetInstance()->getFavoritePos(0, pos);
     ui->mapWidget->setWorldCenter(&pos);

 }

 void MainWindow::onActionCompanyTrigger()
 {
    qDebug() << "onActionCompanyTrigger" ;
    RGeoPoint pos;
    MapEngineManage::GetInstance()->getFavoritePos(1, pos);
    ui->mapWidget->setWorldCenter(&pos);
 }

 void MainWindow::loadFilesFromDirectory(const QString &directoryPath, QStringList &file_name_list, QStringList &file_path_list)
 {
     QDir directory(directoryPath);
     directory.setFilter(QDir::Files | QDir::NoDotAndDotDot); // 设置过滤器，例如只显示文件，不显示文件夹

     file_name_list = directory.entryList();
     for (const QString &fileName : file_name_list) {
         QString filePath = directory.filePath(fileName);
         file_path_list.push_back(filePath);
     }

 }

 void MainWindow::onLoadLogPushButtonClicked()
 {
     if (m_lastLogFilePath.isEmpty()) {
         m_lastLogFilePath = MapEngineManage::GetInstance()->m_loc_trace_log_path;
     }

     QString directory = QFileDialog::getExistingDirectory(
         this,                   // 父窗口指针
         "Open trace data Log",           // 对话框标题
         m_lastLogFilePath,       // 起始路径
         QFileDialog::ShowDirsOnly // 只显示文件夹
     );

     QStringList file_name_list;
     m_loc_log_file_list.clear();
     if (!directory.isEmpty()) {
         loadFilesFromDirectory(directory, file_name_list, m_loc_log_file_list);
     }

     if (file_name_list.size() == 0)
         return;

     //load location file list in combobox
     ui->LocComboBox->clear();
     for (int i = 0; i < file_name_list.size(); i++)
     {
         ui->LocComboBox->addItem(file_name_list[i]);
     }

 }

 void MainWindow::setTraceLogData()
 {
     MapEngineManage *mm = MapEngineManage::GetInstance();
     if (!mm && m_loc_log_file_list.size() > 0)
         return;

     int index = ui->LocComboBox->currentIndex();
     mm->m_loction_control_ptr->setTraceLogData(m_loc_log_file_list[index]);
 }

void MainWindow::setSpeedComboBoxLocationConfig(int idx)
{
    if (idx < 0)
        return;

    SimulConfig config;
    config.position_noise_mean = 0.0;
    config.position_noise_stddev = 0.0;
    config.heading_noise_mean = 0.0;
    config.heading_noise_stddev = 0.0;
    config.sample_interval = 1.0;
    config.play_interval = 1.0/pow(2, idx);


    MapEngineManage *mm = MapEngineManage::GetInstance();
    if (mm->m_loction_control_ptr) {
        mm->m_loction_control_ptr->setSimulatorConfig(config);
    }
}

void MainWindow::onSpeedComboBoxCurrentIndexChanged(int idx)
{
   setSpeedComboBoxLocationConfig(idx);
}

void MainWindow::onPlayPushButtonClicked()
{
    MapEngineManage *mm = MapEngineManage::GetInstance();
    if (!mm)
        return;

    if (ui->playPushButton->text() == "play")
    {
        int idx = ui->speedComboBox->currentIndex();
        setSpeedComboBoxLocationConfig(idx);
        setTraceLogData();

        mm->m_loction_control_ptr->pause();
        mm->m_loction_control_ptr->play();
        ui->playPushButton->setText("pause");
        ui->nextPushButton->setEnabled(false);
        ui->stopPushButton->setEnabled(true);
        ui->setCarPosPushButton->setEnabled(false);
        ui->LocComboBox->setEnabled(false);
    }
    else if (ui->playPushButton->text() == "pause")
    {
        mm->m_loction_control_ptr->pause();
        ui->playPushButton->setText("resume");
        ui->nextPushButton->setEnabled(true);
        //ui->stopPushButton->setEnabled(false);
        ui->setCarPosPushButton->setEnabled(true);
    }
    else if (ui->playPushButton->text() == "resume")
    {
        mm->m_loction_control_ptr->resume();
        ui->playPushButton->setText("pause");
        ui->stopPushButton->setEnabled(true);
    }
}

void MainWindow::onStopPushButtonClicked()
{
    MapEngineManage *mm = MapEngineManage::GetInstance();
    if (!mm)
        return;

    mm->m_loction_control_ptr->stop();
    ui->playPushButton->setText("play");
    ui->stopPushButton->setEnabled(false);
    ui->nextPushButton->setEnabled(true);
    ui->setCarPosPushButton->setEnabled(true);
    ui->LocComboBox->setEnabled(true);
    //((LocationLayer*)m_locationLayer)->clearPoint();
}

void MainWindow::onNextPushButtonClicked()
{
    MapEngineManage *mm = MapEngineManage::GetInstance();
    if (!mm)
        return;

    //mm->m_loction_control_ptr->next();
    mm->m_loction_control_ptr->pause();
    mm->m_loction_control_ptr->play();
}

void MainWindow::onShowDrCheckBoxClicked(bool checked)
{
    if (ui->mapWidget->getLocationLayer()) {
        ui->mapWidget->getLocationLayer()->showMatchLoc(checked);
    }
}

void MainWindow::onShowGpsCheckBoxClicked(bool checked)
{
    if (ui->mapWidget->getLocationLayer()) {
        ui->mapWidget->getLocationLayer()->showOriginalLoc(checked);
    }
}








